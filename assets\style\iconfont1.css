@font-face {
  font-family: "iconfont1"; /* Project id 4416205 */
  src: url('//at.alicdn.com/t/c/font_4416205_mmp7a7be7l.woff2?t=1709001993955') format('woff2'),
       url('//at.alicdn.com/t/c/font_4416205_mmp7a7be7l.woff?t=1709001993955') format('woff'),
       url('//at.alicdn.com/t/c/font_4416205_mmp7a7be7l.ttf?t=1709001993955') format('truetype');
}

.iconfont1 {
  font-family: "iconfont1" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-up-right:before {
  content: "\e62b";
}

.icon-jar1:before {
  content: "\e62a";
}

.icon-gou:before {
  content: "\e687";
}

.icon-calendar-range:before {
  content: "\e621";
}

.icon-a-asset1:before {
  content: "\e622";
}

.icon-calendar-heart:before {
  content: "\e623";
}

.icon-album:before {
  content: "\e624";
}

.icon-a-12:before {
  content: "\e625";
}

.icon-thermometer:before {
  content: "\e626";
}

.icon-asset:before {
  content: "\e627";
}

.icon-a-11:before {
  content: "\e628";
}

.icon-bottle:before {
  content: "\e629";
}

.icon-ticket:before {
  content: "\e613";
}

.icon-file-badge:before {
  content: "\e614";
}

.icon-timer:before {
  content: "\e615";
}

.icon-file-text:before {
  content: "\e616";
}

.icon-odour:before {
  content: "\e617";
}

.icon-a-1:before {
  content: "\e618";
}

.icon-a-Frame87:before {
  content: "\e619";
}

.icon-file-badge-2:before {
  content: "\e61a";
}

.icon-user-circle-2:before {
  content: "\e61b";
}

.icon-filter:before {
  content: "\e61c";
}

.icon-collection:before {
  content: "\e61d";
}

.icon-location1:before {
  content: "\e61e";
}

.icon-flask:before {
  content: "\e61f";
}

.icon-jar:before {
  content: "\e620";
}

.icon-a-13:before {
  content: "\e611";
}

.icon-safe:before {
  content: "\e612";
}

.icon-package:before {
  content: "\e60b";
}

.icon-close:before {
  content: "\e610";
}

.icon-truck:before {
  content: "\e605";
}

.icon-location:before {
  content: "\e606";
}

.icon-thumbup:before {
  content: "\e607";
}

.icon-chevrons-down:before {
  content: "\e608";
}

.icon-required:before {
  content: "\e609";
}

.icon-deny:before {
  content: "\e60c";
}

.icon-a-alarmclock:before {
  content: "\e60d";
}

.icon-plus:before {
  content: "\e60e";
}

.icon-checkbox:before {
  content: "\e60f";
}
