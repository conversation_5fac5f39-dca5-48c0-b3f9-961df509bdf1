/**
 * 常量枚举
 * 存放项目所以枚举字段 
 */

// 营销活动枚举
export const MARKETING_TYPE = {
	LIMIT_BUY: 1, // 限时购
	FLASH_SALE: 2, // 秒杀
	MEMBER_DISCOUNT: 4, // 会员折
	REDUCTION: 8, // 满减送
	COUPON : 16,// 优惠券
	GROUPON: 32, // 拼团
	BARGAIN: 64, // 砍价
	REDEEM: 128, // 积分兑换
	LOTTERY: 256, // 抽奖
	INTEGRAL: 512 ,// 积分抵扣
	REBATES: 1024, // 第二件半价
	ADD_ON_ITEM: 2048, // 加价购
	COMBINATION: 4096, // 组合购
}

// 订单类型枚举
export const ORDER_FLAG = {
	NORMAL : 0, // 普通订单
	GIFT: 1, // 礼品订单
	LIVE: 2, // 直播订单
	SUPPLIER: 4, // 供应商订单
	FIRST_BUY: 8, // 首次购买
	PICKUP: 16, // 自提订单
	CITY: 32,// 同城配送
	CITY_DELIVER: 128,// 同城自配送
	VIRTUAL: 512, // 虚拟商品
	ATTACHMENT: 1024, // 预留信息
	PACKET: 2048, // 裂变券
	VIRTUAL_NOT_REFUND: 4096 // 虚拟商品无售后
}

// 订单配送方式枚举
export const DELIVER_TYPE = {
	LOGISTICS : 2, // 快递发货
	CITY: 3, // 同城配送
	PICKUP: 4 // 自提订单
}

// 满减类型
export const REDUCTION_MODE = {
	LOOP : 2, // 循环满减
	STEP: 1, // 阶梯满减
}

// 支付方式枚举
export const PAY_CHANNEL = {
	WECHAT_JS_API: 11, // 微信浏览器支付
	WECHAT_H5: 12, // 微信H5支付
	WECHAT_APP: 13, // 微信APP支付
	WECHAT_APPLET: 14, // 微信小程序支付
	ALIPAY: 21 // 支付宝App支付
}

// 客户端类型枚举
export const PLATFORM = {
	WECHAT_APPLET: 2,
	ALI_APPLET: 4,
	H5: 8,
	APP: 16,
	WECHAT_H5: 32
}

// 授权模块
export const AUTHORIZE = {
  WECHAT_APPLET: 1,
  H5: 2,
  APP: 4,
  LIVE: 8,
  SUPPLIER: 16, // 供应商模块
  PICKUP_CENTER: 32, // 自提点模块
  APPLET_LIVE: 64, // 小程序直播
  WECHAT_H5: 128, // 微商城
  PARTNER: 1024 ,// 合伙人
  PC: 2048, // pc商城
	VIRTUALSUPPLIER: 4096, // 虚拟商品供应商模块
}

// 商品类型
export const PRODUCT_TYPE = {
	NORMAL: 0, // 普通商品
	VIRTUAL: 1 // 虚拟商品
}

// 虚拟商品有效期
export const VIRTUAL_SETTING = {
	VALIDITY: {
		INFINITY: 1, // 长期有效
		DAY: 2, // 指定天有效
		TIMES: 3 // 指定时间段有效
	},
	AFTER: {
		DISAGREE: 1, // 不支持退款
		AGREE: 2 // 支持申请退款
	},
	EXPIRATION: {
		REFUND: 1, // 过期自动退款
		USE: 2 // 过期自动核销
	}
}

export const ATTACHMENTS_TYPE = {
	TEXT: 1, // 文本
	PHONE: 2, // 手机
	DATE: 3, // 日期
	IMAGE: 4, // 图片
	CODE: 5, // 身份证
	NUMBER: 6 // 数字
}

export const OFFLINE_TYPE = {
	"WECHAT_TRANSFER":"微信转账",
	"ALI_TRANSFER":"支付宝转账",
	"BANK_CARD_TRANSFER":"银行卡转账",
}
export const STORE_TYPE = {
	"WINE_FACTORY":"酒厂托管",
	"SELF":"自己保管",
}

export const ORDER_STATUS={
	"UN_PAID":"待付款",
	"UN_SHIP":"待发货",
	"UN_CONFIRM":"待确认",
	"UN_ARCHIVE":"待封存",
	"UN_RECEIVED":"待收货",
	"FINISHED":"已完成",
	"CLOSED":"已关闭",
}