<template>
  <view class="container-root" :style="{ paddingTop: statusBarHeight + 'px' }">
    <custom-nav :data="{ barBgColor: 'transparent' }" />
    <image class="login-bg" :src="'static/applet/login-bg.png' | formatUrl"></image>
    <view class="header">
      <text v-if="isUseMobile" class="iconfont icon-arrow-left" @click="isUseMobile = false"></text>
    </view>
    <view class="inner-wrap">
      <image class="logo" :src="setting.webLogo || 'static/common/logo.png' | formatUrl" />
      <view class="logo-name">{{ setting.webName || '劲牌封藏' }}</view>
      <template v-if="!isUseMobile">
        <button
          class="btn user-info"
          :open-type="isAgree ? 'getPhoneNumber' : ''"
          @click="checkAgree"
          @getphonenumber="getphonenumber"
          :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }"
        >
          一键授权登录
        </button>
        <button class="btn btn-block btn-other" @click="isUseMobile = true">其他手机号登录</button>
      </template>

      <!-- 手机验证码登录 -->
      <view class="login-mobile" v-else>
        <view class="input-wrap">
          <input type="number" v-model="form.mobile" placeholder="请输入手机号" maxlength="11" />
        </view>
        <view class="input-wrap">
          <input type="number" v-model="form.verifyCode" placeholder="请输入验证码" />
          <text @click="handleSendCode">{{
            second > 0 ? `重新发送${second}s` : '发送验证码'
          }}</text>
        </view>

        <button
          @click="handlePhoneLogin"
          class="btn btn-primary"
          :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }"
        >
          登录
        </button>
      </view>
      <view class="agree" @click="isAgree = !isAgree">
        <text class="iconfont" :class="isAgree ? 'icon-radio-on' : 'icon-radio'"></text>
        已阅读并同意
        <text @click="handleOpen('/pages/user/agreement?type=1')">《用户协议》</text>
        和
        <text @click="handleOpen('/pages/user/agreement?type=2')">《隐私协议》</text>
      </view>
    </view>
  </view>
</template>

<script>
import { AuthLogin, GetMobileCode, MobileLogin } from '@/service/common'
import { saveUserInfo } from '@/controller/login'
import { mapState, mapMutations } from 'vuex'
import { bindUserId } from '@/service/jingBrand/customer'
import customNav from '@/components/customNav/customNav.vue'

export default {
  components: {
    customNav,
  },
  data() {
    return {
      isUseMobile: false, // 是否使用手机短信登录
      form: {
        mobile: '',
        verifyCode: '',
        identityType: 2,
      },
      isAgree: false,
      timer: null,
      second: 0,
      sending: false,
      backUrl: '',
      statusBarHeight: 20,
    }
  },
  computed: {
    ...mapState(['setting', 'theme']),
  },
  methods: {
    ...mapMutations(['SET_LOGIN_PAGE']),
    handleOpen(url) {
      uni.navigateTo({
        url,
      })
    },

    checkAgree() {
      if (!this.isAgree)
        uni.showToast({
          title: '请先同意隐私协议',
          icon: 'none',
        })
    },

    // 授权获取手机号
    getphonenumber(res) {
      uni.login({
        success: loginres => {
          if (loginres.code) {
            AuthLogin({
              code: loginres.code,
              identityType: 2,
              mobileCode: res.detail.code,
            }).then(resBind => {
              if (resBind.code !== '200') {
                return uni.showToast({
                  title: resBind.msg,
                  icon: 'none',
                  duration: 3000,
                })
              }
              saveUserInfo(resBind.data)
              bindUserId()
              const prevPage = getCurrentPages().at(-2)
              uni.reLaunch({
                url: (prevPage && prevPage.$page.fullPath) || '/pages/index/index',
              })
            })
          }
        },
      })
    },

    handleSendCode() {
      if (this.form.mobile.length !== 11) {
        return uni.showToast({
          title: '请填写11位手机号',
          icon: 'none',
        })
      }
      if (this.second > 0) return

      if (this.sending) return
      this.sending = true
      GetMobileCode({
        mobile: this.form.mobile,
      })
        .then(res => {
          this.sending = false
          uni.showToast({
            title: '验证码发送成功',
          })
          this.second = 60
          const timer = setInterval(() => {
            this.second--
            if (this.second <= 0) {
              clearInterval(timer)
            }
          }, 1000)
        })
        .catch(() => {
          this.sending = false
        })
    },

    handlePhoneLogin() {
      const params = this.form
      if (!this.isAgree) {
        return uni.showToast({
          title: '请先勾选同意协议',
          icon: 'none',
        })
      }
      if (params.mobile.length !== 11) {
        return uni.showToast({
          title: '请填写11位手机号',
          icon: 'none',
        })
      }
      if (!params.verifyCode.trim()) {
        return uni.showToast({
          title: '请填写短信验证码',
          icon: 'none',
        })
      }
      uni.login({
        success: loginres => {
          if (loginres.code) {
            params.code = loginres.code
            MobileLogin(params).then(res => {
              if (res.code !== '200') {
                return uni.showToast({
                  title: res.msg,
                  icon: 'none',
                  duration: 3000,
                })
              }
              saveUserInfo(res.data)
              bindUserId()
              uni.navigateBack()
            })
          }
        },
      })
    },
  },
  onLoad(options) {
    uni.getSystemInfo({
      success: res => {
        this.statusBarHeight = res.statusBarHeight
      },
    })
  },

  onUnload() {
    this.SET_LOGIN_PAGE(false)
  },
}
</script>

<style lang="scss" scoped>
.container-root {
  background-color: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.header {
  display: flex;
  position: relative;
  width: 100%;
  height: 88rpx;
  z-index: 2;
  text {
    padding: 26rpx;
    font-size: 36rpx;
  }
}
.inner-wrap {
  display: flex;
  flex: 1;
  align-items: center;
  flex-direction: column;
  min-height: 1060rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin: 80rpx 0 24rpx;
  border-radius: 50%;
}

.user-info {
  margin-top: 80rpx;
  width: 600rpx;
  height: 88rpx;
  font-size: 30rpx;
  border-radius: 44rpx;
  background-color: #07c160;
  color: #fff;
  line-height: 88rpx;

  &::after {
    display: none;
  }
}

.btn-other {
  margin-top: 32rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  &::after {
    border-radius: 88rpx;
  }
}

.agree {
  display: flex;
  align-items: center;
  margin-top: 40rpx;
  line-height: 32rpx;
  font-size: 24rpx;

  text {
    color: #6f84a3;
    &.iconfont {
      font-size: 32rpx;
      margin-right: 8rpx;
      color: #bfbfbf;
      &.icon-radio-on {
        color: #07c160;
      }
    }
  }
}

.logo-name {
  padding: 0 32rpx;
  font-size: 34rpx;
  line-height: 48rpx;
}

.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 400rpx;
  z-index: 1;
}

.login-mobile {
  width: 600rpx;
  margin: 48rpx 0 0;

  .input-wrap {
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    margin-top: 32rpx;
    border-radius: 44rpx;
    background-color: #f5f6f7;

    input {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
    }

    text {
      margin-left: 24rpx;
      color: #626262;
    }
  }

  .btn {
    margin-top: 56rpx;
    border-radius: 44rpx;
    line-height: 88rpx;
  }
}
</style>
