import request from '@/config/request'

// 获取小程序初始化配置
export const GetInitConfig = data => request({ url: 'wine/config/mini/getInitConfig', data })

// 微信小程序code静默登录
export const WechatLogin = data =>
  request({ url: 'wine/user/mini/wxDefaultLogin', data, method: 'post' })

// 获取自定义数据详情
export const GetDecorate = data =>
  request({ url: 'wine/decorate/mini/detail', data, method: 'post' })
// 解析小程序码scene
export const GetCodePage = data => request({ url: 'wine/wechat/code/getRealPage', data })

// 手机号授权登录
export const AuthLogin = data =>
  request({ url: 'wine/user/mini/authLogin', data, method: 'post', intercept: false })
// 手机号登录
export const MobileLogin = data =>
  request({ url: 'wine/user/mini/mobileLogin', data, method: 'post', intercept: false })
// 获取手机验证码
export const GetMobileCode = data =>
  request({ url: 'wine/user/mini/mobile/sendForLogin', data, method: 'post' })

// 获取登录用户信息
export const GetUserInfo = data => request({ url: 'wine/user/mini/userInfo', data })
// 保存用户头像和昵称
export const SaveUserData = data =>
  request({ url: 'wine/user/mini/saveUserData', data, method: 'post' })
// 用户签到
export const UserSignIn = data =>
  request({ url: 'fansClub/signIn/mini/create', data, method: 'post' })
// 获取签到状态
export const GetSignInStatus = () => request({ url: 'fansClub/signIn/mini/getSignInFlag' })

// 获取隐私协议
export const GetAgreement = type => request({ url: `wine/user/mini/getAgreement?type=${type}` })

// 获取OBS上传临时凭证
export const GetTemporaryCredentials = data =>
  request({ url: 'wine/nfs/getTemporaryCredentials', data })

// 收货地址管理
export const GetAddressList = data =>
  request({ url: 'jingBrand/addressController/pageList', data, method: 'post' })
export const GetAddressDetail = id =>
  request({ url: `jingBrand/addressController/getInfo?id=${id}`, data: {}, method: 'post' })
export const DeleteAddress = data =>
  request({ url: 'jingBrand/addressController/delete', data, method: 'post' })
export const SaveWechatAddress = data =>
  request({ url: 'wine/deliveryAddress/mini/createWxDeliveryAddress', data, method: 'post' })
export const SaveAddress = data =>
  request({
    url: `jingBrand/addressController/${data.id ? 'edit' : 'add'}`,
    data,
    method: 'post',
  })

// 获取省市区街道下级
export const GetRegions = data => request({ url: 'wine/district/list', data, showLoading: false })

// 获取自定义数据详情
export const GetDiyInfo = (url, data, method) => request({ url, data, method })

// 获取退款原因列表
export const GetRefundReasonList = data =>
  request({ url: 'wine/refundReasonConfig/mini/list', data })
// 获取商品信息
export const GetProductInfo = id => request({ url: `wine/product/mini/detail?id=${id}` })
// 物流查询
export const logisticsQuery = data => request({ url: 'wine/logistics/query', data, method: 'post' })

// 同意隐私协议
export const AgreeProtocol = () => request({ url: 'wine/basicSetting/agreeProtocol' })

// 检查是否需要弹出隐私协议
export const GetProtocolCheck = () => request({ url: 'wine/basicSetting/getProtocolCheck' })

// 获取用户授权信息
export const GetUserAuthorize = () => request({ url: 'wine/user/mini/getUserAuthorize' })

// 头像开关设置
export const SetIconCtn = data => request({ url: 'wine/user/mini/iconCtn', data })

// 昵称开关设置
export const SetNickNameCtn = data => request({ url: 'wine/user/mini/nickNameCtn', data })

// 注销账号
export const CancelAccount = () => request({ url: 'jingBrand/custom/cancel', intercept: false })

// 获取用户信息
export const GetUserInfoList = data => request({ url: 'wine/user/mini/userInfo', data })

// 获取授权说明信息
export const GetAuthInfo = data => request({ url: 'wine/user/mini/authInfo', data })
