import request from '@/config/request'

// 创建赠酒订单
export const createSendWineOrder = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/transferWine',
    data,
    method: 'post',
  })

// 创建订单前校验请求参数
export const checkCreateSendWineOrder = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/transferWineCheck',
    data,
    method: 'post',
  })

// 根据id获取赠酒详情
export const getSendWineDetail = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/transferInfo',
    data,
  })

// 获取手机验证码
export const getSMSCode = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/sendGiftWineSmsCode',
    data,
  })

// 领取赠酒
export const receiveSendWine = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/getGiftWine',
    data,
    method: 'post',
  })

// 获取转赠列表
export const getSendList = data =>
  request({
    url: 'jingBrand/mini/personal/transferWineLog',
    data,
  })

export const cancelSend = data =>
  request({
    url: 'jingBrand/mini/personal/updateRevokeTime',
    data,
  })
