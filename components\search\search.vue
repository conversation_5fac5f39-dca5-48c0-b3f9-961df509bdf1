<template>
  <view class="search-wrap" :style="paddingStyle">
    <view class="search-input" :style="radiusStyle">
      <text class="iconfont icon-search" :style="radiusStyle == 'border-radius:8rpx' ? 'left:48rpx;top:30rpx' : ''" />
      <input type="text" :style="inputStyle" :placeholder="placeholder" v-model.trim="keyword" @confirm="handleSearch" placeholder-style="color:#BCBCBC" />
    </view>
    <text class="iconfont icon-close" :style="radiusStyle == 'border-radius:8rpx' ? 'top:20rpx' : ''" v-show="keyword" @click="handleClear"></text>
  </view>
</template>

<script>
export default {
  props: {
    paddingStyle: {
      type: String,
      default: ''
    },
    radiusStyle: {
      type: String,
      default: ''
    },
    inputStyle: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入搜索关键词'
    },
    value: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      keyword: ''
    }
  },
  computed: {},
  watch: {
    keyword(value) {
      this.$emit('input', value)
    }
  },
  methods: {
    handleClear() {
      this.keyword = ''
      this.handleSearch()
    },
    handleSearch() {
      uni.hideKeyboard()
      this.$emit('search', this.keyword)
    }
  },
  mounted() {
    this.keyword = this.value
  }
}
</script>

<style lang="scss" scoped>
.search-wrap {
  background-color: #fff;
  padding: 16rpx 24rpx;
  position: relative;
  .search-input {
    background-color: #f4f5f6;
    border-radius: 32rpx;

    .iconfont {
      position: absolute;
      left: 40rpx;
      top: 26rpx;
      font-size: 40rpx;
      color: #bcbcbc;
    }

    input {
      padding: 0 68rpx;
      height: 64rpx;
      line-height: 64rpx;
    }
  }

  .icon-close {
    position: absolute;
    right: 32rpx;
    top: 16rpx;
    font-size: 32rpx;
    color: #bcbcbc;
    padding: 16rpx;
    z-index: 2;
  }
}
</style>
