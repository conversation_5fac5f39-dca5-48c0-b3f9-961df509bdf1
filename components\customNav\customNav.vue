<template>
  <view
    class="header"
    v-if="data.enableBar !== false"
    :style="{ paddingTop: statusBarHeight + 'px', backgroundColor: data.barBgColor || '#fff' }"
  >
    <slot name="navBack">
      <view class="nav-back" @click="handleBack" v-if="data.showBack !== false">
        <view
          class="iconfont icon-arrow-left"
          :style="{ color: data.barColor || (data.barType ? '#fff' : '#000') }"
        ></view>
      </view>
    </slot>
    <view
      class="title"
      v-if="data.title && data.showTitle !== false"
      :style="{ color: data.barColor || (data.barType ? '#fff' : '#000') }"
      >{{ data.title }}</view
    >
    <image
      class="nav-logo"
      v-if="data.showLogo && data.logo"
      :src="data.logo | formatUrl(400)"
      mode="heightFix"
    />
    <image
      class="bg"
      v-if="data.barBgImage"
      :src="data.barBgImage | formatUrl(1080)"
      mode="widthFix"
    />
  </view>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      statusBarHeight: 20,
    }
  },
  created() {
    uni.getSystemInfo({
      success: res => {
        this.statusBarHeight = res.statusBarHeight
        // #ifndef H5
        this.$emit('init', res.statusBarHeight * 2 + 88)
        // #endif
        // #ifdef H5
        this.$emit('init', res.statusBarHeight * 2)
        // #endif
      },
    })
  },
  mounted() {
    uni.setNavigationBarColor({
      frontColor: this.data.barType ? '#ffffff' : '#000000',
      backgroundColor: '#000',
    })
  },

  methods: {
    handleBack() {
      if (getCurrentPages().length < 2) {
        uni.switchTab({
          url: '/pages/index/index',
        })
      } else {
        uni.navigateBack()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  z-index: 6000;
  transition: all 0.3s;

  .title {
    position: relative;
    z-index: 2;
    padding: 0 32rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    font-size: 34rpx;
    color: #000;
  }
  .nav-back {
    position: absolute;
    bottom: 0;
    z-index: 5;
    left: 16rpx;
    width: 64rpx;
    height: 88rpx;
    text-align: center;

    .iconfont {
      line-height: 88rpx;
      font-size: 40rpx;
      color: #212121;
    }
  }

  .nav-logo {
    position: absolute;
    bottom: 0;
    left: 72rpx;
    height: 88rpx;
    z-index: 2;
  }

  .bg {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1;
  }
}
</style>
