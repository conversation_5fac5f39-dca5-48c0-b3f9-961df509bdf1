<template>
  <div>
    <!--    <image mode="widthFix" :src="cover" style="width: 100%" />-->
    <!--    <p class="tips">封面</p>-->
    <div
      :style="{
        position: 'relative',
        // 宽度固定750rpx
        width: `${width}rpx`,
        height: `${height}rpx`,
      }"
    >
      <image
        :src="template"
        style="position: absolute; left: 0; top: 0; width: 100%; height: 100%;"
      />

      <span
        v-for="field in visibleFields"
        :key="field.id"
        :style="{
          position: 'absolute',
          left: `${field.left * radio}rpx`,
          top: `${field.top * (height / value.height)}rpx`,
          fontSize: `${field.fontSize * radio}rpx`,
          fontWeight: field.bold ? 'bold' : 'normal',
          color: field.fill,
          userSelect: 'none',
          whiteSpace: 'nowrap',
        }"
      >
        {{ field.text }}
      </span>
    </div>
    <p class="tips">证书</p>
  </div>
</template>
<script>
import { OSS_PREFIX } from '@/config/system'

export default {
  name: 'Cert',
  props: {
    value: {
      type: Object,
      require: true,
    },
    canvasWidth: {
      type: Number,
      default: 750,
    },
  },
  computed: {
    cover() {
      return OSS_PREFIX + this.value.cover
    },
    template() {
      return OSS_PREFIX + this.value.template
    },
    width() {
      return this.canvasWidth || 750
    },
    height() {
      return (this.value.height * 750) / this.value.width
    },
    radio() {
      return this.width / this.value.width
    },
    visibleFields() {
      return this.value.fields.filter(field => field.visible)
    },
  },
}
</script>

<style lang="scss" scoped>
.tips {
  margin: 16rpx auto 32rpx;
  text-align: center;
  color: #999;
}
</style>
