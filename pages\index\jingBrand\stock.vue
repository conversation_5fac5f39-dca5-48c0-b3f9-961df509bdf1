<template>
  <div class="jing-stock">
    <custom-nav
      :data="{ barBgColor: '#f4f5f6', title: '我的酒库' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div style="margin: 24rpx auto">
        <p v-if="customerName" style="margin-bottom: 16rpx; font-size: 36rpx; font-weight: 600">
          尊敬的 <span class="text-brown">{{ customerName }}</span>
        </p>
        <p class="gray" style="font-size: 24rpx">您的酒库当前藏酒如下：</p>
      </div>
      <template v-if="total > 0">
        <header class="header">
          <div>
            <p class="text">藏酒(坛)</p>
            <p class="num">{{ total }}</p>
          </div>
          <div>
            <p class="text">剩余容量(L)</p>
            <p class="num">{{ remainingCapacity | ml2L }}L</p>
          </div>
        </header>

        <div class="list">
          <p class="title">藏酒</p>
          <div class="divider" />

          <div v-for="(item, index) in list" :key="index" class="item">
            <div class="meta" @click="goDetail(item)">
              <div class="left">
                <p class="subtitle">
                  {{ item.name }}
                </p>
                <p>
                  数量：<span class="text-brown">{{ item.assetsAltarNum }}坛</span>
                </p>
                <p>
                  剩余容量：<span class="text-brown">
                    {{ item.sumOverCapacity | ml2L }}L / {{ item.sumTotalCapacity | ml2L }}L
                  </span>
                </p>
                <p>
                  藏酒类型：<span class="text-brown">{{ item.depositTypeStr || '-' }}</span>
                </p>
              </div>
              <image :src="item.imgList[0] | formatUrl(160)" class="cover" mode="aspectFill" />
            </div>

            <div class="actions">
              <span class="btn" @click="sendWine(item)">赠酒</span>
              <span class="btn btn-primary" @click="takeWine(item)">取酒</span>
            </div>

            <div class="divider" />
          </div>
        </div>
      </template>

      <template v-else>
        <div class="empty">
          <img class="img" :src="'hishop/upload/QNGtoFmiFJYuEd3VCl_UH.png' | formatUrl" alt="" />
          <p class="title">您的酒库暂无藏酒</p>
          <!--          <span class="btn">去藏酒</span>-->
        </div>
      </template>

      <footer v-if="promoter.id" class="footer">
        <img :src="'hishop/upload/fAAjAtfG7u2Y0KJnq4EDt.png' | formatUrl" class="icon" alt="" />
        <div>
          <p class="title">我的封藏管家</p>
          <p class="slogan">劲牌封藏，服务好您的每一坛好酒</p>
        </div>
        <span class="btn" @click="$refs.promoter.open()">立即联系</span>
      </footer>
    </div>

    <promoter ref="promoter" />

    <send-to ref="sendTo" :goods-id="goodsId" :customer-id="customerId" />

    <take ref="take" :goods-id="goodsId" :customer-id="customerId" />
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav'
import { getCustomerName, getStockList, checkExtraction } from '@/service/jingBrand/stock'
import { OSS_PREFIX } from '@/config/system'
import SendTo from './components/SendTo.vue'
import Take from './components/Take.vue'
import Promoter from './components/Promoter.vue'
import { numAdd } from '@/common/utils'
import { mapState } from 'vuex'

export default {
  components: {
    CustomNav,
    SendTo,
    Take,
    Promoter,
  },
  data() {
    return {
      OSS_PREFIX,
      statusBarHeight: 168,
      list: [],
      total: 0,
      promoter: {},
      goodsId: null,
      goodsName: '',
      customerId: null,
      cover: '',
      availableCapacity: 0,
      subpackageInfoList: [],
      customerName: '',
    }
  },
  computed: {
    ...mapState(['userInfo']),
    remainingCapacity() {
      return this.list.reduce((prev, current) => numAdd(prev, current.sumOverCapacity), 0)
    },
  },
  methods: {
    initPage() {
      wx.requestSubscribeMessage({
        // 藏酒订单提醒
        tmplIds: ['vxFS0XrQEij0xkHH6AUL3RSwKfL18_YCCjG4HADXk5Q'],
        success() {
          console.log('订阅成功')
        },
        fail(e) {
          console.log('订阅失败', e)
        },
      })
      return Promise.all([this.initStockList(), this.initMyPromoter(), this.initCustomerName()])
    },

    async initStockList() {
      const { data, code } = await getStockList()
      if (code === '200') {
        this.list = data.list
        this.total = data.num
      }
    },

    async initMyPromoter() {
      this.promoter = await this.$refs.promoter.initMyPromoter()
    },

    async initCustomerName() {
      const { data } = await getCustomerName()
      this.customerName = data
    },

    goDetail(item) {
      uni.navigateTo({
        url: `/jingBrand/stock/detail?id=${item.goodsId}&customerId=${item.customId}`,
      })
    },

    sendWine(item) {
      this.goodsId = item.goodsId
      this.customerId = item.customId
      setTimeout(() => {
        this.$refs.sendTo.open()
      })
    },

    async takeWine(item) {
      this.goodsId = item.goodsId
      this.customerId = item.customId
      const res = await checkExtraction(item.goodsId)
      if (!res.data) {
        return
      }
      setTimeout(() => {
        this.$refs.take.open()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.mt-gap {
  margin-top: 24rpx;
}

.black {
  color: #262626;
}

.gray {
  color: #666;
}

.text-brown {
  color: #bc9173;
}

.container {
  padding: 24rpx;
  padding-bottom: 196rpx;
}

.header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 160rpx;
  text-align: center;
  border-radius: 16rpx;
  background: url('https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/pLUoj42GXXqHhJQrrJEnk.png')
    no-repeat center / 100% 100%;
  overflow: hidden;

  .text {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.85);
    opacity: 0.85;
  }

  .num {
    margin-top: 8rpx;
    font-size: 48rpx;
    font-weight: 600;
    color: #f8f4f1;
  }
}

.list {
  margin-top: 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background-color: #fff;

  .title {
    padding-left: 56rpx;
    font-size: 32rpx;
    line-height: 48rpx;
    font-weight: 500;
    background: url('https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/IWAk8HBXQuCVqdHoc5XID.png')
      no-repeat left center / 40rpx 40rpx;
  }

  .meta {
    display: flex;
    justify-content: space-between;
    gap: 16rpx;
    margin: 24rpx auto;
    padding: 24rpx;
    border-radius: 4rpx;
    background-color: #f9f9f9;
  }

  .subtitle {
    font-size: 28rpx;
    line-height: 40rpx;
    font-weight: 500;
    color: #262626;
  }

  .left {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 16rpx;
    font-size: 28rpx;
    color: #ccc;
    overflow: hidden;
  }

  .cover {
    width: 160rpx;
    min-width: 160rpx;
    height: 160rpx;
    border-radius: 4rpx;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 16rpx;
  }

  .btn {
    padding: 16rpx 40rpx;
    line-height: 40rpx;
    border: 2rpx solid #eeeeee;
    border-radius: 36rpx;
    background-color: #fff;
  }

  .btn-primary {
    color: #0b7d83;
    border-color: #0b7d83;
  }
}

.footer {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: calc(env(safe-area-inset-bottom) + 98rpx + 24rpx);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx;
  background: linear-gradient(180deg, #ecf7f7 0%, #ffffff 100%);
  box-shadow: 0 4rpx 16rpx 0 rgba(0, 0, 0, 0.15);
  border-radius: 24rpx;
  border: 2rpx solid #fff;

  .icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }

  .title {
    color: #262626;
  }

  .slogan {
    font-size: 24rpx;
    color: #999;
  }

  .btn {
    padding: 12rpx 24rpx;
    font-size: 24rpx;
    color: #fff;
    line-height: 32rpx;
    background: #298487;
    border-radius: 28rpx;
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 280rpx;
    height: 280rpx;
  }

  .title {
    margin-top: 0;
    color: #999;
  }

  .btn {
    padding: 16rpx 32rpx;
    margin-top: 24rpx;
    line-height: 1;
    color: #fff;
    background-color: #0b7d83;
    border-radius: 36rpx;
  }
}
</style>
