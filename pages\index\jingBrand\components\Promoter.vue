<template>
  <uni-popup ref="dialog" type="dialog" is-mask-click class="dialog">
    <slot>
      <div class="content">
        <img :src="promoter.avatarImg | formatUrl" class="avatar" mode="aspectFill" />

        <div class="meta">
          <div class="left">
            <p class="name">{{ promoter.name || '--' }}</p>
            <p v-if="promoter.phone" class="phone" @click="cellPhone">
              <span>{{ promoter.phone || '--' }}</span>
              <img
                src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/T8MchNso6Gzry7OPYvVgO.png?x-image-process=image/resize,m_lfit,w_40"
              />
            </p>
          </div>

          <img
            v-if="promoter.qrCode"
            :src="promoter.qrCode | formatUrl"
            class="qrcode"
            show-menu-by-longpress
          />
        </div>

        <div class="bottom">
          <img :src="`${OSS_PREFIX}hishop/upload/PBkp2aTYbUg1x5yQ8U9RQ.png`" class="icon" />
          <span>专属封藏管家</span>
        </div>
      </div>

      <img
        :src="
          `${OSS_PREFIX}hishop/upload/k__9z1xtGIbK9vfVvmKpC.png?x-image-process=image/resize,m_lfit,w_40`
        "
        class="close"
        @click="$refs.dialog.close()"
      />
    </slot>
  </uni-popup>
</template>

<script>
import { getMyPromoter } from '@/service/jingBrand/promoter'
import { OSS_PREFIX } from '@/config/system'

export default {
  data() {
    return {
      OSS_PREFIX,
      promoter: {},
    }
  },
  methods: {
    open() {
      if (!this.promoter || !this.promoter.id) {
        this.initMyPromoter()
      }
      this.$refs.dialog.open()
    },
    async initMyPromoter() {
      const { code, data } = await getMyPromoter()
      if (code === '200') {
        this.promoter = data
        // this.$emit('init', data)
        return data
      }
    },

    cellPhone() {
      wx.makePhoneCall({ phoneNumber: this.promoter.phone })
    },
  },
}
</script>

<style scoped lang="scss">
.avatar {
  width: 100%;
}

.content {
  width: 480rpx;
  border-radius: 24rpx;
  background-color: #fff;
  overflow: hidden;
}

.close {
  display: block;
  width: 80rpx;
  height: 80rpx;
  margin: 80rpx auto 0;
}

.meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  color: #262626;

  .left {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
  }

  .name {
    font-size: 32rpx;
    font-weight: 500;
  }

  .phone {
    font-size: 28rpx;
    color: #666;

    img {
      width: 26rpx;
      height: 26rpx;
      margin-left: 16rpx;
      vertical-align: middle;
    }
  }
}

.qrcode {
  width: 120rpx;
  height: 120rpx;
  background: #fff;
  box-shadow: 0 0 8rpx 0 rgba(0, 0, 0, 0.15);
  border-radius: 8rpx;
}

.bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8rpx;
  padding: 20px 0;
  line-height: 1;
  font-size: 24rpx;
  background-color: #f8f4f1;

  .icon {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>
