<template>
	<view :style="{ height: dataset.height*2 + 'rpx', backgroundColor: dataset.bgColor, borderTopLeftRadius: dataset.radiusTop? '16rpx': 0, borderTopRightRadius: dataset.radiusTop? '16rpx': 0, borderBottomLeftRadius: dataset.radiusBottom? '16rpx': 0, borderBottomRightRadius: dataset.radiusBottom? '16rpx': 0  }">
	</view>
</template>

<script>
	export default {
		props: {
			dataset: {
				type: Object,
				default: () => {}
			}
		}
	}
</script>
