<template>
  <view class="counter-root">
    <text
      class="iconfont icon-steper-minus"
      :class="{ disibale: number <= min }"
      @click="handleSub"
    />
    <input
      :value="number"
      type="number"
      :cursor-spacing="40"
      :always-embed="true"
      :disabled="inputDisabled"
      @input="handleInput"
      @blur="handleBlur"
      @focus="handleFocus"
    />
    <text
      class="iconfont icon-steper-plus"
      :class="{ disibale: number >= max }"
      @click="handleAdd"
    />
  </view>
</template>

<script>
import { isNumber } from '@/uni_modules/uni-forms/components/uni-forms/utils'

export default {
  props: {
    max: Number,
    min: {
      type: Number,
      default: 1,
    },
    inputDisabled: {
      type: Boolean,
      default: false,
    },
    value: [Number, String],
  },
  data() {
    return {
      // min: 1,
      number: this.value,
    }
  },
  watch: {
    number(value) {
      this.$emit('update:value', value)
      this.$emit('change', value)
    },
    value(value) {
      this.number = value
    },
  },
  methods: {
    handleAdd() {
      if (isNumber(this.max) && parseInt(this.number) + 1 > this.max) {
        return
      }
      this.number = parseInt(this.number) + 1
    },
    handleSub() {
      if (isNumber(this.min) && parseInt(this.number) - 1 < this.min) {
        return
      }
      this.number = parseInt(this.number) - 1
    },
    handleInput(e) {
      let value = ''
      if (e.detail.value === '') {
        value = ''
      } else {
        value = parseInt(e.detail.value) || 0
        if (isNumber(this.max) && value > this.max) {
          value = this.max
        }
        if (isNumber(this.min) && value < this.min) {
          value = this.min
        }
      }

      const reg = e.detail.value.match(/^(-)?\d*/g)
      if (reg) {
        value = reg[0]
      }
      this.number = parseInt(value)
      return value
    },
    handleBlur(e) {
      let value = parseInt(e.detail.value) || 0
      if (isNumber(this.max) && value > this.max) {
        value = this.max
      }
      if (isNumber(this.min) && value < this.min) {
        value = this.min
      }
      this.number = parseInt(value)
      return value
    },
    handleFocus() {
      this.$emit('focus')
    },
  },
}
</script>

<style lang="scss" scoped>
.counter-root {
  display: flex;
  .iconfont {
    width: 48rpx;
    height: 48rpx;
    font-size: 48rpx;
    color: $colorgray;
    display: inline-block;
    vertical-align: middle;
    border-radius: 8rpx;
  }

  input {
    width: 72rpx;
    height: 48rpx;
    border-radius: 8rpx;
    line-height: 48rpx;
    font-size: $size;
    color: $colortext;
    text-align: center;
    background-color: $colorbackground;
  }

  .disibale {
    color: $colorgraylight;
  }
}
</style>
