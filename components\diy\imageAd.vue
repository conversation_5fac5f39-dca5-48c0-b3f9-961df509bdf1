<template>
  <view
    class="outer-wrap"
    v-if="obsUrl"
    :style="{
      backgroundColor: dataset.bgColor,
      backgroundImage: dataset.bgImage ? 'url(' + obsUrl + dataset.bgImage + ')' : '',
      padding: dataset.verticalMarginTop * 2 + 'rpx 0 ' + dataset.verticalMarginBottom * 2 + 'rpx',
      borderRadius: dataset.moduleRadius ? '16rpx' : 0,
    }"
  >
    <view
      class="swiper-box"
      v-if="dataset.showType === 2"
      :class="{ 'img-shadow': dataset.imgStyle, 'img-fillet': dataset.imgRadius }"
      :style="{
        marginLeft: dataset.pageMargin * 2 + 'rpx',
        marginRight: dataset.pageMargin * 2 + 'rpx',
      }"
    >
      <swiper
        circular
        autoplay
        :indicator-dots="dataset.indicatorStyle === 1"
        indicator-color="#ebedf0"
        indicator-active-color="#fb1438"
        @change="changeSwiper"
        :style="{ height: (dataset.heightType ? dataset.height * 2 : 750) + 'rpx' }"
      >
        <swiper-item
          v-for="(item, index) in dataset.images"
          :key="index"
          :label="index + 1"
          @click="skip(item.link)"
        >
          <image
            lazy-load
            :src="item.imgSrc | formatUrl(750)"
            :mode="dataset.fill ? 'aspectFill' : 'aspectFit'"
          />
        </swiper-item>
      </swiper>
      <view class="dots-style2" v-if="dataset.indicatorStyle === 2">
        <text
          v-for="(item, index) in dataset.images"
          :key="index"
          :class="{ active: current === index }"
        ></text>
      </view>
      <view class="dots-style3" v-else-if="dataset.indicatorStyle === 3">
        <text>{{ current + 1 }}</text>
        / {{ dataset.images.length }}
      </view>
      <view class="dots-style4" v-else-if="dataset.indicatorStyle === 4"
        >{{ current + 1 }} / {{ dataset.images.length }}</view
      >
    </view>

    <view class="wrap" v-else>
      <view class="nav-box">
        <view
          class="image-nav"
          :class="[
            `style${dataset.showType}`,
            { 'img-shadow': dataset.imgStyle, 'img-fillet': dataset.imgRadius },
          ]"
          :style="{ margin: `0 ${dataset.pageMargin * 2}rpx` }"
        >
          <view
            class="item"
            v-for="(item, index) in dataset.images"
            :key="index"
            @click="skip(item.link)"
            :style="{
              marginTop:
                dataset.showType === 1 || dataset.showType === 6
                  ? dataset.imgMargin * 2 + 'rpx'
                  : 0,
              marginLeft:
                dataset.showType !== 1 && dataset.showType !== 6
                  ? dataset.imgMargin * 2 + 'rpx'
                  : 0,
            }"
          >
            <image
              lazy-load
              v-if="dataset.showType === 1 || dataset.showType === 6"
              :src="item.imgSrc | formatUrl(750)"
              :style="dataset.showType === 6 ? 'width:100%' : ''"
              mode="widthFix"
            />
            <image
              lazy-load
              v-else
              :src="item.imgSrc | formatUrl(750)"
              :style="{
                width: width + 'rpx',
                height: (dataset.heightType ? dataset.height * 2 : width) + 'rpx',
              }"
              mode="aspectFill"
            />
            <view @click.stop class="map-area" v-if="dataset.showType === 6">
              <view
                class="rect"
                v-for="rect in item.rects"
                :key="rect.id"
                @click="skip(rect.link)"
                :style="{
                  width: convertSize(rect.width) + 'rpx',
                  height: convertSize(rect.height) + 'rpx',
                  left: convertSize(rect.left) + 'rpx',
                  top: convertSize(rect.top) + 'rpx',
                }"
              ></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    dataset: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      current: 0,
    }
  },
  computed: {
    ...mapState(['obsUrl']),
    width: function() {
      const totalWidth = 750 - this.dataset.pageMargin * 2 * 2
      let count = this.dataset.lineCount
      if (this.dataset.showType === 3) {
        count = 2
      } else if (this.dataset.showType === 4) {
        count = 3
      }
      return parseInt(totalWidth / count + totalWidth / count / count)
    },
  },
  methods: {
    changeSwiper(e) {
      this.current = e.detail.current
    },
    convertSize(size) {
      return ((750 - this.dataset.pageMargin * 4) * size) / 500
    },
    skip(link) {
      console.log('link', link)
      if (!link) {
        return
      }
      this.$navigateTo({
        ...link,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.outer-wrap {
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: center top;
}

.wrap {
  overflow: hidden;
  .nav-box {
    overflow-x: auto;
    padding-bottom: 32rpx;
    margin-bottom: -32rpx;
  }
}

.image-nav {
  white-space: nowrap;

  .item {
    position: relative;
    display: inline-block;
    vertical-align: top;

    image {
      width: 100%;
      display: block;
      height: auto;
    }

    &:first-child {
      margin: 0 !important;
    }
  }

  &.style1,
  &.style6 {
    .item {
      display: block;
    }
  }

  &.img-shadow {
    padding: 20rpx 0;

    .item {
      box-shadow: 0 8rpx 20rpx rgba(47, 54, 70, 0.2);
    }
  }

  &.img-fillet {
    .item {
      border-radius: 16rpx;
      overflow: hidden;
      image {
        border-radius: 16rpx;
      }
    }
  }
}

.swiper-box {
  position: relative;
  overflow: hidden;

  &.img-fillet {
    border-radius: 16rpx;
    image {
      border-radius: 16rpx;
    }
  }

  &.img-shadow {
    margin: 20rpx 0;
    box-shadow: 0 8rpx 20rpx rgba(47, 54, 70, 0.2);
  }

  image {
    width: 100%;
    height: 100%;
  }

  .dots-style2 {
    position: absolute;
    bottom: 12rpx;
    left: 0;
    right: 0;
    text-align: center;

    text {
      display: inline-block;
      width: 24rpx;
      height: 4rpx;
      margin: 0 4rpx;
      background-color: #c5c5c5;

      &.active {
        background-color: #000;
      }
    }
  }

  .dots-style3 {
    position: absolute;
    right: 32rpx;
    bottom: 16rpx;
    color: #999;
    font-size: 24rpx;
    font-family: Helvetica;

    text {
      margin-right: 8rpx;
      color: #333;
      font-size: 32rpx;
      font-weight: 700;
    }
  }

  .dots-style4 {
    font-family: Helvetica;
    position: absolute;
    right: 32rpx;
    bottom: 16rpx;
    border-radius: 200rpx;
    background: rgba(0, 0, 0, 0.3);
    padding: 8rpx 20rpx;
    color: #fff;
    font-size: 24rpx;
  }
}

.map-area {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 1;
  overflow: hidden;

  view {
    position: absolute;
    // background: rgba(51, 136, 255, 0.5);
  }
}
</style>
