/*
  管理用户登录信息
  验证登录状态
  token维护
*/

import store from '@/store'
import { SET_ISLOGIN, SET_USERINFO } from '@/store/mutation-types'
import { WechatLogin } from '../service/common'

// 快捷登录，token有效直接回调
export const login = (callback, notSkip) => {
  const token = wx.getStorageSync('token')

  // 本地不存在token直接走登录
  if (!token) {
    if (!notSkip) {
      uni.navigateTo({
        url: '/pages/login/login',
      })
    } else {
      callback && callback(true)
    }
    return
  }

  // 本地token在有效期内直接使用
  // if (tokenExpire > Date.now()) {
  //   // 本地不存在用户信息重新获取
  //   if (!store.state.isLogin) return getToken(callback, notSkip)
  //
  //   store.commit(SET_ISLOGIN, true)
  //   callback && callback()
  //   return
  // }

  // token超出有效期重新获取
  // getToken(callback, notSkip)
}

// 微信code换取token
export const getToken = (callback, notSkip) => {
  wx.login({
    success: wxRes => {
      WechatLogin({
        code: wxRes.code,
        identityType: 2,
      })
        .then(res => {
          if (!res.success)
            return wx.showModal({
              title: '提示',
              content: '登录失效，请重新登录',
              confirmText: '知道了',
              showCancel: false,
            })
          const data = res.data
          // 保存登录用户信息
          saveUserInfo(data)

          callback && callback()
        })
        .catch(() => {
          if (!notSkip) {
            wx.navigateTo({
              url: '../login/login',
            })
          }
        })
    },
  })
}

// 保存用户信息
export const saveUserInfo = info => {
  store.commit(SET_USERINFO, info.userInfo)
  wx.setStorageSync('userInfo', info.userInfo)
  setToken(info.token)
}

// 设置token
export const setToken = token => {
  store.commit(SET_ISLOGIN, token !== '')
  wx.setStorageSync('token', token)
  // wx.setStorageSync('tokenExpire', Date.now() + effectiveTime)
}
