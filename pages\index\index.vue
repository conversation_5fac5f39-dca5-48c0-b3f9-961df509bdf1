<template>
  <view>
    <view v-if="navigation">
      <template v-if="navigation.enable">
        <view class="page-container">
          <view
            class="page-show"
            v-for="item in navigation.navs"
            :key="item.value"
            v-show="tabBarSelected === item.path"
          >
            <template v-if="item.path && item.path !== 'scan'">
              <basic-system-home v-if="item.path.includes('basicSystem/home')" :ref="item.path" />

              <!--              <wine-user v-else-if="item.path.includes('wineJar/user')" :ref="item.path" />-->
              <!--              <wine-stock v-else-if="item.path.includes('wineJar/wineStock')" :ref="item.path" />-->
              <!--              <wine-goods-list-->
              <!--                v-else-if="item.path.includes('wineJar/wineGoodsList')"-->
              <!--                :ref="item.path"-->
              <!--              />-->

              <jing-brand-stock
                v-else-if="item.path.includes('jingBrand/stock')"
                :ref="item.path"
              />

              <jing-brand-me v-else-if="item.path.includes('jingBrand/me')" :ref="item.path" />
            </template>
          </view>
        </view>
        <view class="tab-bar border-t" :style="{ backgroundColor: navigation.bgColor }">
          <view
            class="item"
            v-for="item in navigation.navs"
            :key="item.value"
            @click="handleSwitchTab(item)"
          >
            <image
              :src="(tabBarSelected === item.path ? item.iconActive : item.icon) | formatUrl(56)"
              mode="aspectFill"
            />
            <view
              :style="{
                color: tabBarSelected !== item.path ? navigation.color : navigation.colorActive,
              }"
            >
              {{ item.name }}</view
            >
          </view>
        </view>
      </template>
      <basic-system-home v-else />
    </view>
    <uni-popup ref="popup" type="center" :mask-click="false">
      <view class="popup">
        <view class="title">隐私政策提示</view>
        <view class="content">
          请您在使用劲牌封藏会员服务前点击
          <text @click="handleOpenPrivacyContract(1)">《劲牌藏酒用户协议》</text>、<text
            @click="handleOpenPrivacyContract(2)"
            >《隐私政策协议》</text
          >与<text @click="handleOpenPrivacyContract(3)">《赠酒免责条款》</text>
          并仔细阅读。如您同意以上全部服务，请点击“同意”开始使用我们的服务。
        </view>
        <view class="check" @click="isAgree = !isAgree">
          <text class="iconfont" :class="isAgree ? 'icon-radio-on' : 'icon-radio'"></text>
          本人已满14岁，可自行授权个人信息处理
        </view>
        <view class="btn-wrap flex-center">
          <button class="btn" @click="handleQuit">不同意</button>
          <button
            class="btn btn-primary"
            @click="handleAgreePrivacyAuthorization"
            :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }"
          >
            同意
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import BasicSystemHome from './basicSystem/home'

// import WineGoodsList from './wineJar/wineGoodsList'
// import WineStock from './wineJar/wineStock'
// import WineUser from './wineJar/user'

import JingBrandStock from './jingBrand/stock'
import JingBrandMe from './jingBrand/me'
import { AgreeProtocol, GetProtocolCheck } from '@/service/common'

export default {
  components: {
    BasicSystemHome,
    // WineGoodsList,
    // WineStock,
    // WineUser,
    JingBrandStock,
    JingBrandMe,
  },
  data() {
    return {
      buffer: '/pages/index/basicSystem/home', // 上次切换的菜单缓存
      loaded: false,
      isAgree: false,
    }
  },
  computed: {
    ...mapState(['setting', 'tabBarSelected', 'obsUrl', 'theme', 'systemSetting', 'isLogin']),
    navigation() {
      return this.setting.navigation
    },
  },

  watch: {
    tabBarSelected() {
      this.$refs[this.tabBarSelected][0].initPage()
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      })
    },
  },
  methods: {
    ...mapMutations(['SET_TAB_BAR_SELECTED']),

    // 点击切换tab
    handleSwitchTab(item) {
      this.$navigateTo({
        ...item,
        isTabbar: true,
        callback: () => {
          this.SET_TAB_BAR_SELECTED(item.path)
          this.buffer = item.path
        },
      })
    },
    handleQuit() {
      uni.exitMiniProgram()
    },
    // 隐私协议
    handleOpenPrivacyContract(type) {
      uni.navigateTo({
        url: '/pages/user/agreement?type=' + type,
      })
    },
    async handleAgreePrivacyAuthorization() {
      if (!this.isAgree) {
        uni.showToast({
          title: '请确认已满14岁',
          icon: 'none',
        })
        return
      }
      AgreeProtocol().then(() => {
        this.$refs.popup.close()
      })
    },

    protocolCheck() {
      if (this.isLogin) {
        // 检查是否需要显示隐私协议
        GetProtocolCheck().then(res => {
          this.isChecked = true
          if (!res.data) {
            this.$refs.popup.open()
          }
        })
      }
    },
  },
  onPullDownRefresh() {
    const page = this.$refs[this.tabBarSelected][0]
    if (page.initPage) {
      page.initPage()
    } else {
      uni.stopPullDownRefresh()
    }
  },
  onLoad() {},
  onShow() {
    if (!this.isChecked) {
      this.protocolCheck()
    }
    if (this.loaded) {
      if (this.buffer === this.tabBarSelected) {
        this.$refs[this.tabBarSelected][0].initPage()
      } else {
        this.buffer = this.tabBarSelected
      }
    } else {
      this.loaded = true
      if (this.$refs[this.tabBarSelected]?.[0]) {
        this.$refs[this.tabBarSelected][0].initPage()
      }
      // setTimeout(() => {
      //   console.log('wineGoodsList： ', this.$refs)
      //   this.$refs['wineGoodsList'][0].initPage()
      // }, 2000)
    }
    uni.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#c9e5fd',
    })
  },
  onHide() {
    try {
      this.$refs[this.tabBarSelected][0].hidePage()
    } catch (error) {}
  },
  onReachBottom() {
    try {
      this.$refs[this.tabBarSelected][0].reachBottom()
    } catch (error) {}
  },
  onShareAppMessage() {
    return {
      title: this.setting.webName,
      imageUrl: this.obsUrl + this.setting.webLogo,
      path: 'pages/index/index',
    }
  },
  onShareTimeline() {
    return {
      title: this.setting.webName,
      imageUrl: this.obsUrl + this.setting.webLogo,
      path: 'pages/index/index',
    }
  },
}
</script>

<style lang="scss" scoped>
.page-container {
  z-index: 88;
  padding-bottom: calc(98rpx);
  padding-bottom: calc(98rpx + env(safe-area-inset-bottom));
  background-color: #f4f5f6;
  min-height: calc(100vh);
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 98rpx));
  /* #ifdef APP-PLUS */
  min-height: calc(100vh - var(--window-top));
  min-height: calc(100vh - var(--window-top) - env(safe-area-inset-bottom));
  /* #endif */
  box-sizing: border-box;

  ::v-deep {
    .header {
      .nav-back {
        display: none;
      }

      .nav-logo {
        left: 16rpx !important;
      }
    }

    .container-root {
      min-height: calc(100vh - 98rpx);
      min-height: calc(100vh - 98rpx - env(safe-area-inset-bottom));
      box-sizing: border-box;

      .category-aside,
      .cart-foot-bar,
      .footer-fixed {
        bottom: 98rpx;
        bottom: calc(98rpx + env(safe-area-inset-bottom));
      }
    }
  }
}

.tab-bar {
  position: fixed;
  z-index: 99;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: #fff;
  height: 98rpx;
  padding-bottom: env(safe-area-inset-bottom);

  .item {
    position: relative;
    flex: 1;
    text-align: center;

    &.active {
      color: #f52440;
    }

    image {
      width: 56rpx;
      height: 56rpx;
      vertical-align: top;
    }

    view {
      line-height: 28rpx;
      font-size: 20rpx;
    }

    .badge {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(50%);
    }
  }
}

.popup {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 560rpx;
  padding: 48rpx 32rpx;
  border-radius: 36rpx;
  background: #fff;

  .title {
    line-height: 56rpx;
    font-size: 36rpx;
    font-weight: 600;
  }

  .content {
    width: 496rpx;
    margin-top: 32rpx;
    word-break: break-word;

    text {
      color: #1f39f5;
    }
  }

  .check {
    display: flex;
    align-items: center;
    width: 496rpx;
    margin-top: 24rpx;
    line-height: 32rpx;
    font-size: 24rpx;
    color: #666;
    text {
      font-size: 32rpx;
      margin-right: 8rpx;
      &.icon-radio-on {
        color: #f52440;
      }
    }
  }

  .btn-wrap {
    width: 100%;
    margin-top: 48rpx;

    .btn {
      flex: 1;
      margin: 0 16rpx;
      &::after {
        border-radius: 42rpx;
      }
    }
  }
}
</style>
