<template>
  <view :style="{ backgroundColor: dataset.outerBgColor, padding: dataset.verticalMargin * 2 + 'rpx 0', borderRadius: dataset.moduleRadius ? '16rpx' : 0 }">
    <view class="wrap">
      <view class="nav-box">
        <view v-if="dataset.showType === 1" class="image-nav" :class="{ 'nav-flex': !dataset.scroll }" :style="{ backgroundColor: dataset.bgColor, margin: '0 ' + (dataset.pageMargin * 2 - dataset.imgMargin) + 'rpx' }">
          <view class="item" v-for="(item, index) in dataset.images" :key="index" @click="skip(item.link)" :style="{ width: dataset.scroll ? width + 'rpx' : 'auto' }">
            <view :style="{ height: dataset.scroll ? width - dataset.imgMargin * 2 + 'rpx' : height + 'rpx', margin: '0 ' + dataset.imgMargin + 'rpx' }">
              <image lazy-load :src="item.imgSrc | formatUrl(750)" mode="aspectFill" />
            </view>
            <view class="text" :style="{ color: dataset.textColor }" v-if="item.title">{{ item.title }}</view>
          </view>
        </view>
        <view v-else class="image-nav text-nav" :class="{ 'nav-flex': !dataset.scroll }" :style="{ margin: '0 ' + dataset.pageMargin * 2 + 'rpx' }">
          <view class="item" v-for="(item, index) in dataset.images" :key="index" @click="skip(item.link)" :style="{ width: dataset.scroll ? width + 'rpx' : 'auto' }">
            <view class="text" :style="{ color: dataset.textColor }">{{ item.title }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    dataset: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {
    width() {
      const count = this.dataset.lineCount
      return parseInt(750 / count + 750 / count / count)
    },
    height() {
      const total = 750 - this.dataset.pageMargin * 2 * 2
      let height = total / this.dataset.images.length - this.dataset.imgMargin * 2
      if (this.dataset.heightType) {
        height = this.dataset.height * 2
      }
      return height
    }
  },
  methods: {
    skip(link) {
      if (!link) {
        return
      }
      this.$navigateTo({
        ...link
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wrap {
  overflow: hidden;
  .nav-box {
    overflow-x: auto;
    padding-bottom: 32rpx;
    margin-bottom: -32rpx;
  }
}

.image-nav {
  white-space: nowrap;

  &.nav-flex {
    display: flex;

    .item {
      flex: 1;
      overflow: hidden;
    }
  }

  .item {
    display: inline-block;
    vertical-align: top;
    image {
      width: 100%;
      height: 100%;
      display: block;
    }
    .text {
      position: relative;
      padding: 16rpx 10rpx 0;
      height: 32rpx;
      line-height: 32rpx;
      text-align: center;
      font-size: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &.text-nav {
    .item {
      .text {
        padding: 0 8rpx;
        height: 84rpx;
        line-height: 84rpx;
      }

      & + .item .text::before {
        position: absolute;
        content: '';
        width: 1rpx;
        height: 24rpx;
        top: 30rpx;
        left: 0;
        background: #e5e5e5;
      }
    }
  }
}
</style>
