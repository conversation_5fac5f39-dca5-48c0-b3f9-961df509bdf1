/**
 * @description: 上传文件到obs（前端直传）
 * @param {mediaType} 上传文件类型 默认image，可选video、audio
 * @param {count} 上传文件数量 默认1，可选多个
 * @param {success} 上传成功回调
 * @returns array/string 返回上传结果，选择单个文件返回string，多个文件返回array
 */

import { GetTemporaryCredentials } from '@/service/common'
import getPolicyEncode from './getPolicy'
import getSignature from './getSignature'

const uploadFile = ({ count = 1, mediaType = 'image', success, fail }) => {
  const resultFiles = []
  uni.chooseMedia({
    count,
    mediaType: [mediaType],
    sourceType: ['album', 'camera'],
    maxDuration: 30,
    success: async (fileRes) => {
      const obsCredentials = await GetTemporaryCredentials()
      if (!obsCredentials) {
        fail && fail()
        return uni.showToast({
          title: '获取临时凭证失败',
          icon: 'none'
        })
      }
      const config = obsCredentials.data

      uni.showLoading({
        title: '上传中',
        mask: true
      })
      const files = fileRes.tempFiles
      files.forEach((file, index) => {
        const filePath = file.tempFilePath
        const fileName = `${config.path}${filePath.split('tmp/')[1]}`
        const date = new Date()
        date.setHours(date.getHours() + 1)
        const OBSPolicy = {
          // 设定policy内容，policy规则定义可参考步骤3中的超链接签名计算规则文档
          expiration: date.toISOString(),
          conditions: [{ bucket: config.bucketName }, { 'x-obs-security-token': config.security_token }, { key: fileName }]
        }

        const policyEncoded = getPolicyEncode(OBSPolicy) // 计算base64编码后的policy
        const signature = getSignature(policyEncoded, config.secret_access_key) // 计算signature

        uni.uploadFile({
          url: config.endPoint,
          filePath,
          name: 'file',
          header: {
            'content-type': 'multipart/form-data'
          },
          formData: {
            // 从配置文件中获取到的AK信息、计算得到的编码后policy及signature信息
            AccessKeyID: config.access_key_id,
            policy: policyEncoded,
            signature: signature,
            key: fileName,
            'x-obs-security-token': config.security_token
          },
          success: (res) => {
            if (res.statusCode !== 204) {
              console.log('Uploaded failed', res)
              uni.hideLoading()
              return uni.showToast({
                title: '图片上传失败',
                icon: 'none'
              })
            }
            resultFiles.push(fileName)
            if (index === files.length - 1) {
              uni.hideLoading()
              const result = count === 1 ? resultFiles[0] : resultFiles
              success && success(result)
            }
          },
          fail: (e) => {
            console.log(e)
            uni.hideLoading()
          }
        })
      })
    }
  })
}

export default uploadFile
