import { GetInitConfig } from '@/service/common'
import {
  SET_SETTING,
  SET_THEME,
  SET_USERINFO,
  SET_CACHE_ADDRESS,
  SET_OBSURL,
  SET_SPECIAL_GOODS_ID,
} from './mutation-types'
import { appId } from '@/config/system'
import themeColor from '@/assets/js/themeColor'
import { getThemeColor } from '@/common/utils'
import { getSpecialGoodsIds } from '@/service/jingBrand/stock'

export default {
  async getSettingAction({ commit, state }, callback) {
    GetInitConfig({
      appId,
    }).then(res => {
      const data = res.data
      data.pointsName = data.pointsName || '积分'
      data.navigation = JSON.parse(data.navigationJson || '{}')
      commit(SET_SETTING, data)
      commit(SET_OBSURL, data.obsPrefixUrl)

      // 配置主题色
      // const themeId = JSON.parse(data.themeJson || '{}').themeId || 0
      const themeColor = JSON.parse(data.themeJson)
      if (themeColor) {
        themeColor.secondaryTextColor = getThemeColor.get(themeColor.secondary.color6)
        commit(SET_THEME, themeColor)
      }

      callback && callback()
    })
  },

  async setCacheAddressAction({ commit }, params) {
    commit(SET_CACHE_ADDRESS, params)
  },

  async setUserInfo({ commit }, params) {
    commit(SET_USERINFO, params)
  },

  setSpecialGoodsIds({ commit }) {
    getSpecialGoodsIds().then(({ code, data }) => {
      if (code === '200') {
        commit(SET_SPECIAL_GOODS_ID, data || [])
      }
    })
  },
}
