<template>
	<view class="notice-bar" :style="{ backgroundColor: dataset.bgColor }">
		<text class="iconfont icon-notice"></text>
		<view class="notice-bar-wrap">
			<view class="notice-bar-content" :class="{ 'notice-bar-play': dataset.isAnimation }" :style="{ fontSize: dataset.textSize + 'px', color: dataset.textColor, animationDuration: dataset.speed + 's' }">{{dataset.content}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			dataset: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss" scoped>
	.notice-bar {
	  position: relative;
	  display: flex;
	  align-items: center;
	  height: 80rpx;
	  padding-right: 32rpx;
	  font-size: 28rpx;
	  line-height: 48rpx;
	  text {
	    width: 70rpx;
	    text-align: center;
	    color: #ed6a0c;
	    font-size: 40rpx;
	  }
	  .notice-bar-wrap {
	    position: relative;
	    flex: 1;
	    height: 48rpx;
	    overflow: hidden;
	    .notice-bar-content {
	      position: absolute;
	      white-space: nowrap;
	      left: 0;
	      top: 0;
	      &.notice-bar-play {
	        padding-left: 648rpx;
	        animation: notice-bar-play linear infinite both;
	        animation-delay: 0s;
	        animation-duration: 10s;
	      }
	    }
	  }
	}
	
	@keyframes notice-bar-play {
	  to {
	    transform: translate3d(-100%, 0, 0);
	  }
	}
</style>
