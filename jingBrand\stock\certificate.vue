<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '查看证书' }"
      @init="statusBarHeight = $event"
    />
    <view
      :style="{ paddingTop: statusBarHeight + 'rpx', display: 'flex', justifyContent: 'center' }"
    >
      <view
        class="img"
        :style="{ 'background-image': `url(${formatUrl(detail.certificate)})` }"
        @click="previewImage"
      />
      <!--      <Cert v-if="certificateJSON" :value="certificateJSON" />-->
      <!--      <image-->
      <!--        :src="detail.certificate | formatUrl"-->
      <!--        mode="aspectFill"-->
      <!--        class="img"-->
      <!--        @click="previewImage"-->
      <!--      />-->
    </view>

    <button v-if="isShare" class="btn btn-primary" @click="goHome">回到首页</button>
    <button v-else class="btn btn-primary" open-type="share">分享好友</button>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import Cert from './components/Cert.vue'
import request from '@/config/request'
import { mapMutations } from 'vuex'
import formatUrl from '@/hooks/formatUrl'

export default {
  name: 'Certificate',

  components: { CustomNav, Cert },

  data() {
    return {
      statusBarHeight: 128,
      id: null,
      isShare: false,
      detail: {},
    }
  },

  computed: {
    certificateVos() {
      return this.detail.certificateVos || []
    },
    certificateJSON() {
      if (!this.certificateVos.length) return null

      const json = JSON.parse(this.certificateVos[0].templateJson || 'null')

      if (!json) return null

      json.fields.forEach(field => {
        field.text = this.detail[field.id]
      })
      return json
    },
  },

  onShareAppMessage() {
    return {
      title: '劲牌封藏，专属电子证书',
      path: `/jingBrand/stock/certificate?id=${this.id}&share=1`,
    }
  },

  async onLoad({ id, share }) {
    this.id = id
    this.isShare = share === '1'
    const res = await request({
      url: 'jingBrand/mini/warehouseWine/detail',
      data: { id },
    })
    this.detail = res.data
  },

  methods: {
    formatUrl,
    ...mapMutations(['SET_TAB_BAR_SELECTED']),
    goHome() {
      this.SET_TAB_BAR_SELECTED('/pages/index/basicSystem/home')
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
    previewImage() {
      uni.previewImage({
        urls: [`${formatUrl(this.detail.certificate)}`],
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-primary {
  position: fixed;
  left: 24rpx;
  right: 24rpx;
  bottom: 24rpx;
  background-color: #0b7d83;
}

.img {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  margin: auto;
  padding-top: 66.667%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
</style>
