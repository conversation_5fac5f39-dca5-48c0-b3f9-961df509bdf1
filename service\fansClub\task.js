import request from '@/config/request'

// 获取任务列表
export const GetTaskList = (data) => request({ url: 'fansClub/mini/task/list', data, method: 'post', showLoading: false })
// 获取任务详情
export const GetTaskDetail = (data) => request({ url: 'fansClub/mini/task/detail', data })
// 领取任务奖励
export const ClaimTask = (data) => request({ url: 'fansClub/mini/task/claim', data, method: 'post' })
// 领取任务奖励
export const ClaimReward = (data) => request({ url: 'fansClub/mini/task/claimReward', data, method: 'post' })
// 获取用户未领取积分
export const GetUnderReceive = (data) => request({ url: 'fansClub/mini/points/getUnderReceive', data, showLoading: false })
// 获取积分规则
export const GetRule = (data) => request({ url: 'fansClub/mini/points/getRule', data })
// 收集积分
export const PointsCollect = (data) => request({ url: 'fansClub/mini/points/collect', data, method: 'post', showLoading: false })
// 获取当前用户的积分描述
export const GetPointsDesc = (data) => request({ url: 'fansClub/mini/points/desc', data, method: 'post' })
