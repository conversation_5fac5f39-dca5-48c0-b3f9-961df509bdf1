/**
 * @description: 自定义页面跳转
 * @param {String} targetType 跳转类型
 * @param {String} path 跳转路径
 * @param {String} appId 小程序appId
 * @param {String} appPath 小程序路径
 * @param {String} articleLink H5链接
 * @param {Function} callback 回调函数
 * @param {Boolean} isTabbar 是否为Tabbar切换
 * @return {void}
 **/
import store from '@/store'

const navigateTo = ({ targetType, path, appId, appPath, articleLink, isTabbar, callback }) => {
  // 特殊跳转处理
  // 打开其他小程序
  if (targetType === 'OTHER_MINI_APP') {
    return uni.navigateToMiniProgram({
      appId,
      path: appPath || 'pages/index/index'
    })
  }

  // 打开H5
  if (targetType === 'OFFICIAL_ACCOUNT') {
    return uni.navigateTo({
      url: `/pages/webview/webview?url=${encodeURIComponent(articleLink)}`
    })
  }

  // 扫码
  if (path === 'scan') {
    return uni.scanCode({
      success: (res) => {
        const result = res.result
        if (!result)
          return uni.showToast({
            title: '未找到二维码',
            icon: 'none'
          })
        const code = result.split('weixin/')[1]
        if (!code)
          return uni.showToast({
            title: '无效的二维码',
            icon: 'none'
          })
        uni.navigateTo({
          url: `/scanMarketing/activity/detail?qrcode=${code}`
        })
      }
    })
  }
  if (!path) return

  if (isTabbar) {
    callback && callback()
    return
  }

  const indexPath = '/pages/index/index'
  if (path === indexPath) {
    store.commit('SET_TAB_BAR_SELECTED', '/pages/index/basicSystem/home')
    return uni.switchTab({
      url: path
    })
  }

  if (store.state.setting.navigation.navs.some((item) => item.path === path)) {
    store.commit('SET_TAB_BAR_SELECTED', path)
    return uni.switchTab({
      url: indexPath
    })
  }
  uni.navigateTo({
    url: path
  })
}

export default navigateTo
