<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '查看监控' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div v-for="(url, index) in urls" :key="index">
        <video :src="url" autoplay is-live object-fit="fit" style="width: 100%" />
      </div>
    </div>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'

export default {
  components: { CustomNav },
  data() {
    return {
      statusBarHeight: 168,
      urls: [],
    }
  },
  onLoad({ urls }) {
    this.urls = JSON.parse(decodeURIComponent(urls))
  },
}
</script>
