<template>
  <web-view :src="url" />
</template>

<script>
export default {
  data() {
    return {
      url: '',
    }
  },
  methods: {},
  onLoad(options) {
    // 在Android平台上, Webview内容加载完成后, 会将title变为空, 解决方案如下:
    // https://developers.weixin.qq.com/community/develop/article/doc/000a8aa6f3471002ade0812ea63c13
    this.url = decodeURIComponent(options.url) + '?url=劲牌封藏'
  },
  onShareAppMessage() {
    return {}
  },
}
</script>
