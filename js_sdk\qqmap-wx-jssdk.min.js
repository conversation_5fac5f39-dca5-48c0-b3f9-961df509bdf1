var _createClass = function() {
	function a(e, c) {
		for (var b = 0; b < c.length; b++) {
			var d = c[b];
			d.enumerable = d.enumerable || false;
			d.configurable = true;
			if ("value" in d) {
				d.writable = true
			}
			Object.defineProperty(e, d.key, d)
		}
	}
	return function(d, b, c) {
		if (b) {
			a(d.prototype, b)
		}
		if (c) {
			a(d, c)
		}
		return d
	}
}();

function _classCallCheck(a, b) {
	if (!(a instanceof b)) {
		throw new TypeError("Cannot call a class as a function")
	}
}
var ERROR_CONF = {
	KEY_ERR: 311,
	KEY_ERR_MSG: "key格式错误",
	PARAM_ERR: 310,
	PARAM_ERR_MSG: "请求参数信息有误",
	SYSTEM_ERR: 600,
	SYSTEM_ERR_MSG: "系统错误",
	WX_ERR_CODE: 1000,
	WX_OK_CODE: 200
};
var BASE_URL = "https://apis.map.qq.com/ws/";
var URL_SEARCH = BASE_URL + "place/v1/search";
var URL_SUGGESTION = BASE_URL + "place/v1/suggestion";
var URL_GET_GEOCODER = BASE_URL + "geocoder/v1/";
var URL_CITY_LIST = BASE_URL + "district/v1/list";
var URL_AREA_LIST = BASE_URL + "district/v1/getchildren";
var URL_DISTANCE = BASE_URL + "distance/v1/";

var output = 'json';
// #ifdef H5
var output = 'jsonp';
// #endif

var Utils = {
	location2query: function location2query(c) {
		if (typeof c == "string") {
			return c
		}
		var b = "";
		for (var a = 0; a < c.length; a++) {
			var e = c[a];
			if (!!b) {
				b += ";"
			}
			if (e.location) {
				b = b + e.location.lat + "," + e.location.lng
			}
			if (e.latitude && e.longitude) {
				b = b + e.latitude + "," + e.longitude
			}
		}
		return b
	},
	getWXLocation: function getWXLocation(c, b, a) {
		uni.getLocation({
			type: "gcj02",
			success: c,
			fail: b,
			complete: a
		})
	},
	getLocationParam: function getLocationParam(b) {
		if (typeof b == "string") {
			var a = b.split(",");
			if (a.length === 2) {
				b = {
					latitude: b.split(",")[0],
					longitude: b.split(",")[1]
				}
			} else {
				b = {}
			}
		}
		return b
	},
	polyfillParam: function polyfillParam(a) {
		a.success = a.success || function() {};
		a.fail = a.fail || function() {};
		a.complete = a.complete || function() {}
	},
	checkParamKeyEmpty: function checkParamKeyEmpty(c, b) {
		if (!c[b]) {
			var a = this.buildErrorConfig(ERROR_CONF.PARAM_ERR, ERROR_CONF.PARAM_ERR_MSG + b + "参数格式有误");
			c.fail(a);
			c.complete(a);
			return true
		}
		return false
	},
	checkKeyword: function checkKeyword(a) {
		return !this.checkParamKeyEmpty(a, "keyword")
	},
	checkLocation: function checkLocation(c) {
		var a = this.getLocationParam(c.location);
		if (!a || !a.latitude || !a.longitude) {
			var b = this.buildErrorConfig(ERROR_CONF.PARAM_ERR, ERROR_CONF.PARAM_ERR_MSG + " location参数格式有误");
			c.fail(b);
			c.complete(b);
			return false
		}
		return true
	},
	buildErrorConfig: function buildErrorConfig(a, b) {
		return {
			status: a,
			message: b
		}
	},
	buildWxRequestConfig: function buildWxRequestConfig(c, a) {
		var b = this;
		a.header = {
			"content-type": "application/json"
		};
		a.method = "GET";
		a.success = function(d) {
			var e = d.data;
			if (e.status === 0) {
				c.success(e)
			} else {
				c.fail(e)
			}
		};
		a.fail = function(d) {
			d.statusCode = ERROR_CONF.WX_ERR_CODE;
			c.fail(b.buildErrorConfig(ERROR_CONF.WX_ERR_CODE, result.errMsg))
		};
		a.complete = function(d) {
			var e = +d.statusCode;
			switch (e) {
				case ERROR_CONF.WX_ERR_CODE:
					c.complete(b.buildErrorConfig(ERROR_CONF.WX_ERR_CODE, d.errMsg));
					break;
				case ERROR_CONF.WX_OK_CODE:
					var f = d.data;
					if (f.status === 0) {
						c.complete(f)
					} else {
						c.complete(b.buildErrorConfig(f.status, f.message))
					}
					break;
				default:
					c.complete(b.buildErrorConfig(ERROR_CONF.SYSTEM_ERR, ERROR_CONF.SYSTEM_ERR_MSG))
			}
		};
		return a
	},
	getQuery(data) {
		const arr = []
		for (const k in data) {
			arr.push(k + '=' + data[k])
		}
		return arr.join('&')
	},
	buildJsonpRequest: function buildJsonpRequest(c, a) {
		const mapScript = document.getElementById('mapScript')
		if (mapScript) {
			document.body.removeChild(mapScript)
		}
		const script = document.createElement('script')
		script.type = 'text/javascript'
		script.id = 'mapScript'
		script.src = `${a.url}?${this.getQuery(a.data)}`
		document.body.appendChild(script)
	},
	locationProcess: function locationProcess(f, e, c, a) {
		var d = this;
		c = c || function(g) {
			g.statusCode = ERROR_CONF.WX_ERR_CODE;
			f.fail(d.buildErrorConfig(ERROR_CONF.WX_ERR_CODE, g.errMsg))
		};
		a = a || function(g) {
			if (g.statusCode == ERROR_CONF.WX_ERR_CODE) {
				f.complete(d.buildErrorConfig(ERROR_CONF.WX_ERR_CODE, g.errMsg))
			}
		};
		if (!f.location) {
			d.getWXLocation(e, c, a)
		} else {
			if (d.checkLocation(f)) {
				var b = Utils.getLocationParam(f.location);
				e(b)
			}
		}
	}
};
var QQMapWX = function() {
	function b(i) {
		_classCallCheck(this, b);
		if (!i.key) {
			throw Error("key值不能为空")
		}
		this.key = i.key
	}
	_createClass(b, [{
		key: "search",
		value: function f(i) {
			var l = this;
			i = i || {};
			Utils.polyfillParam(i);
			if (!Utils.checkKeyword(i)) {
				return
			}
			var k = {
				keyword: i.keyword,
				orderby: i.orderby || "_distance",
				page_size: i.page_size || 10,
				page_index: i.page_index || 1,
				output,
				key: l.key
			};
			if (i.address_format) {
				k.address_format = i.address_format
			}
			if (i.filter) {
				k.filter = i.filter
			}
			var n = i.distance || "1000";
			var j = i.auto_extend || 1;
			var m = function m(o) {
				k.boundary = "nearby(" + o.latitude + "," + o.longitude + "," + n + "," + j +
					")";
				// #ifndef H5
				uni.request(Utils.buildWxRequestConfig(i, {
					url: URL_SEARCH,
					data: k
				}))
				// #endif
				// #ifdef H5
				Utils.buildJsonpRequest(i, {
					url: URL_SEARCH,
					data: k
				})
				// #endif
			};
			Utils.locationProcess(i, m)
		}
	}, {
		key: "getSuggestion",
		value: function h(i) {
			var k = this;
			i = i || {};
			Utils.polyfillParam(i);
			if (!Utils.checkKeyword(i)) {
				return
			}
			var j = {
				keyword: i.keyword,
				region: i.region || "全国",
				region_fix: i.region_fix || 0,
				policy: i.policy || 0,
				output,
				key: k.key
			};
			// #ifndef H5
			uni.request(Utils.buildWxRequestConfig(i, {
				url: URL_SUGGESTION,
				data: j
			}))
			// #endif
			// #ifdef H5
			Utils.buildJsonpRequest(i, {
				url: URL_GET_GEOCODER,
				data: j
			})
			// #endif
		}
	}, {
		key: "reverseGeocoder",
		value: function a(i) {
			var k = this;
			i = i || {};
			Utils.polyfillParam(i);
			var j = {
				coord_type: i.coord_type || 5,
				get_poi: i.get_poi || 0,
				output,
				key: k.key
			};
			if (i.poi_options) {
				j.poi_options = i.poi_options
			}
			var l = function l(m) {
				j.location = m.latitude + "," + m.longitude;
				
				// #ifndef H5
				uni.request(Utils.buildWxRequestConfig(i, {
					url: URL_GET_GEOCODER,
					data: j
				}))
				// #endif
				// #ifdef H5
				Utils.buildJsonpRequest(i, {
					url: URL_GET_GEOCODER,
					data: j
				})
				// #endif
			};
			Utils.locationProcess(i, l)
		}
	}, {
		key: "geocoder",
		value: function g(i) {
			var k = this;
			i = i || {};
			Utils.polyfillParam(i);
			if (Utils.checkParamKeyEmpty(i, "address")) {
				return
			}
			var j = {
				address: i.address,
				output,
				key: k.key
			};
			// #ifndef H5
			uni.request(Utils.buildWxRequestConfig(i, {
				url: URL_GET_GEOCODER,
				data: j
			}))
			// #endif
			// #ifdef H5
			Utils.buildJsonpRequest(i, {
				url: URL_GET_GEOCODER,
				data: j
			})
			// #endif
		}
	}, {
		key: "getCityList",
		value: function c(i) {
			var k = this;
			i = i || {};
			Utils.polyfillParam(i);
			var j = {
				output,
				key: k.key
			};
			// #ifndef H5
			uni.request(Utils.buildWxRequestConfig(i, {
				url: URL_CITY_LIST,
				data: j
			}))
			// #endif
			// #ifdef H5
			Utils.buildJsonpRequest(i, {
				url: URL_GET_GEOCODER,
				data: j
			})
			// #endif
		}
	}, {
		key: "getDistrictByCityId",
		value: function d(i) {
			var k = this;
			i = i || {};
			Utils.polyfillParam(i);
			if (Utils.checkParamKeyEmpty(i, "id")) {
				return
			}
			var j = {
				id: i.id || "",
				output,
				key: k.key
			};
			// #ifndef H5
			uni.request(Utils.buildWxRequestConfig(i, {
				url: URL_AREA_LIST,
				data: j
			}))
			// #endif
			// #ifdef H5
			Utils.buildJsonpRequest(i, {
				url: URL_GET_GEOCODER,
				data: j
			})
			// #endif
		}
	}, {
		key: "calculateDistance",
		value: function e(i) {
			var k = this;
			i = i || {};
			Utils.polyfillParam(i);
			if (Utils.checkParamKeyEmpty(i, "to")) {
				return
			}
			var j = {
				mode: i.mode || "walking",
				to: Utils.location2query(i.to),
				output,
				key: k.key
			};
			var l = function l(m) {
				j.from = m.latitude + "," + m.longitude;
				// #ifndef H5
				uni.request(Utils.buildWxRequestConfig(i, {
					url: URL_DISTANCE,
					data: j
				}))
				// #endif
				// #ifdef H5
				Utils.buildJsonpRequest(i, {
					url: URL_GET_GEOCODER,
					data: j
				})
				// #endif
			};
			if (i.from) {
				i.location = i.from
			}
			Utils.locationProcess(i, l)
		}
	}]);
	return b
}();
module.exports = QQMapWX;
