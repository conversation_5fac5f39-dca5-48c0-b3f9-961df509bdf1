// Description: 商品等相关
import request from '@/config/request'
// 获取藏酒详情
export const getWineDepotDetail = (data) => request({ url: 'fengtanWine/miniWineCellar/getDepositDetail', data })
// 酒库详情
export const getWineDepot = (data) => request({ url: 'fengtanWine/miniWineCellar/wineCellarDetail', data })
// 包材管理
export const queryTakeWineWrapperList = (data) => request({ url: 'fengtanWine/wrapper/mini/queryTakeWineWrapperList', data, method: 'post' })

// 包材详情
export const WineWrapperInfo = (data) => request({ url: 'fengtanWine/wrapper/getWrapperDetail', data })


// 酒票模板列表
export const GetTicketList = (data) => request({ url: 'fengtanWine/ticketTemplate/mini/pageList', data, method: 'post' })
 
 // 创建酒票
 export const CreateTicket = (data) => request({ url: 'fengtanWine/miniTicket/createTicket', data, method: 'post' })
  
 // 酒票详情
 export const getTicketDetail = (data) => request({ url: 'fengtanWine/miniTicket/detail', data })

// 撤销酒票
 export const RevokeTicket = (data) => request({ url: 'fengtanWine/miniTicket/revokeTicket', data, method: 'post' })
 
 // 领取酒票
  export const ReceiveTicket = (data) => request({ url: 'fengtanWine/miniTicket/receiveTicket', data, method: 'post' })
  
 // 藏酒保管费
  export const DepositStorageFee = (data) => request({ url: 'fengtanWine/miniWineCellar/computeDepositStorageFee', data })
  

  // 藏酒支付保管费
  export const WineFee = (data) => request({ url: 'fengtanWine/miniWineCellar/payDepositStorageFee', data, method: 'post' })
  
  // 酒票保管费
  export const WineTicketFee = (data) => request({ url: 'fengtanWine/miniTicket/computeWineTicketFee', data, method: 'post' })
  
  // 获取证书详情
  export const getCertificateDetail = (data) => request({ url: 'fengtanWine/miniCertificate/getWineCertificateDetail', data })
  
  // 发送验证码
  export const GetTransferCode = (data) => request({ url: 'fengtanWine/sms/sendSmsVerifyCode', data, method: 'post' })
 // 校验验证码
 export const CheckTransferCode = (data) => request({ url: 'fengtanWine/sms/checkSmsVerifyCode', data, method: 'post' })
  
  
  // 藏酒转让
  export const WineTransfer = (data) => request({ url: 'fengtanWine/miniWineCellar/transfer', data, method: 'post' })

  // 撤销藏酒转让
  export const CannleWineTransfer = (data) => request({ url: 'fengtanWine/depositTrans/mini/revokeCellarTransfer', data, method: 'post' })


  // 获取交易配置
  export const GetTradeSetting = (data) => request({ url: 'fengtanWine/systemSetting/getTradeSetting', data })
