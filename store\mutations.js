import * as types from './mutation-types'

export default {
  [types.SET_SETTING](state, setting) {
    state.setting = setting
  },
  [types.SET_OBSURL](state, url) {
    // const obsUrl = `${url}/`
    state.obsUrl = url
    uni.setStorageSync('obsUrl', url)
  },
  [types.SET_ISLOGIN](state, off) {
    state.isLogin = off
  },

  [types.SET_CACHE_ADDRESS](state, params) {
    state.cacheAddress = params
  },
  [types.SET_WECHAT_SUBSCRIBE_MESSAGE](state, params) {
    state.wechatSubscribeMessage = params
  },

  [types.SET_USERINFO](state, params) {
    state.userInfo = params
  },
  [types.SET_THEME](state, theme) {
    state.theme = theme
  },

  [types.SET_TAB_BAR_SELECTED](state, path) {
    console.log('SET_TAB_BAR_SELECTED:', path)
    state.tabBarSelected = path
  },

  [types.SET_LOGIN_PAGE](state, value) {
    state.isLoginPage = value
  },

  [types.SET_SPECIAL_GOODS_ID](state, value) {
    state.specialGoodsIds = value
  },
}
