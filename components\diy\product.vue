<template>
  <view :style="{ backgroundColor: dataset.bgColor, padding: dataset.verticalMargin * 2 + 'rpx 0', borderRadius: dataset.moduleRadius ? '16rpx' : 0 }">
    <view class="wrap">
      <view :class="{ scroll: dataset.showType === 6 }">
        <view class="product-list clearfix" :class="[`style${dataset.showType}`, { 'list-waterfall': dataset.listStyle === 6 }]" :style="{ padding: '0 ' + dataset.pageMargin * 2 + 'rpx', margin: -dataset.productMargin + 'rpx' }">
          <view class="item" v-for="(item, index) in productList" :key="index" @click="navigateTo(item.id)" :style="dataset.fontStyle ? 'font-weight:bold' : ''">
            <view class="product-wrap" :style="{ margin: dataset.productMargin + 'rpx', borderRadius: dataset.radius ? '16rpx' : 0, backgroundColor: dataset.listStyle === 4 ? 'transparent' : '#fff', boxShadow: dataset.listStyle === 2 ? '0 2px 8px rgba(93,113,127,.08)' : 'none', border: dataset.listStyle === 3 ? '1px solid #f2f2f2' : '0' }">
              <view class="product-img" v-if="dataset.listStyle !== 6" :style="{ paddingTop: dataset.imgRatio * 100 + '%' }">
                <image lazy-load :src="item.thumbnail | formatUrl(400)" :mode="dataset.imgFill ? 'aspectFill' : 'aspectFit'" />
              </view>
              <view class="product-img" v-else>
                <image lazy-load :src="item.thumbnail | formatUrl(400)" mode="widthFix" style="height: auto" />
              </view>
              <view class="tag" v-if="dataset.showTag">
                <image v-if="dataset.tagStyle < 5" :src="defaultTag[dataset.tagStyle - 1] | formatUrl" mode="aspectFill" />
                <image v-else :src="dataset.tagImg | formatUrl(80)" mode="aspectFill" />
              </view>
              <view class="product-info" :style="dataset.textAlign ? 'text-align:center' : ''">
                <view class="h3" v-if="dataset.showName" :class="{ 'two-line': dataset.nameLine === 2 }" :style="{ color: dataset.nameColor }">
                  <image v-if="dataset.showNameIcon" :src="dataset.nameIcon | formatUrl(100)" mode="heightFix" />
                  {{ item.name }}
                </view>
                <view class="p" v-if="dataset.showDesc" :style="{ color: dataset.descColor }">{{ item.sellingPoint }}</view>
                <view class="buy-info" v-if="dataset.showPrice || dataset.showBtn" :class="{ 'flex-column': dataset.textAlign }">
                  <text v-if="dataset.showPrice" class="price" :style="{ color: dataset.priceColor }">
                    <text>¥</text>
                    {{ item.salePrice }}
                  </text>
                  <text class="s" :style="{ color: dataset.markingPriceColor }" v-if="dataset.showMarkingPrice && item.markingPrice">¥{{ item.markingPrice }}</text>

                  <template v-if="dataset.showBtn">
                    <text v-if="dataset.listStyle === 5" class="btn-limit" @click.stop="handleAddCart(item)">我要抢购</text>
                    <text v-else-if="dataset.btnStyle !== 9" @click.stop="handleAddCart(item)" class="btn-buy iconfont" :class="['style' + dataset.btnStyle, { 'icon-cart3': dataset.btnStyle === 1, 'icon-cart2': dataset.btnStyle === 2, 'icon-plus': dataset.btnStyle === 3, 'icon-plus-fill': dataset.btnStyle === 4 }]">{{ dataset.btnStyle > 4 ? dataset.btnText : '' }}</text>
                    <image v-else :src="dataset.btnImg | formatUrl(120)" mode="aspectFit" @click.stop="handleAddCart(item)" />
                  </template>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { MARKETING_TYPE } from '@/common/constant'

export default {
  props: {
    dataset: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      defaultTag: ['static/applet/tag_new_cn.png', 'static/applet/tag_hot_cn.png', 'static/applet/tag_new_en.png', 'static/applet/tag_hot_en.png'],
      productList: this.dataset.products
    }
  },
  methods: {
    navigateTo(id) {
      uni.navigateTo({
        url: '../product/detail?productId=' + id
      })
    },
    updateProduct() {
      // if (this.dataset.productType) {
      //   GetProductList(
      //     {
      //       pageNo: 1,
      //       pageSize: this.dataset.productCount,
      //       category: this.dataset.categoryId,
      //       sortName: this.dataset.sortType ? this.dataset.sortType.key : '',
      //       sortDesc: this.dataset.sortType ? this.dataset.sortType.value : true
      //     },
      //     false
      //   ).then((res) => {
      //     this.computeList(res.data.data)
      //   })
      // } else {
      //   const productIds = []
      //   this.dataset.products.map((item) => {
      //     productIds.push(item.id)
      //   })
      //   if (productIds.length === 0) return
      //   GetProducts({ids: productIds.join(',')}).then(res => {
      //   	this.computeList(res.data)
      //   })
      // }
    },
    computeList(data) {
      let list = data
      if (this.dataset.listStyle === 6) {
        const oddArr = []
        const evenArr = []
        list.map((item, index) => {
          if (index % 2) {
            evenArr.push(item)
          } else {
            oddArr.push(item)
          }
        })
        list = [...oddArr, ...evenArr]
      }
      this.productList = list
    },
    handleAddCart(item) {
      if ((item.marketType & (MARKETING_TYPE.FLASH_SALE | MARKETING_TYPE.GROUPON | MARKETING_TYPE.BARGAIN | MARKETING_TYPE.REDEEM)) > 0) {
        return this.navigateTo(item.id)
      }
      this.$emit('addcart', item.id)
    }
  },
  created() {
    this.updateProduct()
  }
}
</script>

<style lang="scss" scoped>
.wrap {
  overflow: hidden;
  .scroll {
    overflow-x: auto;
    padding-bottom: 32rpx;
    margin-bottom: -32rpx;
  }
}
.product-list {
  &.list-waterfall.style2 {
    column-count: 2;
    column-gap: 0;

    .item {
      display: block;
      break-inside: avoid;
      width: auto;
      margin-top: -1px;
      padding-top: 1px;
    }

    .product-img image {
      position: relative;
      height: auto;
    }
  }

  .item {
    display: inline-block;
    vertical-align: top;
    overflow: hidden;
    width: 100%;

    .product-wrap {
      overflow: hidden;
      box-sizing: border-box;
      position: relative;
    }

    .product-img {
      position: relative;

      image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: block;
      }
    }

    .tag {
      position: absolute;
      left: 0;
      top: 0;
      width: 80rpx;
      height: 80rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .product-info {
      padding: 0 20rpx;
    }

    .buy-info {
      display: flex;
      min-height: 80rpx;
      align-items: center;
      color: #fb1438;

      &.flex-column {
        flex-direction: column;

        .price {
          display: block;
          line-height: 64rpx;
        }

        .s {
          display: block;
          margin: 0;
          padding-bottom: 16rpx;
        }

        .btn-buy {
          margin-bottom: 20rpx;
        }
      }

      .price {
        font-size: 32rpx;
        color: #212121;
        text {
          font-size: 24rpx;
        }
      }

      .s {
        padding-top: 8rpx;
        margin-left: 16rpx;
        color: #bcbcbc;
        font-size: 24rpx;
        line-height: 36rpx;
        text-decoration: line-through;
      }

      .btn-buy {
        margin-left: auto;
        font-size: 44rpx;
        font-weight: 400;

        &.style5,
        &.style7 {
          font-size: 24rpx;
          background: #f44;
          color: #fff;
          border-radius: 24rpx;
          padding: 0 16rpx;
          line-height: 48rpx;
        }

        &.style6,
        &.style8 {
          font-size: 28rpx;
          border: 2rpx solid #fba7a7;
          color: #f44;
          border-radius: 24rpx;
          padding: 0 12rpx;
          line-height: 44rpx;
        }

        &.style7,
        &.style8 {
          border-radius: 4rpx;
        }
      }

      image {
        width: 120rpx;
        height: 80rpx;
      }

      .btn-limit {
        margin-right: -20rpx;
        margin-left: auto;
        line-height: 80rpx;
        padding: 0 16rpx;
        font-size: 24rpx;
        background: #f44;
        color: #fff;
        white-space: nowrap;
      }
    }

    .h3 {
      font-weight: inherit;
      margin-top: 20rpx;
      font-size: 28rpx;
      line-height: 40rpx;
      word-break: break-all;
      overflow: hidden;
      height: 40rpx;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      white-space: normal;
      &.two-line {
        height: 80rpx;
        -webkit-line-clamp: 2;
      }
      image {
        height: 20px;
        vertical-align: middle;
        position: relative;
        top: -2px;
      }
    }

    .p {
      height: 40rpx;
      font-weight: inherit;
      font-size: 24rpx;
      color: #926969;
      line-height: 40rpx;
      margin-top: 8rpx;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }

  &.style2 .item,
  &.style5 .item:nth-child(3n + 2),
  &.style5 .item:nth-child(3n + 3) {
    width: 50%;

    .btn-buy {
      font-size: 40rpx;

      &.style5,
      &.style7 {
        line-height: 40rpx;
        font-size: 24rpx;
      }

      &.style6,
      &.style8 {
        line-height: 36rpx;
        font-size: 24rpx;
      }
    }
  }

  &.style3 .item,
  &.style6 .item {
    width: 33.33%;

    .price {
      font-size: 28rpx;
    }

    .h3 {
      font-size: 24rpx;
    }

    .btn-buy {
      font-size: 40rpx;

      &.style5,
      &.style7 {
        line-height: 40rpx;
      }

      &.style6,
      &.style8 {
        line-height: 36rpx;
        font-size: 24rpx;
      }
    }
  }

  &.style6 {
    white-space: nowrap;

    .item {
      width: 30%;
    }
  }

  &.style4 {
    .product-img {
      width: 290rpx;
      height: 290rpx;
      padding: 0 !important;
      float: left;
      margin-right: 20rpx;
    }

    .product-info {
      position: relative;
      height: 270rpx;
      margin-left: 290rpx;
    }

    .buy-info {
      position: absolute;
      left: 20rpx;
      right: 20rpx;
      bottom: 0;
    }
  }
}
</style>
