<template>
	<view class="tabs">
		<view class="item" v-for="item in options" :key="item.value" :class="{ active: active === item.value }" :style="{ color: active === item.value ? color : '#5C5C5C' }" @click="handleChange(item.value)">
			{{item.name}}
			<view v-show="active === item.value" :style="{ background: color }"></view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			color: {
				type: String,
				default: '#F52440'
			},
			value: {
				type: [String, Number],
				default: 0
			},
			options: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				active: 0
			}
		},
		computed: {
		},
		watch: {
			value: {
				handler(value) {
					this.active = value
				},
				immediate: true
			}
		},
		methods: {
			handleChange(value) {
				this.$emit('input', value)
				this.$emit('change', value)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tabs {
		display: flex;
		background: #fff;
		line-height: 88rpx;
		.item {
			position: relative;
			flex: 1;
			color: #5C5C5C;
			text-align: center;
			&.active {
				color: #F52440;
				font-weight: bold;
			}
			view {
				position: absolute;
				width: 48rpx;
				height: 8rpx;
				border-radius: 4rpx;
				bottom: 0;
				left: 50%;
				margin-left: -24rpx;
			}
		}
	}
</style>
