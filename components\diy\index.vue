<template>
  <view class="diy-box" v-if="page.header">
    <view :style="{ backgroundColor: page.header.bgColor, overflow: 'hidden' }">
      <view v-for="(item, index) in page.list" :key="index">
        <space v-if="item.type === 'space'" :dataset="item.data" />
        <search-custom v-else-if="item.type === 'search-custom'" :dataset="item.data" />
        <rich-text-custom v-else-if="item.type === 'rich-text-custom'" :dataset="item.data" />
        <product v-else-if="item.type === 'product'" :dataset="item.data" />
        <image-ad v-else-if="item.type === 'image-ad'" :dataset="item.data" />
        <image-nav v-else-if="item.type === 'image-nav'" :dataset="item.data" />
        <cube v-else-if="item.type === 'cube'" :dataset="item.data" />
        <integral v-else-if="item.type === 'integral'" :dataset="item.data" />
        <title-text v-else-if="item.type === 'title-text'" :dataset="item.data" />
        <notice v-else-if="item.type === 'notice'" :dataset="item.data" />
      </view>
    </view>
    <!-- <view class="empty" v-else>
      <image :src="'static/applet/img-paint.png' | formatUrl" mode="aspectFit"></image>
      <view>页面装修中...</view>
    </view> -->
  </view>
</template>

<script>
import { GetDiyInfo } from '@/service/common'
import { mapState } from 'vuex'
import Space from './space'
import SearchCustom from './search'
import RichTextCustom from './richText'
import Product from './product'
import ImageAd from './imageAd'
import ImageNav from './imageNav'
import Integral from './integral'
import Cube from './cube'
import TitleText from './titleText'
import Notice from './notice'

export default {
  components: {
    Space,
    SearchCustom,
    RichTextCustom,
    Product,
    ImageAd,
    ImageNav,
    Cube,
    Integral,
    Notice,
    TitleText
  },
  props: {
    url: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => ({})
    },
    method: {
      type: String,
      default: 'post'
    }
  },
  data() {
    return {
      page: {}
    }
  },
  computed: {
    ...mapState(['setting', 'obsUrl'])
  },
  mounted() {
    if (this.url) {
      this.loadData(false)
    }
  },
  methods: {
    loadData() {
      GetDiyInfo(this.url, this.params, this.method).then((res) => {
		console.log("获取diy详情: ",res)
        const data = res.data
        const settingJson = data.settingJson
        if (!settingJson) return
        const page = JSON.parse(settingJson)
        page.list.map((item) => {
          if (item.type === 'search') {
            item.type = 'search-custom'
          } else if (item.type === 'rich-text') {
            item.type = 'rich-text-custom'
          }
        })
		console.log("获取页面数据：",page)
        this.page = page
        this.$emit('load', { ...page, list: null })
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
