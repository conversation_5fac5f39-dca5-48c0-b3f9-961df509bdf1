// Description: 下单、订单、售后等相关
import request from '@/config/request'
// 订单申请售后
export const ApplyRefund = (data) => request({ url: 'scanMarketing/giftOrderRefund/mini/applyRefund', data, method: 'post' })
// 售后列表
export const GetRefundList = (data) => request({ url: 'scanMarketing/giftOrderRefund/mini/pageList', data, method: 'post' })
// 售后详情
export const GetRefundDetail = (data) => request({ url: 'scanMarketing/giftOrderRefund/mini/detail', data })
// 撤销售后
export const CancelRefund = (refundId) => request({ url: `scanMarketing/giftOrderRefund/mini/cancelRefund?refundId=${refundId}`, method: 'post' })
// 填写售后物流信息
export const FillLogistics = (data) => request({ url: 'scanMarketing/giftOrderRefund/mini/fillLogistics', data, method: 'post' })
// 获取售后日志列表
export const GetListRefundLog = (refundId) => request({ url: `scanMarketing/giftOrderRefund/mini/listRefundLog?refundId=${refundId}` })


// 发起支付
export const PostOrderPay = (data) => request({ url: 'scanMarketing/giftOrder/mini/pay', data, method: 'post' })
// 订单详情
export const GetOrderDetail = (data) => request({ url: 'scanMarketing/giftOrder/mini/detail', data })
// 提交订单
export const SubmitOrder = (data) => request({ url: 'scanMarketing/giftOrder/mini/submit', data, method: 'post' })
// 获取订单列表
export const GetPageList = (data) => request({ url: 'scanMarketing/giftOrder/mini/pageList', data, method: 'post' })
// 关闭订单
export const CloseOrder = (data) => request({ url: 'scanMarketing/giftOrder/mini/close', data, method: 'post' })
// 确认订单
export const GetOrderConfirm = (data) => request({ url: 'scanMarketing/giftOrder/mini/confirm', data, method: 'post' })
// 确认收货
export const ConfirmReceipt = (data) => request({ url: 'scanMarketing/giftOrder/mini/confirmReceipt', data, method: 'post' })
// 获取晒单详情
export const GetShareDetail = (data) => request({ url: 'scanMarketing/giftOrder/mini/getShareDetail', data })
