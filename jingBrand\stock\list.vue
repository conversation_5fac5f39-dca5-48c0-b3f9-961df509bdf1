<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '我的藏酒' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <ul class="tabs">
          <li
            v-for="item in capacityTypes"
            :key="item.value"
            class="tab"
            @click="handleTabClick(item.value)"
          >
            {{ item.label }}
          </li>
          <li class="line" :style="{ left: tabLeft }" />
        </ul>

        <div v-for="item in list" :key="item.id" class="card">
          <div class="header">
            <div class="flex flex-align-center">
              <img
                src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/IWAk8HBXQuCVqdHoc5XID.png"
              />
              <span>{{ item.depositNo }}</span>
            </div>
            <span :class="item.capacity === 0 ? 'text-gray' : 'text-brown'">
              {{ item.capacity === 0 ? '已取完' : '存储中' }}
            </span>
          </div>

          <div class="inner">
            <div class="meta">
              <img :src="item.depositImgList[0] | formatUrl" class="cover" />

              <div>
                <p class="title">{{ item.depositName }}</p>

                <p class="desc">
                  <template v-if="item.capacity">
                    <span class="text-brown">{{ item.capacity | ml2L }}L</span>
                    <span class="text-gray"> /{{ item.totalCapacity | ml2L }}L </span>
                  </template>
                  <template v-else>
                    <span class="text-gray">0L</span>
                  </template>
                </p>
              </div>
            </div>

            <img
              v-if="item.capacity === 0"
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/jlusgYmY8VHCCud5dA_qx.png?x-image-process=image/resize,m_lfit,w_170"
              class="status"
            />
          </div>

          <div class="flex flex-justify-between flex-align-center">
            <p class="text-gray">贮藏日期: {{ item.sealingTime }}</p>

            <button
              v-if="item.totalCapacity && item.capacity !== item.totalCapacity"
              class="btn"
              style="margin-right: 0; color: #0b7d83; border-color: #0b7d83"
              @click="goRecord(item)"
            >
              取酒记录
            </button>
          </div>
        </div>

        <div v-if="!list || !list.length" class="empty">
          <img class="img" :src="'hishop/upload/QNGtoFmiFJYuEd3VCl_UH.png' | formatUrl" alt="" />
          <p class="title">暂无藏酒</p>
          <!--          <span class="btn">去藏酒</span>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import { getStockPage } from '@/service/jingBrand/stock'
import { OSS_PREFIX } from '@/config/system'

export default {
  name: 'list',
  components: { CustomNav },
  data() {
    return {
      OSS_PREFIX,
      statusBarHeight: 168,
      capacityTypes: [
        {
          label: '全部',
          value: null,
        },
        {
          label: '存储中',
          value: 1,
        },
        {
          label: '已取完',
          value: 2,
        },
      ],
      query: {
        pageNo: 1,
        pageSize: 10,
        capacityType: null,
      },
      list: [],
    }
  },
  computed: {
    tabLeft() {
      const index = this.capacityTypes.findIndex(t => t.value === this.query.capacityType)
      const itemWidth = 100 / this.capacityTypes.length
      return `${itemWidth * index + itemWidth / 2}%`
    },
  },
  onLoad() {
    this.init()
  },
  methods: {
    handleTabClick(value) {
      this.query.capacityType = value
      this.init()
    },
    async init() {
      try {
        const res = await getStockPage(this.query)
        this.list = res.data.list
      } catch (e) {
        // this.list = []
      }
    },

    goRecord(item) {
      uni.navigateTo({
        url: `/jingBrand/take/record?depositId=${item.id}&targetCustomId=${item.customId}`,
      })
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  padding-bottom: 24rpx;
  background-color: #f5f4f3;
}
//
//.content {
//  margin: 24rpx;
//  padding: 32rpx 24rpx;
//  background-color: #fff;
//  border-radius: 16rpx;
//}
.card {
  margin: 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background-color: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }
}

.text-brown {
  color: #bc9173;
}

.text-gray {
  color: #b0b0b0;
}

.tabs {
  position: relative;
  display: flex;
  justify-content: space-around;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #faf9f7;

  .tab {
    flex: 1;
    line-height: 88rpx;
    text-align: center;

    //&--active {
    //  color: #0b7d83;
    //  border-bottom: 8rpx solid #0b7d83;
    //}
  }

  .line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 32rpx;
    height: 8rpx;
    background-color: #0b7d83;
    border-radius: 4rpx;
    transform: translateX(-50%);
    transition: left 0.2s ease;
  }
}

.inner {
  position: relative;
  margin: 24rpx auto;
  padding: 24rpx;
  background-color: #f9f9f9;
  border-radius: 4rpx;
}

.meta {
  display: flex;
}

.cover {
  width: 160rpx;
  min-width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
  background-color: #f5f4f3;
}

.title {
  font-size: 28rpx;
}

.desc {
  margin-top: 8rpx;
  font-size: 24rpx;
}

.status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120rpx;
  height: 120rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 280rpx;
    height: 280rpx;
  }

  .title {
    margin-top: 0;
    color: #999;
  }

  .btn {
    padding: 16rpx 32rpx;
    margin-top: 24rpx;
    line-height: 1;
    color: #fff;
    background-color: #0b7d83;
    border-radius: 36rpx;
  }
}
</style>
