<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '订单详情' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <div class="status">
          <img :src="statusIcon" class="status__icon" />
          <span class="status__text">{{ detail.orderStatusDesc }}</span>
        </div>

        <div v-if="detail.deliveryMethod === 1" class="card">
          <div class="header">收货地址</div>
          <div class="flex flex-align-center" style="gap: 24rpx">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/sZkFYaoBh9hBj_bC9OKRk.png?x-image-process=image/resize,m_lfit,w_72"
              style="width: 40rpx; height: 40rpx; min-width: 40rpx"
            />
            <div>
              <p>{{ detail.receiveName }} {{ detail.receivePhone }}</p>
              <p style="margin-top: 12rpx; color: #999">
                {{ detail.province || '' }}{{ detail.city || '' }}{{ detail.area || '' }}
                {{ detail.address || '' }}
              </p>
            </div>
          </div>
        </div>

        <div v-if="detail.deliveryMethod === 2" class="card">
          <div class="header">提货人信息</div>

          <div class="form-item">
            <label class="label">提货人</label>
            <span v-model="form.addressVo.receiveName" type="text" class="value">
              {{ detail.receiveName }}
            </span>
          </div>
          <div class="form-item">
            <label class="label">联系方式</label>
            <span class="value">{{ detail.receivePhone }}</span>
          </div>
          <div class="form-item">
            <label class="label">提货时间</label>
            <span class="value">
              {{ formatDate(detail.estimateTime, 'yyyy-MM-dd') || '--' }}
              <!--              <view class="uni-input">{{ date }}</view>-->
            </span>
          </div>
          <div class="form-item">
            <label class="label">提货地址</label>
            <span class="value">{{ detail.pickupSelfName }}</span>
          </div>
        </div>

        <div v-if="!isSpecialGoodId" class="card">
          <div class="header">取酒坛号</div>

          <ul class="tags">
            <li v-for="item in detail.extractionCustomDepositInfoVos" :key="item.id" class="tag">
              {{ item.depositNo }}
            </li>
          </ul>
        </div>

        <div v-else class="card">
          <div class="header flex-align-center" style="margin-bottom: 0">
            取酒数量

            <span style="color: #666; font-weight: normal; font-size: 16px">
              {{ detail.extractionCustomDepositInfoVos.length }}
            </span>
          </div>
        </div>

        <div v-if="detail.subpackageInfoList && detail.subpackageInfoList.length" class="card">
          <div class="header">产品规格</div>

          <div class="goods">
            <div
              v-for="(subpackage, index) in detail.subpackageInfoList.filter(t => t.number > 0)"
              :key="index"
              class="goods-item"
            >
              <div style="position: relative; border-radius: 4rpx; overflow: hidden">
                <img :src="subpackage.effectImgs | formatUrl" class="cover" mode="aspectFill" />
                <p
                  v-if="subpackage.subpackageType"
                  style="
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 100%;
                    padding-block: 8rpx;
                    background-color: rgba(0, 0, 0, 0.55);
                    text-align: center;
                    font-size: 10px;
                    color: #fff;
                  "
                >
                  {{ getSubpackageTypeLabel(subpackage.subpackageType) }}
                </p>
              </div>

              <div class="meta">
                <p class="title">{{ subpackage.name }}</p>
                <p class="desc">
                  <!--                  {{ subpackage.capacity }}ml * {{ subpackage.num }}-->
                  <template v-if="subpackage.minCreateNum">
                    起订量{{ subpackage.minCreateNum }}{{ subpackage.unit }}
                  </template>
                </p>
              </div>

              <span class="count">x{{ subpackage.number }}</span>
            </div>
          </div>

          <p style="margin-top: 24rpx; text-align: right">
            <span>取酒总量：</span>
            {{ totalCapacity | ml2L }}L
          </p>
        </div>

        <div class="card">
          <div class="header flex-align-center" style="margin-bottom: 0">
            <span>配送方式</span>

            <span style="color: #666; font-weight: normal; font-size: 14px">{{
              detail.deliveryMethod === 1 ? '快递发货' : '上门自提'
            }}</span>
          </div>
        </div>

        <div v-if="detail.buyerMessage" class="card">
          <div class="header">买家留言</div>
          <textarea v-model="detail.buyerMessage" disabled class="textarea" />
          <!--            <span class="textarea-limit">{{ form.buyerMessage.length || 0 }}/50</span>-->
        </div>

        <div class="card">
          <div class="header">详细信息</div>
          <div class="form-item">
            <label class="label">订单编号</label>
            <span class="value">
              {{ detail.orderNo }}
              <span class="tag tag--small" @click="copy">复制</span>
            </span>
          </div>
          <div class="form-item">
            <label class="label">下单时间</label>
            <span class="value">{{ detail.createTime }}</span>
          </div>
        </div>

        <div class="actions">
          <button v-if="detail.orderStatus === 'IN_CONFIRMED'" class="btn" @click="cancelOrder()">
            取消订单
          </button>
          <button
            v-if="detail.logisticsList && detail.logisticsList.length"
            class="btn"
            @click="goPackageList"
          >
            查看物流
          </button>
          <button
            v-if="detail.orderStatus !== 'CLOSED' && promoter.id"
            class="btn btn-primary"
            @click="$refs.promoter.open()"
          >
            联系我们
          </button>
        </div>
      </div>
    </div>

    <promoter ref="promoter" />
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import { numAdd, numMultiply } from '@/common/utils'
import { cancelTake, confirmReceive, getDetail } from '@/service/jingBrand/take'
import Promoter from '@/pages/index/jingBrand/components/Promoter.vue'
import { formatDate } from '../../uni_modules/uni-dateformat/components/uni-dateformat/date-format'
import { mapState } from 'vuex'

export default {
  components: { Promoter, CustomNav },
  data() {
    return {
      statusBarHeight: 168,
      id: null,
      detail: {},
      promoter: {},
    }
  },
  computed: {
    ...mapState(['specialGoodsIds']),
    statusIcon() {
      return {
        IN_CONFIRMED:
          'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/PUGQn8Cy3nu2TurBZaPb4.png?x-image-process=image/resize,m_lfit,w_170',
        IN_PRODUCTION:
          'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/9xCdfnIohP20EB8CsMvAn.png?x-image-process=image/resize,m_lfit,w_170',
        UN_RECEIVED:
          'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/b6Xt2jm09mLbTc-AlPHEs.png?x-image-process=image/resize,m_lfit,w_170',
        FINISHED:
          'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/4pySQddxaUzBKHE5YSaoc.png?x-image-process=image/resize,m_lfit,w_170',
        CLOSED:
          'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/Fz2UJu1rWxQgOhoXGOoI2.png?x-image-process=image/resize,m_lfit,w_170',
      }[this.detail.orderStatus]
    },
    totalCapacity() {
      if (!this.detail.subpackageInfoList || !this.detail.subpackageInfoList.length) return 0
      return this.detail.subpackageInfoList.reduce(
        (prev, current) =>
          numAdd(prev, numMultiply(numMultiply(current.capacity, current.num), current.number)),
        0
      )
    },
    /**
     * 特殊的藏酒id, 不用选择已选坛号, 根据填的取酒数量自动选择坛号
     */
    isSpecialGoodId() {
      return this.specialGoodsIds.includes(parseInt(this.detail?.extractionGoodsInfoVo?.goodsId))
    },
  },
  async onLoad(options) {
    this.id = options.id
    this.init()
    this.promoter = await this.$refs.promoter.initMyPromoter()
  },
  methods: {
    formatDate,
    goPackageList() {
      uni.navigateTo({
        url: `/jingBrand/take/packageList?logisticsList=${JSON.stringify(
          this.detail.logisticsList
        )}&phoneNumber=${this.detail.receivePhone}`,
      })
    },
    getSubpackageTypeLabel(value) {
      const item = [
        {
          label: '木箱分装',
          value: 'WOODEN',
        },
        {
          label: '主题分装',
          value: 'THEME',
        },
        {
          label: '坛藏分装',
          value: 'ALTAR',
        },
        {
          label: '通用分装',
          value: 'COMMON',
        },
        {
          label: '定向分装',
          value: 'DIRECTIONAL',
        },
        {
          label: '简易分装',
          value: 'SIMPLE',
        },
      ].find(t => t.value === value)
      if (!item) return ''
      return item.label
    },
    copy() {
      uni.setClipboardData({
        data: this.detail.orderNo,
        success: () => {
          uni.showToast({
            title: '复制成功',
          })
        },
      })
    },

    async init() {
      const res = await getDetail({ id: this.id })
      this.detail = res.data
    },

    cancelOrder() {
      uni.showModal({
        title: '提示',
        content: '是否确认取消订单？',
        success: res => {
          if (res.confirm) {
            cancelTake({ id: this.detail.id }).then(() => {
              this.init().then(() => {
                uni.showToast({ title: '取消成功' })
              })
            })
          }
        },
      })
    },

    confirm() {
      uni.showModal({
        title: '提示',
        content: '是否确认收货',
        success: res => {
          if (res.confirm) {
            confirmReceive({ id: this.detail.id }).then(() => {
              this.init().then(() => {
                uni.showToast({ title: '确认收货成功' })
              })
            })
          }
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  background-color: #f5f4f3;
  overflow: hidden;
}

.card {
  padding: 32rpx 24rpx;
  margin: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;

  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  min-width: 100%;
  padding: 0;
  margin-top: 24rpx;
}

.tag {
  padding: 16rpx 40rpx;
  list-style: none;
  color: #0b7d83;
  background-color: #f2f8f8;
  border-radius: 36rpx;

  &--small {
    padding: 4rpx 8rpx;
    margin-left: 16rpx;
    font-size: 20rpx;
  }
}

.textarea {
  width: 606rpx;
  height: 152rpx;
  padding: 16rpx 24rpx;
  margin: auto;
  color: #999;
  background-color: #f5f5f5;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 104rpx;
  border-bottom: 1rpx solid #eee;

  .label {
    width: 150rpx;
    color: #262626;
    font-weight: 500;
  }

  .value {
    //flex: 1;
    //height: 104rpx;
  }
}

.goods {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #f9f9f9;

  &-item {
    display: flex;
    justify-content: space-between;

    .cover {
      width: 160rpx;
      height: 160rpx;
    }

    .meta {
      flex: 1;
      margin-left: 16rpx;
    }

    .title {
      font-size: 28rpx;
      font-weight: bold;
    }

    .desc {
      margin-top: 8rpx;
      font-size: 24rpx;
      color: #999;
    }
  }
}

.status {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
  margin: 40rpx auto;

  &__icon {
    width: 48rpx;
    height: 48rpx;
  }

  &__text {
    font-size: 32rpx;
    color: #262625;
  }
}

.actions {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16rpx;
  padding: 0 24rpx;
}

.btn {
  flex: 1;
  margin: 56rpx auto;
}
.btn-primary {
  background-color: #0b7d83;
}
</style>
