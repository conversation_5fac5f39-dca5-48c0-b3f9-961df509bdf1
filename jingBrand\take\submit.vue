<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '提交订单' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <div class="card">
          <div class="header flex-align-center" style="margin-bottom: 0">
            <span>配送方式</span>

            <ul class="tabs">
              <li
                v-for="item in deliveryMethods"
                :key="item.value"
                class="tab"
                :class="['tab', item.value === form.deliveryMethod ? 'tab--active' : '']"
                @click="form.deliveryMethod = item.value"
              >
                {{ item.label }}
              </li>
            </ul>
          </div>
        </div>

        <div v-if="form.deliveryMethod === 1" class="card">
          <div class="header">收货地址</div>

          <div
            v-if="!cacheAddress"
            class="flex flex-align-center flex-justify-center"
            style="gap: 16rpx; padding: 24rpx; background-color: #f9f9f9"
            @click="goAddressList"
          >
            <uni-icons type="plusempty" size="20" />
            添加地址
          </div>

          <div
            v-if="cacheAddress"
            class="flex flex-align-center"
            style="gap: 24rpx"
            @click="goAddressList"
          >
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/sZkFYaoBh9hBj_bC9OKRk.png?x-image-process=image/resize,m_lfit,w_72"
              style="width: 40rpx; height: 40rpx; min-width: 40rpx"
            />
            <div class="flex-grow-1">
              <p>{{ form.addressVo.receiveName }} {{ form.addressVo.receivePhone }}</p>
              <p style="margin-top: 12rpx; color: #999">
                {{ form.addressVo.province }}{{ form.addressVo.city }}{{ form.addressVo.area }}
                {{ form.addressVo.address }}
              </p>
            </div>
            <i class="iconfont icon-edit" />
          </div>
        </div>

        <div v-if="form.deliveryMethod === 2" class="card">
          <div class="header">提货人信息</div>

          <div class="form-item">
            <label class="label">提货人</label>
            <input
              v-model="form.addressVo.receiveName"
              type="text"
              placeholder="请输入姓名"
              maxlength="20"
              class="input"
            />
          </div>
          <div class="form-item">
            <label class="label">联系方式</label>
            <input
              v-model="form.addressVo.receivePhone"
              type="number"
              placeholder="请输入手机号码"
              maxlength="11"
              class="input"
            />
          </div>
          <div class="form-item">
            <label class="label">提货时间</label>
            <picker
              mode="date"
              :value="form.estimateTime"
              :start="startDate"
              :end="endDate"
              class="input"
              @change="onTimeChange"
            >
              <input
                v-model="form.estimateTime"
                type="text"
                disabled
                readonly
                placeholder="请选择提货时间"
                class="input"
              />
              <!--              <view class="uni-input">{{ date }}</view>-->
            </picker>

            <!--
              <input
              type="text"
              placeholder="请选择提货时间"
              class="input"
            />-->
          </div>
          <div class="form-item">
            <label class="label">提货地址</label>
            <picker
              v-if="pickupSelfList.length"
              mode="selector"
              :range="pickupSelfList"
              range-key="pickupSelfName"
              class="input"
              @change="onPickupSelfChange"
            >
              <input
                v-model="form.addressVo.pickupSelfName"
                disabled
                readonly
                type="text"
                class="input"
                placeholder="请选择自提地点"
              />
            </picker>
          </div>
        </div>

        <div v-if="!isSpecialGoodId" class="card">
          <div class="header">取酒坛号</div>

          <ul class="tags">
            <li v-for="item in deposits" :key="item.id" class="tag">{{ item.depositNo }}</li>
          </ul>
        </div>

        <div v-else class="card">
          <div class="header flex-align-center" style="margin-bottom: 0">
            取酒数量

            <span style="color: #666; font-weight: normal; font-size: 16px">
              {{ deposits.length }}
            </span>
          </div>
        </div>

        <div v-if="form.subpackageInfoList && form.subpackageInfoList.length" class="card">
          <div class="header">产品规格</div>

          <div class="goods">
            <div
              v-for="(subpackage, index) in form.subpackageInfoList.filter(t => t.number > 0)"
              :key="index"
              class="goods-item"
            >
              <div style="position: relative; border-radius: 4rpx; overflow: hidden">
                <img :src="subpackage.effectImgs | formatUrl" class="cover" />
                <p
                  v-if="subpackage.subpackageType"
                  style="
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    width: 100%;
                    padding-block: 8rpx;
                    background-color: rgba(0, 0, 0, 0.55);
                    text-align: center;
                    font-size: 10px;
                    color: #fff;
                  "
                >
                  {{ getSubpackageTypeLabel(subpackage.subpackageType) }}
                </p>
              </div>

              <div class="meta">
                <p class="title">{{ subpackage.name }}</p>
                <p class="desc">
                  <!--                  {{ subpackage.capacity }}ml * {{ subpackage.num }}-->
                  <template v-if="subpackage.minCreateNum">
                    起订量{{ subpackage.minCreateNum }}{{ subpackage.unit }}
                  </template>
                </p>
              </div>

              <span class="count">x{{ subpackage.number }}</span>
            </div>
          </div>

          <p style="margin-top: 24rpx; text-align: right">
            <span>取酒总量：</span>
            {{ totalCapacity | ml2L }}L
          </p>
        </div>

        <div class="card">
          <div class="header">买家留言</div>

          <div class="relative">
            <textarea
              v-model="form.buyerMessage"
              placeholder="请填写留言，50字以内"
              maxlength="50"
              class="textarea"
            />
            <span class="textarea-limit">{{ form.buyerMessage.length || 0 }}/50</span>
          </div>
        </div>

        <button class="btn btn-primary" @click="submit">提交订单</button>
      </div>
    </div>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import UniDataPicker from '@/uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker.vue'
import { formatDate } from '@/uni_modules/uni-dateformat/components/uni-dateformat/date-format'
import { createTakeOrder } from '@/service/jingBrand/stock'
import { getPickupSelfList,  } from '@/service/jingBrand/pickup-self'
import { numAdd, numMultiply } from '@/common/utils'
import { mapActions, mapMutations, mapState } from 'vuex'
import request from '@/config/request'
import { GetAddressList } from '@/service/common'

const date = new Date()
let year = date.getFullYear()
let month = date.getMonth() + 1
let day = date.getDate()
const format = 'yyyy-MM-dd'
const startDate = formatDate(date.setDate(day + 7), format)
const endDate = formatDate(date.setFullYear(year + 10), format)

export default {
  components: { UniDataPicker, CustomNav },
  data() {
    return {
      statusBarHeight: 168,
      showPickupSelf: true,
      form: {
        goodsId: null,
        goodsName: '',
        targetCustomId: null,
        deliveryMethod: 1,
        addressVo: {
          // 省份
          province: '',
          // 市
          city: '',
          // 区
          area: '',
          // 详细地址
          address: '',

          // 自提点名称
          pickupSelfName: '',

          // 收货人、提货人
          receiveName: '',
          // 收货联系电话
          receivePhone: '',
        },
        subpackageInfoList: [],
        buyerMessage: '',
        estimateTime: startDate,
      },
      deposits: [],
      // deliveryMethods: ,
      startDate,
      endDate,
      pickupSelfList: [],
    }
  },
  computed: {
    ...mapState(['cacheAddress', 'specialGoodsIds']),
    deliveryMethods() {
      if (this.showPickupSelf) {
        return [
          {
            label: '快递发货',
            value: 1,
          },
          {
            label: '上门自提',
            value: 2,
          },
        ]
      } else {
        return [
          {
            label: '快递发货',
            value: 1,
          },
        ]
      }
    },
    totalCapacity() {
      return this.form.subpackageInfoList.reduce(
        (prev, current) =>
          numAdd(prev, numMultiply(numMultiply(current.capacity, current.num), current.number)),
        0
      )
    },
    /**
     * 特殊的藏酒id, 不用选择已选坛号, 根据填的取酒数量自动选择坛号
     */
    isSpecialGoodId() {
      return this.specialGoodsIds.includes(parseInt(this.form.goodsId))
    },
  },
  watch: {
    cacheAddress(value) {
      if (!value || this.form.deliveryMethod !== 1) return
      this.form.addressVo = value
    },
  },
  onLoad() {
    const { goodsId, goodsName, targetCustomId, deposits, subpackageInfoList } =
      uni.getStorageSync('takeOrderSubmit')
    this.deposits = deposits
    this.form.goodsId = goodsId
    this.form.goodsName = goodsName
    this.form.targetCustomId = targetCustomId
    this.form.subpackageInfoList = subpackageInfoList

    request({
      url: 'jingBrand/systemSetting/settingQuery',
    }).then(res => {
      this.showPickupSelf = res.data.izPickupSelf
    })

    getPickupSelfList().then(res => {
      if (res.data.list && res.data.list.length) {
        this.pickupSelfList = res.data.list.filter(t => t.status)
      } else {
        this.pickupSelfList = []
      }
    })

    GetAddressList({
      pageSize: 9999,
      pageNo: 1,
    }).then(res => {
      const { list } = res.data
      if (list && list.length) {
        const defaultItem = list.find(item => item.izDefault)
        if (defaultItem) {
          this.setCacheAddressAction(defaultItem)
        }
      }
    })
  },
  methods: {
    ...mapMutations(['SET_TAB_BAR_SELECTED']),
    ...mapActions(['setCacheAddressAction']),

    getSubpackageTypeLabel(value) {
      const item = [
        {
          label: '木箱分装',
          value: 'WOODEN',
        },
        {
          label: '主题分装',
          value: 'THEME',
        },
        {
          label: '坛藏分装',
          value: 'ALTAR',
        },
        {
          label: '通用分装',
          value: 'COMMON',
        },
        {
          label: '定向分装',
          value: 'DIRECTIONAL',
        },
        {
          label: '简易分装',
          value: 'SIMPLE',
        },
      ].find(t => t.value === value)
      if (!item) return ''
      return item.label
    },
    goAddressList() {
      uni.navigateTo({
        url: `/pages/address/list?type=2`,
      })
    },

    onTimeChange(evt) {
      this.form.estimateTime = evt.detail.value
    },
    onPickupSelfChange(evt) {
      const index = evt.detail.value || 0
      const item = this.pickupSelfList[index]
      this.form.addressVo.pickupSelfName = item.pickupSelfName
      this.form.addressVo.address = item.pickupSelfAddress
      this.form.addressVo.province = item.province
      this.form.addressVo.provinceCode = item.provinceId.toString()
      this.form.addressVo.city = item.city
      this.form.addressVo.cityCode = item.cityId.toString()
      this.form.addressVo.area = item.area
      this.form.addressVo.areaCode = item.areaId.toString()
    },
    async submit() {
      if (this.form.deliveryMethod === 1) {
        if (!this.form.addressVo.receivePhone) {
          uni.showToast({
            title: '请选择收货地址',
            icon: 'none',
          })
          return
        }
      }

      if (this.form.deliveryMethod === 2) {
        if (!this.form.addressVo.receiveName) {
          uni.showToast({
            title: '请输入提货人',
            icon: 'none',
          })
          return
        }

        if (!this.form.addressVo.receivePhone) {
          uni.showToast({
            title: '请联系方式',
            icon: 'none',
          })
          return
        }

        if (!this.form.addressVo.pickupSelfName) {
          uni.showToast({
            title: '请选择提货地址',
            icon: 'none',
          })
          return
        }
      }
			
      createTakeOrder({
        ...this.form,
        estimateTime: this.form.estimateTime + ' 00:00:00',
        depositIds: this.deposits.map(t => t.id),
      }).then(() => {
        wx.requestSubscribeMessage({
          tmplIds: [
            // 取酒下单提醒
            'yyKGHfkAz-TW6d-yqznj3LNskm3xlgGBBpBFBKC9dRA',
            // 取酒确认提醒
            'Xrf7qw4nmyEjIN2unPHel_ZCUsQ3wmekk2oAv-rLaLE',
            // 取酒发货提醒
            'TRsoUuOmOS7GkSTqQSl_cGlc-obHbZfyyLSKPfV-jVc',
          ],
          success(res) {
            console.log('消息订阅成功', res)
          },
          fail(e) {
            console.log('消息订阅失败', e)
          },
        })
        uni.showToast({
          title: '提交成功',
          icon: 'success',
        })
        this.SET_TAB_BAR_SELECTED('/pages/index/jingBrand/stock')
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1500)

        // request({
        //   url: 'jingBrand/msgSetting/pageList',
        //   data: {
        //     pageNo: 1,
        //     pageSize: 50,
        //   },
        //   method: 'POST',
        // }).then(({ data }) => {
        //   const tmplIds = data.list
        //     .filter(t => t.remind === '会员' && t.msgType.startWith('取酒'))
        //     .map(t => t.wxId)
        //   console.log(tmplIds)
        //
        // })
      })
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  background-color: #f5f4f3;
  overflow: hidden;
}

.card {
  padding: 32rpx 24rpx;
  margin: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;

  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}

.tabs {
  display: flex;
  padding: 8rpx;
  list-style: none;
  background-color: #f9f9f9;
  border-radius: 44rpx;
}

.tab {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  border-radius: 44rpx;

  &--active {
    color: #fff;
    background-color: #0b7d83;
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  min-width: 100%;
  padding: 0;
  margin-top: 24rpx;
}

.tag {
  padding: 16rpx 40rpx;
  list-style: none;
  color: #0b7d83;
  background-color: #f2f8f8;
  border-radius: 36rpx;
}

.textarea {
  width: 606rpx;
  height: 152rpx;
  padding: 16rpx 24rpx;
  margin: auto;
  color: #999;
  background-color: #f5f5f5;

  &-limit {
    position: absolute;
    bottom: 20rpx;
    right: 32rpx;
    color: #999;
  }
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 104rpx;
  border-bottom: 1rpx solid #eee;

  .label {
    width: 150rpx;
    color: #262626;
    font-weight: 500;
  }

  .input {
    flex: 1;
    height: 104rpx;
  }
}

.goods {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 24rpx;
  background-color: #f9f9f9;

  &-item {
    display: flex;
    justify-content: space-between;

    .cover {
      width: 160rpx;
      height: 160rpx;
    }

    .meta {
      flex: 1;
      margin-left: 16rpx;
    }

    .title {
      font-size: 28rpx;
      font-weight: bold;
    }

    .desc {
      margin-top: 8rpx;
      font-size: 24rpx;
      color: #999;
    }
  }
}

.btn-primary {
  padding: 24rpx 0;
  margin: 56rpx 24rpx;
  line-height: 1;
  background-color: #0b7d83;
}
</style>
