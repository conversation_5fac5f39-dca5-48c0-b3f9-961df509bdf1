import request from '@/config/request'

// 获取话题内容评论列表
export const GetCommentList = (data) => request({ url: 'fansClub/mini/community/content/comment/pageList', data, method: 'post' })
// 获取话题内容详情
export const GetContentDetail = (id) => request({ url: `fansClub/mini/community/content/detail?id=${id}` })
// 修改内容
export const UpdateContent = (data) => request({ url: 'fansClub/mini/community/content/edit', data, method: 'post' })
// 点赞内容
export const LikeContent = (data) => request({ url: 'fansClub/mini/community/content/like', data, method: 'post' })
// 取消点赞内容
export const CancelLikeContent = (data) => request({ url: 'fansClub/mini/community/content/unlike', data, method: 'post' })
// 发布内容
export const PublishContent = (data) => request({ url: 'fansClub/mini/community/content/publish', data, method: 'post' })
// 删除内容
export const DeleteContent = (data) => request({ url: 'fansClub/mini/community/content/remove', data, method: 'post' })
// 评论内容
export const CommentContent = (data) => request({ url: 'fansClub/mini/community/content/review', data, method: 'post' })
// 获取内容列表
export const GetContentList = (data) => request({ url: 'fansClub/mini/community/content/pageList', data, method: 'post' })
// 商品列表
export const GetProductList = (data) => request({ url: 'fansClub/mini/community/product/pageList', data, method: 'post' })
// 获取话题详情
export const GetTopicDetail = (id) => request({ url: `fansClub/mini/community/topic/detail?id=${id}` })
// 获取首页最热话题
export const GetHotTopic = (data) => request({ url: 'fansClub/mini/community/topic/mostHot', data })
// 获取话题列表
export const GetTopicList = (data) => request({ url: 'fansClub/mini/community/topic/pageList', data, method: 'post' })
// 分享内容
export const ContentShare = (data) => request({ url: 'fansClub/mini/community/content/share', data, method: 'post' })
// 获取我发布的内容列表
export const GetMyContentList = (data) => request({ url: 'fansClub/mini/community/content/mineList', data, method: 'post' })
