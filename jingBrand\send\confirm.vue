<template>
  <div class="stock-send-confirm">
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: isReceive ? '领取赠酒' : '赠酒确认' }"
      @init="statusBarHeight = $event"
    />

    <div
      v-if="detail.toCustomPhone"
      class="container"
      :style="{ paddingTop: statusBarHeight + 'rpx', overflow: 'hidden' }"
    >
      <div class="content">
        <img :src="detail.depositImgList[0] | formatUrl" class="cover" />
        <p class="title">{{ detail.depositName }}</p>

        <div class="form-item">
          <span class="label">坛号</span>
          <span class="value">{{ detail.deposits.map(t => t.depositNo).join('、') }}</span>
        </div>

        <div class="form-item">
          <span class="label">受赠人</span>
          <span class="value">{{ detail.toCustomName }}</span>
        </div>

        <div class="form-item">
          <span class="label">手机号</span>
          <span class="value">{{ detail.toCustomPhone }}</span>
        </div>

        <template v-if="isReceive && detail.smsFlag">
          <div class="divider" />
          <div class="form-item">
            <span class="label">手机号</span>
            <span class="value">{{ detail.toCustomPhone }}</span>
          </div>

          <div class="form-item">
            <span class="label">验证码</span>
            <div class="flex ">
              <input v-model="smsCode" type="number" placeholder="请输入验证码" />
              <span style="width: 140rpx; text-align: center;color: #0b7d83" @click="getSmsCode">
                {{ timer ? `${timeout}s` : '获取验证码' }}
              </span>
            </div>
          </div>
        </template>

        <template v-if="!isReceive">
          <div class="divider" />

          <div class="tips">
            <p>赠送说明：</p>
            <ul>
              <li>1. 选择要赠送的藏酒坛号，输入受赠人名字和电话，微信分享链接给好友；</li>
              <li>2. 好友点击“确认领取”，即赠酒成功；</li>
              <li>3. 在个人中心“转赠记录”可查看转赠情况；</li>
              <li>
                4. 好友需在赠酒发起后{{ validityPeriod }}小时内确认领取，超过{{
                  validityPeriod
                }}小时未领取赠酒将自动退回。
              </li>
            </ul>
          </div>
        </template>

        <div
          v-if="isReceive && (detail.expiredFlag || ['ACQUIRE', 'CLOSED'].includes(detail.status))"
          style="margin-top: 80rpx; color: #999"
        >
          <div class="divider" />

          <span
            v-if="detail.status === 'ACQUIRE'"
            class="flex flex-justify-center flex-align-center"
          >
            领取成功

            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/tScHLfpLo8tavZhFNSnuc.png"
              style="width: 120rpx; height: 120rpx"
            />
          </span>
          <span
            v-if="detail.expiredFlag || detail.status === 'CLOSED'"
            class="flex flex-justify-center flex-align-center"
          >
            当前转赠已失效或撤销
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/LG2fXxfWu4d72fDVumrJc.png"
              style="width: 120rpx; height: 120rpx"
            />
          </span>
        </div>
      </div>

      <button v-if="!isReceive" class="btn btn-primary" open-type="share">确认转发</button>

      <template v-else>
        <button v-if="detail.status === 'IN_TRANSFER'" class="btn btn-primary" @click="confirmGet">
          确认领取
        </button>

        <button v-else class="btn btn-primary" @click="goHome">
          去首页
        </button>
      </template>
    </div>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import {
  createSendWineOrder,
  getSendWineDetail,
  getSMSCode,
  receiveSendWine,
} from '@/service/jingBrand/send'
import { getStockDetail } from '@/service/jingBrand/stock'
import { mapMutations } from 'vuex'
import request from '@/config/request'
import { numMultiply } from '@/common/utils'

export default {
  name: 'confirm',
  components: { CustomNav },
  data() {
    return {
      statusBarHeight: 168,
      id: null,
      goodsId: null,
      customerId: null,
      detail: {
        depositName: '',
        // depositNo: '',
        status: '',
        smsFlag: false,
        toCustomPhone: '',
        toCustomName: '',
        depositImgList: [],
        expiredFlag: false,
      },
      form: {
        // depositId: null,
        // depositNo: null,
        // depositIds: [],
        deposits: [],
        toCustomName: '',
        toCustomPhone: '',
      },
      isReceive: false,
      timer: null,
      timeout: 60,
      smsCode: '',
      validityPeriod: 0,
    }
  },
  onShow() {},
  // 这里的id是赠酒订单id, transferId
  onLoad({ id, isReceive, goodsId, customerId }) {
    this.id = id
    this.goodsId = goodsId
    this.customerId = customerId
    this.isReceive = isReceive === 'true'
    this.initPage()
  },
  onShareAppMessage() {
    const title = '给您赠送一坛藏酒，赶紧领取'

    const promise = new Promise(async (resolve, reject) => {
      if (!this.id) {
        // console.log('开始赠酒创建订单')
        // console.log({
        //   toCustomName: this.form.toCustomName,
        //   toCustomPhone: this.form.toCustomPhone,
        //   depositIds: this.from.deposits.map(t => t.id),
        // })
        const { code, data } = await createSendWineOrder({
          toCustomName: this.form.toCustomName,
          toCustomPhone: this.form.toCustomPhone,
          depositIds: this.form.deposits.map(t => t.id),
        })
        if (code === '200') {
          this.id = data
        }
      }

      resolve({
        title,
        path: `/jingBrand/send/confirm?isReceive=true&id=${this.id}`,
      })
    })

    this.SET_TAB_BAR_SELECTED('/pages/index/jingBrand/stock')
    setTimeout(() => {
      uni.showToast({
        title: '转发成功',
        icon: 'success',
      })
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 500)
    return {
      title,
      path: `/jingBrand/send/confirm?isReceive=true&id=${this.id}`,
      promise,
    }
  },
  methods: {
    ...mapMutations(['SET_TAB_BAR_SELECTED']),

    async initPage() {
      wx.showLoading({
        title: '加载中...',
        mask: true,
      })
      try {
        await Promise.all([this.initDetail(), this.initSystemSetting()])
      } finally {
        wx.hideLoading()
      }
    },

    async initDetail() {
      if (this.id) {
        const { code, data } = await getSendWineDetail({
          transferId: this.id,
        })
        if (code === '200') {
          data.deposits = data.details.map(t => ({ id: t.depositId, depositNo: t.depositNo }))
          this.detail = data
        }
      } else {
        const form = uni.getStorageSync('sendOrderSubmit')
        this.form = form
        this.detail.toCustomName = form.toCustomName
        this.detail.toCustomPhone = form.toCustomPhone
        this.detail.deposits = form.deposits

        const { data } = await getStockDetail({
          goodsId: this.goodsId,
          targetCustomId: this.customerId,
        })
        this.detail.depositName = data.name
        this.detail.depositImgList = data.imgList
      }
    },

    async initSystemSetting() {
      const { data } = await request({
        url: 'jingBrand/systemSetting/settingQuery',
        showLoading: true,
      })
      if (data.bestowExtractNum) {
        this.validityPeriod = numMultiply(data.bestowExtractNum, 24)
      }
    },

    async getSmsCode() {
      if (!this.detail.toCustomPhone) {
        uni.showToast({
          title: '手机号为空',
          icon: 'none',
        })
        return
      }

      if (this.timer) return

      getSMSCode({
        phone: this.detail.toCustomPhone,
      }).then(() => {
        this.timer = setInterval(() => {
          this.timeout--

          if (this.timeout <= 0) {
            clearInterval(this.timer)
            this.timer = null
            this.timeout = 60
          }
        }, 1000)
      })
    },
    confirmGet() {
      if (this.detail.smsFlag && !this.smsCode) {
        return uni.showToast({
          title: '请输入验证码',
          icon: 'none',
        })
      }

      receiveSendWine({
        smsCode: this.smsCode,
        transferId: this.id,
      }).then(() => {
        this.initDetail()
      })
    },

    goHome() {
      this.SET_TAB_BAR_SELECTED('/pages/index/basicSystem/home')
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  background-color: #f5f4f3;
}

.content {
  margin: 24rpx;
  padding: 32rpx 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.cover {
  display: block;
  width: 320rpx;
  height: 320rpx;
  margin: 16rpx auto 40rpx;
}

.title {
  line-height: 48rpx;
  text-align: center;
  font-weight: 600;
  font-size: 36rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 88rpx;
  //border-bottom: 1rpx solid #eee;

  .label {
    width: 150rpx;
    color: #262626;
    font-weight: 500;
    white-space: nowrap;
    margin-right: 60rpx;
  }

  .value {
    font-weight: 400;
  }
}

.tips {
  padding: 24rpx;
  margin-top: 32rpx;
  background-color: #f9f9f9;

  ul {
    margin: 0;
    margin-top: 16rpx;
    padding: 0;
  }

  li {
    margin-bottom: 16rpx;
    list-style: none;
    font-size: 20rpx;
    color: #999;
  }

  img {
    width: 120rpx;
    height: 120rpx;
  }
}

.btn-primary {
  padding: 24rpx;
  margin: 100rpx 24rpx;
  line-height: 40rpx;
  background-color: #0b7d83;
}

.divider {
  position: relative;
  margin-block: 60rpx;
  border-bottom: 1px dashed #eee;
  background-color: transparent;

  $size: 48rpx;
  &:before,
  &:after {
    content: '';
    position: absolute;
    bottom: -$size / 2;
    width: $size;
    height: $size;
    border-radius: 50%;
    background-color: #f5f5f5;
  }

  &:before {
    left: -$size;
  }

  &:after {
    right: -$size;
  }
}
</style>
