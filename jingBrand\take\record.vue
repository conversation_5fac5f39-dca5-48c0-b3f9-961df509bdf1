<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '取酒记录' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <div v-for="item in list" :key="item.id" class="card">
          <div class="inner">
            <div class="meta">
              <img :src="item.depositImgList[0] | formatUrl" class="cover" />

              <div class="flex-grow-1">
                <p class="title">{{ item.depositName }}</p>

                <p
                  v-for="subpackage in item.extractionSubpackageInfoList"
                  :key="subpackage.id"
                  class="desc"
                >
                  <span>{{ subpackage.name }}</span>
                  <span>x{{ subpackage.number }}</span>
                </p>
              </div>
            </div>

            <img
              v-if="item.capacity === 0"
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/jlusgYmY8VHCCud5dA_qx.png?x-image-process=image/resize,m_lfit,w_170"
              class="status"
            />
          </div>

          <div class="flex flex-justify-between flex-align-center">
            <span class="text-gray">{{ item.createTime || '--' }}</span>

            <span>
              容量总计：
              <span class="text-brown">{{ item.totalCapacity | ml2L }}L</span>
            </span>
          </div>
        </div>

        <div v-if="!list || !list.length" class="empty">
          <img
            class="img"
            :src="
              `${OSS_PREFIX}hishop/upload/9lUMYzegJh9LgY4yDCCVS.png?x-image-process=image/resize,m_lfit,w_170`
            "
            alt=""
          />
          <p class="title">取酒记录为空</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import { getTakeRecord } from '@/service/jingBrand/stock'
import { OSS_PREFIX } from '@/config/system'

export default {
  name: 'list',
  components: { CustomNav },
  data() {
    return {
      OSS_PREFIX,
      statusBarHeight: 168,
      query: {
        depositId: null,
        targetCustomId: null,
      },
      list: [],
    }
  },
  onLoad(options) {
    this.query.depositId = options.depositId
    this.query.targetCustomId = options.targetCustomId
    this.init()
  },
  methods: {
    async init() {
      try {
        const res = await getTakeRecord(this.query)
        this.list = res.data
      } catch (e) {
        // this.list = []
      }
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  padding-bottom: 24rpx;
  background-color: #f5f4f3;
}
//
//.content {
//  margin: 24rpx;
//  padding: 32rpx 24rpx;
//  background-color: #fff;
//  border-radius: 16rpx;
//}
.card {
  margin: 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background-color: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }
}

.text-brown {
  color: #bc9173;
}

.text-gray {
  color: #b0b0b0;
}

.tabs {
  position: relative;
  display: flex;
  justify-content: space-around;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #faf9f7;

  .tab {
    flex: 1;
    line-height: 88rpx;
    text-align: center;

    //&--active {
    //  color: #0b7d83;
    //  border-bottom: 8rpx solid #0b7d83;
    //}
  }

  .line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 32rpx;
    height: 8rpx;
    background-color: #0b7d83;
    border-radius: 4rpx;
    transform: translateX(-50%);
    transition: left 0.2s ease;
  }
}

.inner {
  position: relative;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #f9f9f9;
  border-radius: 4rpx;
}

.meta {
  display: flex;
}

.cover {
  width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
  background-color: #f5f4f3;
}

.title {
  margin-bottom: 24rpx;
  font-size: 28rpx;
}

.desc {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120rpx;
  height: 120rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 280rpx;
    height: 280rpx;
  }

  .title {
    margin-top: 0;
    color: #999;
  }
}
</style>
