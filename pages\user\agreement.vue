<template>
  <view class="container-root" v-if="isInit">
    <u-parse :content="dataset" :imageProp="{ lazyLoad: true, domain: obsUrl }" />
  </view>
</template>

<script>
import uParse from '@/components/uParse/parse.vue'
import { GetAgreement } from '@/service/common'
import { mapState } from 'vuex'

export default {
  components: {
    uParse,
  },
  data() {
    return {
      isInit: false,
      dataset: '',
    }
  },
  computed: {
    ...mapState(['obsUrl']),
  },
  onLoad(options) {
    const type = parseInt(options.type || 1)
    let title = ''
    switch (type) {
      case 1:
        title = '用户协议'
        break
      case 2:
        title = '隐私政策'
        break
      case 3:
        title = '赠酒免责条款'
        break
    }
    uni.setNavigationBarTitle({
      title,
    })
    GetAgreement(type).then(res => {
      this.isInit = true
      this.dataset = res.data
    })
  },
}
</script>

<style lang="scss" scoped>
.container-root {
  padding: 24rpx;
  overflow: hidden;
  background-color: #fff;
}
.rich-text {
  box-sizing: border-box;
}
</style>
