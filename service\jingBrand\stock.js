import request from '@/config/request'

// 获取藏酒列表
export const getStockList = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/myList',
    data,
  })

// 获取藏酒详情
export const getStockDetail = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/getDepositInfo',
    data,
  })

// 获取酒坛编号
export const getJarCodes = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/getDepositNumberPull',
    data,
  })

export const createTakeOrder = data =>
  request({
    url: 'jingBrand/mini/warehouseWine/extractionOrder',
    data,
    method: 'post',
  })

export const getStockPage = data =>
  request({
    url: 'jingBrand/mini/personal/pageList',
    data,
    method: 'post',
  })

export const getTakeRecord = data =>
  request({
    url: 'jingBrand/mini/personal/extractionLog',
    data,
  })

export const getCustomerName = () =>
  request({
    url: 'jingBrand/mini/warehouseWine/getCustomName',
  })

export const getSpecialGoodsIds = () =>
  request({
    url: 'jingBrand/goods/getGoodsIdByConfigure',
  })
	
// export ?id=742
export const checkExtraction = (id) =>
  request({
    url: 'jingBrand/mini/warehouseWine/checkExtraction',
    data: {
      id
    }
  })