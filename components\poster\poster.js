import utils from './utils.js';
import { base64ToPath } from './image-tools.js';
import { QRCodeAlg } from '../qrcode/qrcode.js';
const ShreUserPosterBackgroundKey = 'ShrePosterBackground_'; // 背景图片缓存名称前缀
const idKey = 'QSSHAREPOSTER_IDKEY'; //drawArray自动生成的idkey
var nbgScale = 1;
// export default 
function getSharePoster(obj) {
	let {
		type,
		formData,
		background,
		posterCanvasId,
		backgroundImage,
		reserve,
		textArray,
		drawArray,
		qrCodeArray,
		imagesArray,
		setCanvasWH,
		setCanvasToTempFilePath,
		canvas2image,
		setDraw,
		bgScale,
		Context,
		_this,
		delayTimeScale,
		drawDelayTime,
		draw
	} = obj;
	
	uni.showLoading({
		title: '生成中'
	});
	return new Promise(async (rs, rj) => {
		try {
			if (!Context) {
				Context = uni.createCanvasContext(posterCanvasId, (_this || null));
			}
			let bgObj;
			if (background && background.width && background.height) {
				bgObj = background;
			} else {
				bgObj = await getShreUserPosterBackground({
					backgroundImage,
					type,
					formData
				});
			}
			
			bgScale = bgScale || nbgScale;
			// #ifdef H5
			const res = uni.getSystemInfoSync()
			const maxSize = 1365
			if (res.platform === 'ios') {
				if (bgObj.width > bgObj.height) {
					if (bgObj.width > maxSize) {
						bgScale = maxSize / bgObj.width
					}
				} else {
					if (bgObj.height > maxSize) {
						if (bgObj.height > maxSize) {
							bgScale = maxSize / bgObj.height
						}
					}
				}
			}
			// #endif
			bgObj.width = bgObj.width * bgScale;
			bgObj.height = bgObj.height * bgScale;
			Context.scale(bgScale, bgScale);

			const params = {
				bgObj,
				type,
				bgScale,
				getBgObj: function() {
					return params.bgObj;
				},
				setBgObj: function(newBgObj) {
					const n = {
						...params.bgObj,
						...newBgObj
					};
					params.bgObj = n;
					bgObj = n;
				}
			};
			if (imagesArray) {
				if (typeof(imagesArray) == 'function')
					imagesArray = imagesArray(params);
				imagesArray = await setImage(imagesArray);
			}
			if (textArray) {
				if (typeof(textArray) == 'function')
					textArray = textArray(params);
				textArray = setText(Context, textArray);

			}
			if (qrCodeArray) {
				if (typeof(qrCodeArray) == 'function')
					qrCodeArray = qrCodeArray(params);
				for (let i = 0; i < qrCodeArray.length; i++) {
					if (qrCodeArray[i].image)
						qrCodeArray[i].image = await utils.downloadFile_PromiseFc(qrCodeArray[i].image);
				}
			}
			if (drawArray) {
				if (typeof(drawArray) == 'function') {
					drawArray = drawArray(params);
				}
				if (utils.isPromise(drawArray)) {
					drawArray = await drawArray;
				}

				if (utils.isArray(drawArray) && drawArray.length > 0) {
					// let hasAllInfoCallback = false;
					const addDrawArray = [];
					for (let i = 0; i < drawArray.length; i++) {
						const drawArrayItem = drawArray[i];
						drawArrayItem[idKey] = i;
						let newData;
						let addDraw = false;
						switch (drawArrayItem.type) {
							case 'image':
								newData = await setImage(drawArrayItem);
								break;
							case 'text':
								newData = setText(Context, drawArrayItem, params.bgObj);
								
								break;
							case 'qrcode':
								if (drawArrayItem.image)
									newData = {
										image: await utils.downloadFile_PromiseFc(drawArrayItem.image)
									};
								break;
							case 'custom':
								break;
							case 'fillrect':
								break;
							case 'strokeRect':
								break;
							case 'roundStrokeRect':
								break;
							case 'roundFillRect':
								break;
							default:
								break;
						}
						if (!addDraw && newData && utils.isObject(newData)) {
							drawArray[i] = {
								...drawArrayItem,
								...newData
							}
						};
					}

					const drawArray_copy = [...drawArray];
					drawArray_copy.sort((a, b) => {
						const a_serialNum = !utils.isUndef(a.serialNum) && !utils.isNull(a
							.serialNum) ? Number(a.serialNum) : Number.NEGATIVE_INFINITY;
						const b_serialNum = !utils.isUndef(b.serialNum) && !utils.isNull(b
							.serialNum) ? Number(b.serialNum) : Number.NEGATIVE_INFINITY;
						return a_serialNum - b_serialNum;
					})

					for (let i = 0; i < drawArray_copy.length; i++) {
						const item = {
							...drawArray_copy[i]
						};
						const item_idKey = item[idKey];
						const ind = drawArray.findIndex(it => it[idKey] == item_idKey);
						if (-1 == ind) break;
						if (utils.isFn(item.allInfoCallback)) {
							let newData = item.allInfoCallback({
								drawArray
							});
							if (utils.isPromise(newData)) newData = await newData;

							if (drawArray[ind].type === 'text' && newData.size) {
								const textLength = countTextLength(Context, {
									text: newData.text || drawArray[ind].text,
									size: newData.size
								});
								newData.textLength = textLength;
							}
							drawArray[ind] = {
								...item,
								...newData
							};
						}
						if (drawArray[ind].type === 'text') {
							const setLineFeedResult = setLineFeed(Context, drawArray[ind], params.bgObj);
							if (utils.isArray(setLineFeedResult)) {
								setLineFeedResult.forEach((ite, index) => {
									ite[idKey] = drawArray.length + index;
									ite.allInfoCallback = null;
								})
								drawArray.splice(ind, 1, ...setLineFeedResult);
							} else {
								drawArray.splice(ind, 1, setLineFeedResult);
							}
						}

					}
				}
			}
			drawArray.sort((a, b) => {
				const a_zIndex = !utils.isUndef(a.zIndex) && !utils.isNull(a
					.zIndex) ? Number(a.zIndex) : Number.NEGATIVE_INFINITY;
				const b_zIndex = !utils.isUndef(b.zIndex) && !utils.isNull(b
					.zIndex) ? Number(b.zIndex) : Number.NEGATIVE_INFINITY;
				return a_zIndex - b_zIndex;
			});
			if (setCanvasWH && typeof(setCanvasWH) == 'function') {
				await new Promise((resolve, reject) => {
					setCanvasWH(params);
					setTimeout(() => {
						resolve();
					}, 50)
				})
			}
			const poster = await drawShareImage({
				Context,
				type,
				posterCanvasId,
				reserve,
				drawArray,
				textArray,
				imagesArray,
				bgObj,
				qrCodeArray,
				setCanvasToTempFilePath,
				setDraw,
				bgScale,
				_this,
				delayTimeScale,
				drawDelayTime,
				canvas2image,
				draw
			});
			rs({
				bgObj,
				poster,
				type
			});
		} catch (e) {
			//TODO handle the exception
			rj(e);
		}
	});
}
function drawShareImage(obj) { //绘制海报方法
	let {
		Context,
		type,
		posterCanvasId,
		reserve,
		bgObj,
		drawArray,
		textArray,
		qrCodeArray,
		imagesArray,
		setCanvasToTempFilePath,
		setDraw,
		bgScale,
		_this,
		delayTimeScale,
		drawDelayTime,
		canvas2image,
		draw
	} = obj;
	const params = {
		Context,
		bgObj,
		type,
		bgScale
	};
	delayTimeScale = delayTimeScale!==undefined?delayTimeScale:15;
	drawDelayTime = drawDelayTime!==undefined?drawDelayTime:100;
	return new Promise((rs, rj) => {
		try {
			if (bgObj && bgObj.path) {
				Context.drawImage(bgObj.path, 0, 0, bgObj.width, bgObj.height);
			} else {
				if (bgObj.backgroundColor) {
					Context.setFillStyle(bgObj.backgroundColor);
					Context.fillRect(0, 0, bgObj.width, bgObj.height);
				}
			}

			if (imagesArray && imagesArray.length > 0)
				drawImage(Context, imagesArray);

			if (setDraw && typeof(setDraw) == 'function') setDraw(params);

			if (textArray && textArray.length > 0)
				drawText(Context, textArray, bgObj);

			if (qrCodeArray && qrCodeArray.length > 0) {
				for (let i = 0; i < qrCodeArray.length; i++) {
					drawQrCode(Context, qrCodeArray[i]);
				}
			}

			if (drawArray && drawArray.length > 0) {
				for (let i = 0; i < drawArray.length; i++) {
					const drawArrayItem = drawArray[i];
					switch (drawArrayItem.type) {
						case 'image':
							drawImage(Context, drawArrayItem);
							break;
						case 'text':
							drawText(Context, drawArrayItem, bgObj);
							break;
						case 'qrcode':
							drawQrCode(Context, drawArrayItem);
							break;
						case 'custom':
							if (drawArrayItem.setDraw && typeof drawArrayItem.setDraw === 'function')
								drawArrayItem.setDraw(Context);
							break;
						case 'fillRect':
							drawFillRect(Context, drawArrayItem);
							break;
						case 'strokeRect':
							drawStrokeRect(Context, drawArrayItem);
							break;
						case 'roundStrokeRect':
							drawRoundStrokeRect(Context, drawArrayItem);
							break;
						case 'roundFillRect':
							drawRoundFillRect(Context, drawArrayItem);
							break;
						default:
							break;
					}
				}
			}
			if (draw === false) {
				rs();
				return;
			}
			const fn = function() {
				let setObj = setCanvasToTempFilePath || {};
				if (setObj && typeof(setObj) == 'function')
					setObj = setCanvasToTempFilePath(bgObj, type);
				let canvasToTempFilePathFn;
				const dpr = uni.getSystemInfoSync().pixelRatio;
				const data = {
					quality: .8,
					fileType: 'jpg',
					...setObj,
					canvasId: posterCanvasId,
				};
				if (canvas2image === false) {
					uni.hideLoading();
					return rs({
						setCanvasToTempFilePath: data
					});
				}
				canvasToTempFilePathFn = function() {
					const toTempFilePathObj = { //输出为图片
						...data,
						success(res) {
							uni.hideLoading();
							rs({
								...res,
								setCanvasToTempFilePath: data
							});
						},
						fail(err) {
							uni.hideLoading();
							rj('输出图片失败:' + JSON.stringify(err))
						}
					}
					uni.canvasToTempFilePath(toTempFilePathObj, _this || null);
				}
				let delayTime = 0;
				if (qrCodeArray) {
					qrCodeArray.forEach(item => {
						if (item.text) {
							delayTime += Number(item.text.length);
						}
					})
				}
				if (imagesArray) {
					imagesArray.forEach(() => {
						delayTime += delayTimeScale;
					})
				}
				if (textArray) {
					textArray.forEach(() => {
						delayTime += delayTimeScale;
					})
				}
				if (drawArray) {
					drawArray.forEach(item => {
						switch (item.type) {
							case 'text':
								if (item.text) {
									delayTime += item.text.length;
								}
								break;
							case 'qrcode':
								if (item.text) {
									delayTime += item.text.length * 2;
								}
								break;
							default:
								delayTime += delayTimeScale;
								break;
						}
					})
				}
				setTimeout(canvasToTempFilePathFn, delayTime);
			}
			
			Context.draw((typeof(reserve) == 'boolean' ? reserve : false), setTimeout(fn, drawDelayTime));
		} catch (e) {
			//TODO handle the exception
			uni.hideLoading();
			rj(e);
		}
	});
}
// export
function drawFillRect(Context, drawArrayItem = {}) {	//填充矩形
	Context.setFillStyle(drawArrayItem.backgroundColor || 'black');
	Context.setGlobalAlpha(drawArrayItem.alpha || 1);
	Context.fillRect(drawArrayItem.dx || 0, drawArrayItem.dy || 0, drawArrayItem.width || 0, drawArrayItem.height || 0);
	Context.setGlobalAlpha(1);
}

// export
function drawStrokeRect(Context, drawArrayItem = {}) {	//线条矩形
	Context.setStrokeStyle(drawArrayItem.color||'black');
	Context.setLineWidth(drawArrayItem.lineWidth || 1);
	Context.strokeRect(drawArrayItem.dx, drawArrayItem.dy, drawArrayItem.width, drawArrayItem.height);
}

// export
function drawRoundStrokeRect(Context, drawArrayItem = {}) {
	let {
		dx,
		dy,
		width,
		height,
		r,
		lineWidth,
		color
	} = drawArrayItem;
	r = r || width * .1;

	if (width < 2 * r) {
		r = width / 2;
	}
	if (width < 2 * r) {
		r = width / 2;
	}
	Context.beginPath();
	Context.arc(dx + r, dy + r, r, 1 * Math.PI, 1.5 * Math.PI);
	Context.lineTo(dx + width - r, dy);
	Context.arc(dx + width - r, dy + r, r, 1.5 * Math.PI, 0);
	Context.lineTo(dx + width, dy + height - r);
	Context.arc(dx + width - r, dy + height - r, r, 0, .5 * Math.PI);
	Context.lineTo(dx + r, dy + height);
	Context.arc(dx + r, dy + height - r, r, .5 * Math.PI, 1 * Math.PI);
	Context.lineTo(dx, dy + r);
	Context.closePath();
	Context.setLineWidth(lineWidth || 1);
	Context.setStrokeStyle(color || 'black');
	Context.stroke();
}

// export
function drawRoundFillRect(Context, drawArrayItem = {}) {
	let {
		dx,
		dy,
		width,
		height,
		r,
		backgroundColor
	} = drawArrayItem;
	r = r || width * .1;

	if (width < 2 * r) {
		r = width / 2;
	}
	if (width < 2 * r) {
		r = width / 2;
	}
	Context.beginPath();
	Context.arc(dx + r, dy + r, r, 1 * Math.PI, 1.5 * Math.PI);
	Context.lineTo(dx + width - r, dy);
	Context.arc(dx + width - r, dy + r, r, 1.5 * Math.PI, 0);
	Context.lineTo(dx + width, dy + height - r);
	Context.arc(dx + width - r, dy + height - r, r, 0, .5 * Math.PI);
	Context.lineTo(dx + r, dy + height);
	Context.arc(dx + r, dy + height - r, r, .5 * Math.PI, 1 * Math.PI);
	Context.lineTo(dx, dy + r);
	Context.closePath();
	Context.setFillStyle(backgroundColor);
	Context.fill();
}

// export 
function setText(Context, texts) { // 设置文本数据
	if (texts && utils.isArray(texts)) {
		if (texts.length > 0) {
			for (let i = 0; i < texts.length; i++) {
				texts[i] = setTextFn(Context, texts[i]);
			}
		}
	} else {
		texts = setTextFn(Context, texts);
	}
	return texts;
}

function setTextFn(Context, textItem) {
	if (textItem.text && typeof(textItem.text) == "string" && textItem.text.length > 0) {
		textItem.alpha = textItem.alpha !== undefined ? textItem.alpha : 1;
		textItem.color = textItem.color || 'black';
		textItem.size = textItem.size !== undefined ? textItem.size : 10;
		textItem.textAlign = textItem.textAlign || 'left';
		textItem.textBaseline = textItem.textBaseline || 'top';
		textItem.dx = textItem.dx || 0;
		textItem.dy = textItem.dy || 0;
		textItem.size = Math.ceil(Number(textItem.size));
		const textLength = countTextLength(Context, {
			text: textItem.text,
			size: textItem.size
		});
		let infoCallBackObj = {};
		if (textItem.infoCallBack && typeof(textItem.infoCallBack) === 'function')
			infoCallBackObj = textItem.infoCallBack(textLength);
		textItem = {
			...textItem,
			textLength,
			...infoCallBackObj
		}
	}
	return textItem;
}

function setLineFeed(Context, textItem, bgObj) {
	if (textItem.text && textItem.lineFeed) {
		let lineNum = -1,
			maxWidth = bgObj.width,
			lastLineMaxWidth,
			lineHeight = textItem.size,
			dx = textItem.dx;
		if (utils.isObject(textItem.lineFeed)) {
			const lineFeed = textItem.lineFeed;
			lineNum = (lineFeed.lineNum !== undefined && typeof(lineFeed.lineNum) === 'number') && lineFeed
				.lineNum >= 0 ?
				lineFeed.lineNum : lineNum;
			maxWidth = (lineFeed.maxWidth !== undefined && typeof(lineFeed.maxWidth) === 'number') ? lineFeed
				.maxWidth :
				maxWidth;
				
			lastLineMaxWidth = (lineFeed.lastLineMaxWidth !== undefined && typeof(lineFeed.lastLineMaxWidth) === 'number') ? lineFeed
				.lastLineMaxWidth :
				maxWidth;
			
			lineHeight = (lineFeed.lineHeight !== undefined && typeof(lineFeed.lineHeight) === 'number') ? lineFeed
				.lineHeight :
				lineHeight;
			dx = (lineFeed.dx !== undefined && typeof(lineFeed.dx) === 'number') ? lineFeed.dx : dx;
		}
		
		utils.lineFeedTags.forEach(i => {
			textItem.text = textItem.text.split(i).join(utils.tagetLineFeedTag);
		})
		const chr = (textItem.text).split("");
		let temp = "";
		const row = [];
		//循环出几行文字组成数组
		for (let a = 0, len = chr.length; a < len; a++) {
			if (chr[a] === utils.tagetLineFeedTag) {
				row.push(temp);
				temp = chr[++a];
				continue;
			}
			if (countTextLength(Context, {
					text: temp,
					size: textItem.size
				}) <= maxWidth && countTextLength(Context, {
					text: (temp + chr[a]),
					size: textItem.size
				}) <= maxWidth) {
				temp += chr[a];
				if (a == (chr.length - 1)) {
					row.push(temp);
				}
			} else {
				row.push(temp);
				temp = chr[a];
				if (a == chr.length - 1) row.push(chr[a]);
			}
		}
		//只显示几行 变量间距lineHeight  变量行数lineNum
		let allNum = (lineNum >= 0 && lineNum < row.length) ? lineNum : row.length;
		const newArr = [];
		for (let i = 0; i < allNum; i++) {
			let str = row[i];
			if (i == (allNum - 1) && allNum < row.length && row.length > 1) {
				
				const chr2 = (str).split("");
				let temp2 = "";
				//循环出几行文字组成数组
				for (let a = 0, len = chr2.length; a < len; a++) {
					if (countTextLength(Context, {
							text: temp2,
							size: textItem.size
						}) <= lastLineMaxWidth && countTextLength(Context, {
							text: (temp2 + chr2[a]),
							size: textItem.size
						}) <= lastLineMaxWidth) {
						temp2 += chr2[a];
					} 
				}
				str = temp2;
				if (countTextLength(Context, {
						text: str,
						size: textItem.size
					}) > (lastLineMaxWidth - textItem.size) * .9) {
					str = str.substring(0, str.length - 1) + '...';
				}
			}
			const obj = {
				...textItem,
				text: str,
				dx: i === 0 ? textItem.dx : (dx >= 0 ? dx : textItem.dx),
				dy: textItem.dy + (i * lineHeight),
				textLength: countTextLength(Context, {
					text: str,
					size: textItem.size
				})
			};
			newArr.push(obj);
		}
		const result = newArr.length > 1 ? newArr : newArr[0];
		return result
	}
	return textItem;
}

function countTextLength(Context, obj) {
	const {
		text,
		size
	} = obj;
	Context.setFontSize(size);
	let textLength;
	try{
		textLength = Context.measureText(text); // 官方文档说 App端自定义组件编译模式暂时不可用measureText方法
	}catch(e){
		//TODO handle the exception
		textLength = {};
	}
	textLength = textLength && textLength.width ? textLength.width : 0;
	if (!textLength) {
		let l = 0;
		for (let j = 0; j < text.length; j++) {
			let t = text.substr(j, 1);
			if (/[a-zA-Z]/.test(t)) {
				l += .7;
			} else if (/[0-9]/.test(t)) {
				l += .55;
			} else if (/\./.test(t)) {
				l += .27;
			} else if (/-/.test(t)) {
				l += .325;
			} else if (/[\u4e00-\u9fa5]/.test(t)) { //中文匹配
				l += 1;
			} else if (/\(|\)/.test(t)) {
				l += .373;
			} else if (/\s/.test(t)) {
				l += .25;
			} else if (/%/.test(t)) {
				l += .8;
			} else {
				l += 1;
			}
		}
		textLength = l * size;
	}
	return textLength;
}

// export 
function setImage(images) { // 设置图片数据
	return new Promise(async (resolve, rejcet) => {
		try {
			if (images && utils.isArray(images)) {
				for (let i = 0; i < images.length; i++) {
					images[i] = await setImageFn(images[i]);
				}
			} else {
				images = await setImageFn(images);
			}
			resolve(images);
		} catch (e) {
			//TODO handle the exception
			rejcet(e);
		}
	})
}

function base64ToPathFn(path) {
	var reg =
		/^\s*data:([a-z]+\/[a-z0-9-+.]+(;[a-z-]+=[a-z0-9-]+)?)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s]*?)\s*$/i;
	if (!reg.test(path)) {
		return Promise.resolve(path);
	}
	return base64ToPath(path);
}

function setImageFn(image) {
	return new Promise(async (resolve, reject) => {
		if (image.url) {
			image.url = (await base64ToPathFn(image.url));
			let imgUrl = image.url;
			imgUrl = await utils.downloadFile_PromiseFc(imgUrl);
			image.url = imgUrl;
			const hasinfoCallBack = image.infoCallBack && typeof(image.infoCallBack) === 'function';
			let imageInfo = {};
			imageInfo = await utils.getImageInfo_PromiseFc(imgUrl);
			if (hasinfoCallBack) {
				image = {
					...image,
					...image.infoCallBack(imageInfo)
				};
			}
			image.dx = image.dx || 0;
			image.dy = image.dy || 0;
			image.dWidth = image.dWidth || imageInfo.width;
			image.dHeight = image.dHeight || imageInfo.height;
			image = {
				...image,
				imageInfo
			}
		}
		resolve(image);
	})
}

// export 
function drawText(Context, textArray, bgObj) { // 先遍历换行再绘制
	if (!utils.isArray(textArray)) {
		textArray = [textArray];
	} else {
	}
	const newArr = [];
	if (textArray && textArray.length > 0) {
		for (let j = 0; j < textArray.length; j++) {
			const textItem = textArray[j];
			if (textItem.text && textItem.lineFeed) {
				let lineNum = -1,
					maxWidth = bgObj.width,
					lineHeight = textItem.size,
					dx = textItem.dx;
				if (textItem.lineFeed instanceof Object) {
					const lineFeed = textItem.lineFeed;
					lineNum = (lineFeed.lineNum !== undefined && typeof(lineFeed.lineNum) === 'number') && lineFeed.lineNum >= 0 ?
						lineFeed.lineNum : lineNum;
					maxWidth = (lineFeed.maxWidth !== undefined && typeof(lineFeed.maxWidth) === 'number') ? lineFeed.maxWidth :
						maxWidth;
					lineHeight = (lineFeed.lineHeight !== undefined && typeof(lineFeed.lineHeight) === 'number') ? lineFeed.lineHeight :
						lineHeight;
					dx = (lineFeed.dx !== undefined && typeof(lineFeed.dx) === 'number') ? lineFeed.dx : dx;
					
					if (lineFeed.lineHeight){
						textItem.dy += (lineFeed.lineHeight - textItem.size) / 2
					}
				}
				const chr = (textItem.text).split("");
				let temp = "";
				const row = [];
				//循环出几行文字组成数组
				for (let a = 0, len = chr.length; a < len; a++) {
					if (countTextLength(Context, {
							text: temp,
							size: textItem.size
						}) <= maxWidth && countTextLength(Context, {
							text: (temp + chr[a]),
							size: textItem.size
						}) <= maxWidth) {
						temp += chr[a];
						if (a == (chr.length - 1)) {
							row.push(temp);
						}
					} else {
						row.push(temp);
						temp = chr[a];
					}
				}
				//只显示几行 变量间距lineHeight  变量行数lineNum
				let allNum = (lineNum >= 0 && lineNum < row.length) ? lineNum : row.length;

				for (let i = 0; i < allNum; i++) {
					let str = row[i];
					if (i == (allNum - 1) && allNum < row.length) {
						str = str.substring(0, str.length - 1) + '...';
					}
					const obj = { ...textItem,
						text: str,
						dx: i === 0 ? textItem.dx : (dx >= 0 ? dx : textItem.dx),
						dy: textItem.dy + (i * lineHeight),
						textLength: countTextLength(Context, {
							text: str,
							size: textItem.size
						})
					};
					newArr.push(obj);
				}
			} else {
				newArr.push(textItem);
			}
		}
	}
	drawTexts(Context, newArr);
}

function setFont(textItem = {}) {
	if (textItem.font && typeof(textItem.font) === 'string') {
		return textItem.font;
	} else {
		let fontStyle = 'normal';
		let fontVariant = 'normal';
		let fontWeight = 'normal';
		let fontSize = textItem.size || 10;
		let fontFamily = 'sans-serif';
		fontSize = Math.ceil(Number(fontSize));
		if (textItem.fontStyle && typeof(textItem.fontStyle) === 'string')
			fontStyle = textItem.fontStyle.trim();
		if (textItem.fontVariant && typeof(textItem.fontVariant) === 'string')
			fontVariant = textItem.fontVariant.trim();
		if (textItem.fontWeight && (typeof(textItem.fontWeight) === 'string' || typeof(textItem.fontWeight) === 'number'))
			fontWeight = textItem.fontWeight.trim();
		if (textItem.fontFamily && typeof(textItem.fontFamily) === 'string')
			fontFamily = textItem.fontFamily.trim();
		return fontStyle + ' ' +
			fontVariant + ' ' +
			fontWeight + ' ' +
			fontSize + 'px' + ' ' +
			fontFamily;
	}
}

function drawTexts(Context, texts) { // 绘制文本
	if (texts && utils.isArray(texts)) {
		if (texts.length > 0) {
			for (let i = 0; i < texts.length; i++) {
				drawTextFn(Context, texts[i]);
			}
		}
	} else {
		drawTextFn(Context, texts);
	}
}

function drawTextFn(Context, textItem) {
	if (textItem && utils.isObject(textItem) && textItem.text) {
		Context.font = setFont(textItem);
		Context.setFillStyle(textItem.color);
		Context.setGlobalAlpha(textItem.alpha);
		Context.setTextAlign(textItem.textAlign);
		Context.setTextBaseline(textItem.textBaseline);
		Context.fillText(textItem.text, textItem.dx, textItem.dy);
		if (textItem.lineThrough && textItem.lineThrough instanceof Object) {
			let lineThrough = textItem.lineThrough;
			lineThrough.alpha = lineThrough.alpha !== undefined ? lineThrough.alpha : textItem.alpha;
			lineThrough.style = lineThrough.style || textItem.color;
			lineThrough.width = lineThrough.width !== undefined ? lineThrough.width : textItem.size / 10;
			lineThrough.cap = lineThrough.cap !== undefined ? lineThrough.cap : 'butt';
			Context.setGlobalAlpha(lineThrough.alpha);
			Context.setStrokeStyle(lineThrough.style);
			Context.setLineWidth(lineThrough.width);
			Context.setLineCap(lineThrough.cap);
			let mx, my;
			switch (textItem.textAlign) {
				case 'left':
					mx = textItem.dx;
					break;
				case 'center':
					mx = textItem.dx - (textItem.textLength) / 2;
					break;
				default:
					mx = textItem.dx - (textItem.textLength);
					break;
			}
			switch (textItem.textBaseline) {
				case 'top':
					my = textItem.dy + (textItem.size * .5);
					break;
				case 'middle':
					my = textItem.dy;
					break;
				default:
					my = textItem.dy - (textItem.size * .5);
					break;
			}
			Context.beginPath();
			Context.moveTo(mx, my);
			Context.lineTo(mx + textItem.textLength, my);
			Context.stroke();
			Context.closePath();
		}
		Context.setGlobalAlpha(1);
		Context.font = '10px sans-serif';
	}
}

// export
function drawQrCode(Context, qrCodeObj) { //生成二维码方法， 参考了 诗小柒 的二维码生成器代码
	// 默认 为二维码绘制白色圆角背景
	let qrScale = 0
	if (qrCodeObj.backgroundRect !== false) {
		drawRoundFillRect(Context, {
			dx: qrCodeObj.dx,
			dy: qrCodeObj.dy,
			width: qrCodeObj.size,
			height: qrCodeObj.size,
			r: 18,
			backgroundColor: '#ffffff'
		})
		qrScale = 40
	}
	
	
	let qrcodeAlgObjCache = [];
	let options = {
		text: String(qrCodeObj.text || '') || '', // 生成内容
		size: (Number(qrCodeObj.size || 0) || 200) - qrScale, // 二维码大小
		background: String(qrCodeObj.background || '') || '#ffffff', // 背景色
		foreground: String(qrCodeObj.foreground || '') || '#000000', // 前景色
		pdground: String(qrCodeObj.pdground || '') || '#000000', // 定位角点颜色
		correctLevel: Number(qrCodeObj.correctLevel || 0) || 3, // 容错级别
		image: String(qrCodeObj.image || '') || '', // 二维码图标
		imageSize: Number(qrCodeObj.imageSize || 0) || 40, // 二维码图标大小
		dx: (Number(qrCodeObj.dx || 0) || 0) + qrScale / 2, // x轴距离
		dy: (Number(qrCodeObj.dy || 0) || 0) + qrScale / 2 // y轴距离
	}
	
	let qrCodeAlg = null;
	let d = 0;
	for (var i = 0, l = qrcodeAlgObjCache.length; i < l; i++) {
		d = i;
		if (qrcodeAlgObjCache[i].text == options.text && qrcodeAlgObjCache[i].text.correctLevel == options
			.correctLevel) {
			qrCodeAlg = qrcodeAlgObjCache[i].obj;
			break;
		}
	}
	if (d == l) {
		qrCodeAlg = new QRCodeAlg(options.text, options.correctLevel);
		qrcodeAlgObjCache.push({
			text: options.text,
			correctLevel: options.correctLevel,
			obj: qrCodeAlg
		});
	}
	let getForeGround = function(config) {
		let options = config.options;
		if (options.pdground && (
				(config.row > 1 && config.row < 5 && config.col > 1 && config.col < 5) ||
				(config.row > (config.count - 6) && config.row < (config.count - 2) && config.col > 1 && config
					.col < 5) ||
				(config.row > 1 && config.row < 5 && config.col > (config.count - 6) && config.col < (config.count -
					2))
			)) {
			return options.pdground;
		}
		return options.foreground;
	}
	let count = qrCodeAlg.getModuleCount();
	let ratioSize = options.size;
	let ratioImgSize = options.imageSize;
	//计算每个点的长宽
	let tileW = (ratioSize / count).toPrecision(4);
	let tileH = (ratioSize / count).toPrecision(4);
	//绘制
	for (let row = 0; row < count; row++) {
		for (let col = 0; col < count; col++) {
			let w = (Math.ceil((col + 1) * tileW) - Math.floor(col * tileW));
			let h = (Math.ceil((row + 1) * tileW) - Math.floor(row * tileW));
			let foreground = getForeGround({
				row: row,
				col: col,
				count: count,
				options: options
			});
			Context.setFillStyle(qrCodeAlg.modules[row][col] ? foreground : options.background);
			Context.fillRect(options.dx + Math.round(col * tileW), options.dy + Math.round(row * tileH), w, h);
		}
	}
	if (options.image) {
		let x = options.dx + Number(((ratioSize - ratioImgSize) / 2).toFixed(2));
		let y = options.dy + Number(((ratioSize - ratioImgSize) / 2).toFixed(2));
		drawRoundedRect(Context, x, y, ratioImgSize, ratioImgSize, 2, 6, true, true)
		Context.drawImage(options.image, x, y, ratioImgSize, ratioImgSize);
		// 画圆角矩形
		function drawRoundedRect(ctxi, x, y, width, height, r, lineWidth, fill, stroke) {
			ctxi.setLineWidth(lineWidth);
			ctxi.setFillStyle(options.background);
			ctxi.setStrokeStyle(options.background);
			ctxi.beginPath(); // draw top and top right corner 
			ctxi.moveTo(x + r, y);
			ctxi.arcTo(x + width, y, x + width, y + r, r); // draw right side and bottom right corner 
			ctxi.arcTo(x + width, y + height, x + width - r, y + height, r); // draw bottom and bottom left corner 
			ctxi.arcTo(x, y + height, x, y + height - r, r); // draw left and top left corner 
			ctxi.arcTo(x, y, x + r, y, r);
			ctxi.closePath();
			if (fill) {
				ctxi.fill();
			}
			if (stroke) {
				ctxi.stroke();
			}
		}
	}
}


// export 
function drawImage(Context, images) { // 绘制图片
	if (images && utils.isArray(images)) {
		if (images.length > 0) {
			for (let i = 0; i < images.length; i++) {
				readyDrawImageFn(Context, images[i]);
			}
		}
	} else {
		readyDrawImageFn(Context, images);
	}

}

function readyDrawImageFn(Context, img) {
	if (img.url) {
		if (img.circleSet) {
			drawCircleImage(Context, img);
		} else if (img.roundRectSet) {
			drawRoundRectImage(Context, img);
		} else {
			drawImageFn(Context, img);
		}
	}
}

const drawImageModes = {
	scaleToFill(Context, img) {
		Context.drawImage(img.url, Number(img.dx || 0), Number(img.dy || 0),
			Number(img.dWidth) || false, Number(img.dHeight) || false);
	},
	aspectFit(Context, img) {
		const {
			imageInfo,
			dWidth,
			dHeight
		} = img;
		const {
			height,
			width
		} = imageInfo;
		let drawWidth = dWidth;
		let drawHeight = height / width * drawWidth;
		if (drawHeight < dHeight) {
			const diffHeight = (Number(dHeight) - Number(drawHeight)) / Number(dHeight) * height;
			img.dy = Number(img.dy) + diffHeight / 2;
		} else {
			drawHeight = dHeight;
			drawWidth = width / height * drawHeight;
			const diffWidth = (Number(dWidth) - Number(drawWidth)) / Number(dWidth) * width;
			img.dx = Number(img.dx) + diffWidth / 2;
		}
		Context.drawImage(img.url, 0, 0, width, height, img.dx, img.dy, drawWidth, drawHeight);
	},
	aspectFill(Context, img) {
		const dpr = uni.getSystemInfoSync().pixelRatio;
		const {
			imageInfo,
			dWidth,
			dHeight
		} = img;
		const {
			height,
			width
		} = imageInfo;
		let sx = 0,
			sy = 0,
			sWidth = (width),
			sHeight = (height);
		let drawWidth = dWidth;
		let drawHeight = height / width * drawWidth;
		if (drawHeight < dHeight) {
			drawHeight = dHeight;
			drawWidth = width / height * drawHeight;
			const diffWidth = ((Number(drawWidth) - Number(dWidth)) / Number(drawWidth)) * width;
			sx = diffWidth / 2;
			sWidth = width - diffWidth;
		} else {
			const diffHeight = ((Number(drawHeight) - Number(dHeight)) / Number(drawHeight)) * height;
			sy = diffHeight / 2;
			sHeight = (height - diffHeight);
		}
		Context.drawImage(img.url, sx, sy, sWidth, sHeight, img.dx, img.dy, dWidth, dHeight);
	}
}

function drawImageFn(Context, img) {
	if (img.url) {
		if (img.alpha) {
			Context.setGlobalAlpha(img.alpha);
		}
		if (img.dHeight === undefined) img.dHeight = img.imageInfo.height;
		if (img.dWidth === undefined) img.dWidth = img.imageInfo.width;
		const fn = drawImageModes[img.mode];
		if (fn) {
			fn(Context, img);
		} else {
			if (img.dWidth && img.dHeight && img.sx && img.sy && img.sWidth && img.sHeight) {
				Context.drawImage(img.url, img.dx || 0, img.dy || 0,
					img.dWidth || false, img.dHeight || false,
					img.sx || false, img.sy || false,
					img.sWidth || false, img.sHeight || false);
			} else if (img.dWidth && img.dHeight) {
				Context.drawImage(img.url, img.dx || 0, img.dy || 0,
					img.dWidth || false, img.dHeight || false);
			} else {
				Context.drawImage(img.url, img.dx || 0, img.dy || 0);
			}
			if (img.alpha) {
				Context.setGlobalAlpha(1);
			}
		}
	}
	/* if (img.circleSet || img.roundRectSet) {
		Context.restore();
	} */
}

function drawCircleImage(Context, obj) {
	Context.save();
	let {
		dx,
		dy,
		dWidth,
		dHeight,
		circleSet,
		imageInfo
	} = obj;
	let x, y, r;
	if (typeof circleSet === 'object') {
		x = circleSet.x;
		y = circleSet.y;
		r = circleSet.r;
	}
	if (!r) {
		let d;
		d = dWidth > dHeight ? dHeight : dWidth;
		r = d / 2;
	}

	x = x ? dx + x : (dx || 0) + r;
	y = y ? dy + y : (dy || 0) + r;
	Context.beginPath();
	Context.arc(x, y, r, 0, 2 * Math.PI, false);
	Context.closePath();
	Context.fillStyle = '#FFFFFF';
	Context.fill();
	Context.clip();
	drawImageFn(Context, obj);
	Context.restore();
}

function drawRoundRectImage(Context, obj) { // 绘制矩形
	Context.save();
	let {
		dx,
		dy,
		dWidth,
		dHeight,
		roundRectSet,
		imageInfo
	} = obj;
	let r;
	let radius;
	if (typeof roundRectSet === 'object') {
		r = roundRectSet.r;
		radius = roundRectSet.radius;
	}
	r = r || dWidth * .1;

	if (dWidth < 2 * r) {
		r = dWidth / 2;
	}
	if (dHeight < 2 * r) {
		r = dHeight / 2;
	}
	Context.beginPath();
	Context.moveTo(dx, dy + dHeight);
	Context.arcTo(dx, dy, dx + dWidth, dy, radius && !radius[0]? 0.1 : r); // 左上
	Context.arcTo(dx + dWidth, dy, dx + dWidth, dy + dHeight, radius && !radius[1]? 0.1 : r); //右上
	Context.arcTo(dx + dWidth, dy + dHeight, dx, dy + dHeight, radius && !radius[2]? 0.1 : r); // 右下
	Context.arcTo(dx, dy + dHeight, dx, dy, radius && !radius[3]? 0.1 : r); // 左下
	
	Context.closePath();
	Context.fillStyle = '#FFFFFF';
	Context.fill();
	Context.clip();
	drawImageFn(Context, obj);
	Context.restore();
}

function getPosterStorage(type) {
	return utils.getStorageSync(getStorageKey(type));
}

function removePosterStorage(type) {
	const ShreUserPosterBackgroundKey = getStorageKey(type);
	const pbg = utils.getStorageSync(ShreUserPosterBackgroundKey);
	if (pbg && pbg.path) {
		utils.removeSavedFile(pbg.path);
		utils.removeStorageSync(ShreUserPosterBackgroundKey);
	}
}

function setPosterStorage(type, data) {
	utils.setStorage(getStorageKey(type), data);
}

function getStorageKey(type) {
	return ShreUserPosterBackgroundKey + (type || 'default');
}

function getShreUserPosterBackground(objs) { //检查背景图是否存在于本地， 若存在直接返回， 否则调用getShreUserPosterBackgroundFc方法
	let {
		backgroundImage,
		type
	} = objs;
	return new Promise(async (resolve, reject) => {
		try {
			const savedFilePath = await getShreUserPosterBackgroundFc(objs)
			resolve(savedFilePath);
		} catch (e) {
			reject(e);
		}
	})
}

function getShreUserPosterBackgroundFc(objs, upimage) { //下载并保存背景图方法
	let {
		backgroundImage,
		type
	} = objs;
	return new Promise(async (resolve, reject) => {
		try {
			let image = backgroundImage ? backgroundImage : (await utils.getPosterUrl(objs));
			image = (await base64ToPathFn(image));
			const savedFilePath = await utils.downLoadAndSaveFile_PromiseFc(image);
			if (savedFilePath) {
				const imageObj = await utils.getImageInfo_PromiseFc(image);
				const returnObj = {
					path: savedFilePath,
					width: imageObj.width,
					height: imageObj.height,
					name: utils.fileNameInPath(image)
				}

				// #ifndef H5
				setPosterStorage(type, {
					...returnObj
				});
				// #endif

				resolve({
					...returnObj
				});
			} else {
				reject('not find savedFilePath');
			}
		} catch (e) {
			//TODO handle the exception
			reject(e);
		}
	});
}

function roundRect(ctx, x, y, w, h, r) {
	if (w< 2 * r) r = w / 2
	if (h < 2 * r) r = h / 2
	ctx.beginPath()
	ctx.moveTo(x + r, y)
	ctx.arcTo(x + w, y, x + w, y + h, r)
	ctx.arcTo(x + w, y + h, x, y + h, r)
	ctx.arcTo(x, y + h, x, y, r)
	ctx.arcTo(x, y, x + w, y, r)
	ctx.closePath()
}

module.exports = {
	getSharePoster,
	setText,
	setImage,
	drawText,
	drawImage,
	drawQrCode,
	drawFillRect,
	drawStrokeRect,
	drawRoundStrokeRect,
	drawRoundFillRect
}