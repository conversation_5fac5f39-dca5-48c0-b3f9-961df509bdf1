<template></template>

<script>
import { GetCodePage } from '@/service/common'
export default {
  components: {},
  data() {
    return {}
  },
  onLoad(options) {
    GetCodePage({
      scene: options.scene
    }).then((res) => {
      // TODO: 暂未处理特殊链接
      // this.$navigateTo({
      //   path: res.data
      // })
      uni.redirectTo({
        url: res.data
      })
    })
  }
}
</script>

<style lang="scss" scoped></style>
