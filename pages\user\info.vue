<template>
  <view class="container-root">
    <view class="content">
      <view class="title">您可以查阅劲牌藏酒小程序对您的个人信息的收集情况</view>
      <view class="rows">
        <view class="flex-row border-b">
          <view class="cell-title">
            头像
            <view class="desc">{{ authInfo.iconStr }}</view>
          </view>
          <view class="cell-value" v-if="userInfo.avatarUrl">
            <image :src="userInfo.avatarUrl | formatUrl" class="avatar" mode="aspectFill"></image>
          </view>
        </view>
        <view class="flex-row border-b">
          <view class="cell-title">
            昵称
            <view class="desc">{{ authInfo.nickNameStr }}</view>
          </view>
          <view class="cell-value">{{ userInfo.nickName || '-' }}</view>
        </view>
        <view class="flex-row border-b">
          <view class="cell-title">
            手机号
            <view class="desc">{{ authInfo.mobileStr }}</view>
          </view>
          <view class="cell-value">{{ userInfo.mobile || '-' }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { GetUserInfoList, GetAuthInfo } from '@/service/common'

export default {
  data() {
    return {
      userInfo: {},
      authInfo: {},
    }
  },

  onLoad() {
    this.getUserInfo()
    this.getAuthInfo()
  },

  methods: {
    async getUserInfo() {
      const res = await GetUserInfoList()
      this.userInfo = res.data
    },

    async getAuthInfo() {
      const res = await GetAuthInfo()
      this.authInfo = res.data
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 0 24rpx;
  .title {
    color: #666;
    padding: 32rpx 0 16rpx;
  }
}

.rows {
  background-color: #fff;
  padding: 0 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.flex-row {
  display: flex;
  padding: 24rpx 0;
  position: relative;
  align-items: center;
}
.cell-title {
  flex: 1;
  color: #000;
  line-height: 40rpx;
  font-size: 28rpx;
  .desc {
    margin-top: 8rpx;
    color: #8c8c8c;
    font-size: 24rpx;
    line-height: 32rpx;
  }
}
.iconfont {
  color: #8c8c8c;
}
.icon-arrow-right {
  font-size: 20rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.cell-value {
  margin-left: 24rpx;
  color: #333;
  font-size: 28rpx;
}
</style>
