import request from '@/config/request'

// 获取活动列表
export const GetActivityList = (data) => request({ url: 'fansClub/mini/activity/pageList', data, method: 'post' })
// 获取活动详情
export const GetActivityDetail = (data) => request({ url: 'fansClub/mini/activity/detail', data })
// 报名参与活动
export const JoinActivity = (data) => request({ url: 'fansClub/mini/activity/join', data, method: 'post' })
// 获取活动排行榜
export const GetActivityRankList = (data) => request({ url: 'fansClub/mini/activity/rankList', data, method: 'post' })
// 同步步数
export const SyncStep = (data) => request({ url: 'fansClub/mini/activity/syncStep', data, method: 'post', showLoading: false })

// 获取抽奖详情
export const GetDrawDetail = (data) => request({ url: 'fansClub/mini/draw/detail', data })
// 获取中奖名单
export const GetActWinner = (data) => request({ url: 'fansClub/mini/draw/actWinner', data })
// 抽奖
export const SportsDraw = (data) => request({ url: 'fansClub/mini/draw/draw', data, method: 'post' })
// 获取用户奖品
export const GetUserActPrize = (data) => request({ url: 'fansClub/mini/draw/userActPrize', data })
