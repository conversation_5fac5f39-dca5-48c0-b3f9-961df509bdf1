view {
  &[hidden] {
    display: none;
  }
}

uni-page-head {
  display: none !important;
}
@keyframes timeAni {
  0% {
    transform: translateY(0%);
  }
  100% {
    transform: translateY(-100%);
  }
}

.scroll-Y {
  overflow-y: scroll;
  height: 100%;
}

.scroll-x {
  overflow-x: scroll;
  width: 100%;
}

.hidden {
  display: none !important;
}

.overflow-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.clearfix:after {
  content: '';
  display: block;
  clear: both;
}

.clearfix {
  zoom: 1;
}
.border,
.border-b,
.border-t,
.border-r,
.border-l {
  position: relative;
}
.relative {
  position: relative;
}
.ibk {
  display: inline-block;
}
.border-b::before {
  @include border-line(auto, 0, 0, 0);
}

.border-t::before {
  @include border-line(0, 0, auto, 0);
}

.border-r::before {
  @include border-line(0, 0, 0, auto);
}

.border-l::before {
  @include border-line(0, auto, 0, 0);
}

.border-line {
  content: ' ';
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid $colorborder;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 64rpx;
}

.border::after {
  content: ' ';
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid $colorborder;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 50%;
}

.no-more {
  position: relative;
  height: 36rpx;
  padding-bottom: 40rpx;
}

.no-more::before {
  content: '';
  position: absolute;
  top: 18rpx;
  left: 0;
  right: 0;
  height: 1px;
  background-color: $colorborder;
  transform: scaleY(0.5);
}

.no-more::after {
  content: '没有更多数据了';
  position: absolute;
  top: 0;
  left: 50%;
  height: 36rpx;
  padding: 0 16rpx;
  line-height: 36rpx;
  color: $colorgraylight;
  font-size: 26rpx;
  background-color: #fff;
  transform: translateX(-50%);
}

.no-more-default {
  line-height: 40rpx;
  color: $colorgraylight;
  font-size: 26rpx;
  padding: 24rpx;
  text-align: center;
}

.color-gray {
  color: $colorgray;
}

.align-right {
  text-align: right;
}

.bg-white {
  background-color: #fff;
}

/**
 * 数字角标
 */
.badge {
  display: inline-block;
  padding: 0 6rpx;
  height: 32rpx;
  min-width: 32rpx;
  font-size: 20rpx;
  border-radius: 16rpx;
  background-color: #ff3d47;
  color: #fff;
  line-height: 32rpx;
  box-sizing: border-box;
  text-align: center;
}

/**
 * 详情页底部固定导航
 */
.footer-nav-wrap {
  position: fixed;
  z-index: 9;
  bottom: 0;
  left: 0;
  right: 0;
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  background-color: #fff;
  display: flex;
  .nav-list {
    display: flex;
    position: relative;
  }

  .nav-item {
    position: relative;
    padding-left: 28rpx;
    padding-right: 28rpx;
    text-align: center;
    box-sizing: content-box;
    line-height: 1;
    border-radius: 0;
    background-color: #fff;
    font-size: 20rpx;
    overflow: visible;
    & + .nav-item {
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        background: #f0f0f0;
        width: 1px;
        transform: scaleX(0.5);
      }
    }
    .iconfont {
      display: block;
      font-size: 48rpx;
    }
    .text {
      display: block;
      line-height: 28rpx;
      font-size: 20rpx;
    }
    &::after {
      display: none;
    }

    .badge {
      position: absolute;
      top: -8rpx;
      right: 19rpx;
    }
  }

  .handle {
    display: flex;
    flex: 1;
    overflow: hidden;
    padding-right: 24rpx;
    .btn + .btn {
      margin-left: 16rpx;
    }
  }

  .btn {
    flex: 1;
    overflow: hidden;
    padding: 0;
    height: 72rpx;
    background-color: #f52440;
    color: #fff;
    line-height: 72rpx;
    border-radius: 36rpx;
    font-size: 28rpx;
    &::after {
      display: none;
    }
  }
}

.btn-disabled {
  opacity: 0.5;
}

/**
 * 字体相关
 */
.bold {
  font-weight: bold;
}

.color-red {
  color: #f52440 !important;
}

/**
 * cell 列表tab
 */

.cell-list-item-small {
  position: relative;
  @include cell-list-item('small');
}

.cell-list-item-big {
  position: relative;
  @include cell-list-item('big');
}

.cell-list-item-noaside {
  .main {
    padding: 0;
  }
}

.cell-list-item-noother {
  .main {
    padding-right: 0;
  }
}

.cell-list-item-noborder {
  &::after {
    display: none;
  }
}

/**
 * 带图片的列表
 */
.cell-list-item-pic {
  display: flex;
  padding: 32rpx 24rpx 40rpx;
  background-color: #fff;
  image {
    width: 136rpx;
    height: 136rpx;
  }
  .image-wrap {
    width: 136rpx;
    position: relative;

    .tag {
      position: absolute;
      top: 8rpx;
      left: 0;
      padding-right: 16rpx;
      padding-left: 8rpx;
      height: 32rpx;
      line-height: 32rpx;
      border-radius: 0 16rpx 16rpx 0;
      background-color: #ff3d47;
      color: #fff;
      font-size: 20rpx;
    }
  }
  .main {
    padding-left: 16rpx;
    flex: 1;
    overflow: hidden;
    line-height: 40rpx;
    .name {
      @include multiline-overflow(2);
    }
    .sku {
      padding-top: 16rpx;
      color: $colorgraydeep;
    }
  }
}

/**
 * 复选，单选按钮
 */

.checkbox-group {
  display: flex;
  height: 48rpx;
  align-items: center;
}

.checkbox {
  line-height: 1;
  display: inline-block;
  position: relative;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  overflow: hidden;

  // &::after{
  // 	content: " ";
  // 	width: 200%;
  // 	height: 200%;
  // 	position: absolute;
  // 	top: 0;
  // 	left: 0;
  // 	border: 3px solid #bcbcbc;
  // 	transform: scale(.5);
  // 	transform-origin: 0 0;
  // 	box-sizing: border-box;
  // 	border-radius: 50%;
  // }
  .iconfont {
    display: none;
    color: #bcbcbc;
    font-size: 40rpx;
  }

  .border-line {
    position: absolute;
    content: ' ';
    width: 200%;
    height: 200%;
    top: 0;
    left: 0;
    border: 3px solid #bcbcbc;
    transform: scale(0.5);
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 50%;
  }
}

.checkbox-checked {
  .border-line {
    border-color: #f52440 !important;
  }
  .iconfont {
    display: inline-block;
    color: #f52440;
  }
}

.checkbox-disabled {
  .checkbox {
    background-color: #fafafa !important;
  }
  .border-line {
    border-color: #e6e6e6 !important;
  }
}

.btn {
  border-radius: 9999rpx;
  line-height: 3.0;
  font-size: $size;
  color: $colortext;
  background-color: #fff;
  &::after {
    border-color: #e6e6e6;
    border-radius: 72rpx;
  }
}

.btn-small {
  padding: 0 24rpx;
  border-radius: 28rpx;
  line-height: 56rpx;
  font-size: 24rpx;
  &::after {
    border-radius: 56rpx;
  }
}

.btn-use {
  &::after {
    display: none;
  }
}

.btn-primary {
  background-color: #f52440;
  color: #fff;
  &::after {
    display: none;
  }
}

.btn-light {
  color: #f52440;
  &::after {
    border-color: #f52440;
  }
}

.btn-disiable {
  opacity: 0.8;
}

.btn-block {
  width: 100%;
}

/**
 * 外层容器
 */
.container-root {
  background-color: $colorbackground;
  min-height: 100vh;
  /* #ifdef APP-PLUS */
  min-height: calc(100vh - var(--window-top));
  /* #endif */
  box-sizing: border-box;
  overflow: hidden;
}
.transparent {
  opacity: 0;
}

.header-sticky {
  position: sticky;
  top: 0;
  overflow: hidden;
  z-index: 10;
  background-color: #fff;
}

/**
 * 空内容提示
 */
.empty {
  // height: 100vh;
  // background: $colorbackground;
  padding-top: 200rpx;
  text-align: center;
  box-sizing: border-box;
  font-size: 0;
  image {
    width: 288rpx;
    height: 188rpx;
  }
  view {
    margin-top: 32rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    color: $colorgraylight;
  }
}

/**
 * 头部渐变色块
 */
.cell-gradient {
  padding: 32rpx 40rpx;
  background: linear-gradient(to right, #ff3d46, #ffa320);
  color: #fff;
  position: relative;
}

/**
 * 底部弹出菜单
 */
.action-sheet {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 200;
  transform: translateY(100%);
  &.active {
    transform: translateY(0);
  }
  .mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 0;
    background-color: rgba(0, 0, 0, 0.3);
  }

  &-wrap {
    position: absolute;
    transform: translateY(100%);
    right: 0;
    bottom: 0;
    left: 0;
    height: 954rpx;
    display: flex;
    flex-direction: column;
    transition: all 0.3s;
    background-color: #fff;
    .active & {
      transform: translateY(0);
    }
  }

  &-header {
    line-height: 46rpx;
    padding: 26rpx 24rpx;
    font-size: 32rpx;
    text-align: center;
  }

  &-content {
    flex: 1;
    overflow-y: scroll;
    padding: 24rpx;
  }

  .close {
    position: absolute;
    top: 30rpx;
    right: 24rpx;
    font-size: 40rpx;
    color: $colorgraydeep;
  }

  .back {
    position: absolute;
    top: 28rpx;
    left: 16rpx;
    font-size: 28rpx;
    padding: 8rpx;
  }

  &-bottom {
    display: flex;
    padding: 24rpx;
  }

  &-warn {
    flex-shrink: 0;
    line-height: 72rpx;
    font-size: 28rpx;
    color: #f5c462;
    background-color: $coloryellowlight;
    text-align: center;
  }

  &-bottom .btn {
    flex: 1;
    &:first-child {
      font-size: 28rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
    }
    &:last-child {
      line-height: 88rpx;
      border-radius: 44rpx;
      font-size: 30rpx;
    }
    & + .btn {
      margin-left: 24rpx;
      font-size: 28rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
    }
  }

  &-cell-title {
    padding-top: 16rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
  }
  &-cell-item {
    padding: 24rpx 0 8rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    color: $colorgray;
  }
}

.histore-tag {
  display: inline-block;
  height: 32rpx;
  line-height: 32rpx;
  font-size: 20rpx !important;
  background-color: rgba(255, 241, 241, 1);
  position: relative;
  color: #ff3d47;
  padding: 0 8rpx;
  margin-right: 16rpx;
  vertical-align: middle;
  border-radius: 16rpx;
  &::before {
    content: ' ';
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(255, 61, 71, 1);
    transform: scale(0.5);
    transform-origin: 0 0;
    box-sizing: border-box;
    border-radius: 32rpx;
  }
}

.mt-24 {
  margin-top: 24rpx;
}

.mt-16 {
  margin-top: 16rpx;
}

.pr-0 {
  padding-right: 0 !important;
}

/**
 * 限时抢购标签
 */
.limit-buy-tag {
  position: absolute;
  top: 0;
  left: 0;
  height: 32rpx;
  color: #fff;
  padding-right: 16rpx;
  overflow: hidden;
  text {
    position: relative;
    z-index: 4;
    display: block;
    background: #ff3d47;
    padding-left: 8rpx;
    font-size: 20rpx;
    font-weight: 400;
    line-height: 32rpx;
    height: 32rpx;
    max-width: 100rpx;
    overflow: hidden;
  }
  .bg {
    position: absolute;
    right: 0;
    top: 0;
    width: 104rpx;
    height: 32rpx;
    z-index: 2;
  }
}

.limit-buy-tag-search {
  top: 16rpx;
}

.row-list-item {
  flex-direction: column;
  align-items: flex-start;
}

.group-buy-tag {
  margin-right: 16rpx;
  display: inline-block;
  height: 32rpx;
  line-height: 32rpx;
  vertical-align: middle;
  padding: 0 8rpx;
  background-color: #ff3d47;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
}

/* 字体加粗 */
.fw-b {
  font-weight: bold;
}

.fw-n {
  font-weight: 400;
}

// 重写uni样式
.uni-picker-view-mask {
  height: calc(100% + 8rpx);
}

.uni-input-placeholder,
.uni-input-input,
.uni-textarea-placeholder,
.uni-textarea-textarea {
  font-size: 28rpx;
}

.uni-progress-bar,
.uni-progress-inner-bar {
  border-radius: 16rpx;
}

uni-actionsheet .uni-actionsheet__cell,
uni-actionsheet .uni-actionsheet__title {
  font-size: 15px;
}

.uni-input-input:disabled {
  color: #999;
}
.wine-btn-bg {
  width: 100%;
  height: 100%;
  background-image: url('https://hishop-wine.obs.cn-south-1.myhuaweicloud.com:443/hishop/images/wechat/mini/fengtan/btn-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-blend-mode: overlay;
}
.price-box {
  display: flex;
  align-items: baseline;
}
.num-int {
  font-weight: 600;
  font-family: 'LatoBold';
  font-size: 32rpx;
}
.copy-btn {
  width: 56rpx;
  height: 32rpx;
  background: #f7eeec;
  border-radius: 4rpx;
  font-size: 20rpx;
  color: #af573e;
  margin-left: 16rpx;
}

.custom-input-box {
  background-color: #f5f4f3;
  padding: 16rpx 24rpx;
  margin-top: 16rpx;
  position: relative;

  .textarea {
  }

  .limit-tip {
    position: absolute;
    bottom: 20rpx;
    right: 24rpx;
  }
}

.custom-input-single {
  height: 56rpx;

  .textarea {
    width: 80%;
    height: 100%;
    line-height: 100%;
    line-height: 40rpx;
    height: 40rpx;
  }
}

.triangle {
  position: relative;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 12px solid black;
}

.divider {
  width: 100%;
  height: 1rpx;
  margin: 24rpx auto;
  background-color: #eee;
}

