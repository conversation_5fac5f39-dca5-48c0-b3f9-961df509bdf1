<template>
  <view class="container">
    <div class="card">
      <div class="header">快递信息</div>
      <div class="form-item">
        <label class="label">物流单号</label>
        <span class="value">
          {{ detail.deliveryTrackNumber }}
          <span class="tag tag--small" @click="copy">复制</span>
        </span>
      </div>
      <div class="form-item">
        <label class="label">物流公司</label>
        <span class="value">{{ detail.deliveryCompanyName }}</span>
      </div>
    </div>

    <div class="card">
      <uni-steps :options="traces" active-color="#0b7d83" direction="column" />
    </div>
  </view>
</template>

<script>
import { logisticsQuery } from '@/service/common'
import UniSteps from '@/uni_modules/uni-steps/components/uni-steps/uni-steps.vue'

export default {
  components: { UniSteps },
  data() {
    return {
      detail: {},
      phoneNumber: '',
      traces: [],
    }
  },

  onLoad(options) {
    this.detail = JSON.parse(decodeURIComponent(options.detail))
    this.phoneNumber = options.phoneNumber

    logisticsQuery({
      logisticCode: this.detail.deliveryTrackNumber,
      shipperCode: this.detail.deliveryCompanyCode,
      phoneLastNumber: this.phoneNumber,
    }).then(({ data }) => {
      this.traces = data.traces.map(t => ({
        title: t.acceptStation,
        desc: t.acceptTime,
      }))
    })
  },

  methods: {
    copy() {
      uni.setClipboardData({
        data: this.detail.deliveryTrackNumber,
        success: () => {
          uni.showToast({
            title: '复制成功',
          })
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - 48rpx);
  padding: 24rpx 0;
  background-color: #f5f5f5;
}

.card {
  padding: 32rpx 24rpx;
  margin: 24rpx;
  margin-top: 0;
  background-color: #fff;
  border-radius: 16rpx;

  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 104rpx;
  border-bottom: 1rpx solid #eee;

  .label {
    width: 150rpx;
    color: #262626;
    font-weight: 500;
  }

  .value {
    //flex: 1;
    //height: 104rpx;
  }
}

.tag {
  padding: 16rpx 40rpx;
  list-style: none;
  color: #0b7d83;
  background-color: #f2f8f8;
  border-radius: 36rpx;

  &--small {
    padding: 4rpx 8rpx;
    margin-left: 16rpx;
    font-size: 20rpx;
  }
}
</style>
