<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '取酒订单' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <div style="padding: 24rpx; background-color: #FAF9F7;">
          <uni-easyinput
            v-model="query.goodsName"
            prefixIcon="search"
            placeholder="请输入藏酒商品名称"
            :clearable="false"
            trim="all"
            confirm-type="search"
            primary-color="#0b7d83"
            :styles="{ backgroundColor: '#eee' }"
            @confirm="init(true)"
          />
        </div>

        <ul class="tabs">
          <li
            v-for="item in orderStatuses"
            :key="item.value"
            class="tab"
            @click="handleTabClick(item.value)"
          >
            {{ item.label }}
          </li>
          <li class="line" :style="{ left: tabLeft }" />
        </ul>

        <scroll-view :scroll-y="true" style="height: 100vh" @scrolltolower="init(false)">
          <div v-for="item in list" :key="item.id" class="card">
            <div class="header" @click="goDetail(item)">
              <span>订单号：{{ item.orderNo }}</span>
              <span
                :class="
                  item.orderStatus === 'FINISHED'
                    ? 'text-primary'
                    : item.orderStatus === 'CLOSED'
                    ? 'text-gray'
                    : 'text-brown'
                "
              >
                {{ item.orderStatusDesc }}
              </span>
            </div>
            <div class="inner" @click="goDetail(item)">
              <div class="meta">
                <img :src="getDepositImg(item) | formatUrl" class="cover" />

                <div class="flex-grow-1">
                  <p class="title">{{ item.goodsName }}</p>

                  <p
                    v-for="subpackage in item.subpackageInfoList"
                    :key="subpackage.id"
                    class="desc"
                  >
                    <span>{{ subpackage.name }}</span>
                    <span>x{{ subpackage.number }}</span>
                  </p>
                </div>
              </div>
            </div>

            <div class="flex flex-justify-between flex-align-center">
              <span class="text-gray">{{ item.createTime || '--' }}</span>

              <span>
                容量总计：
                <span class="text-brown">{{ item.totalCapacity | ml2L }}L</span>
              </span>
            </div>

            <div class="flex flex-justify-end" style="gap: 16rpx">
              <button
                v-if="item.orderStatus === 'IN_CONFIRMED'"
                class="btn"
                @click="cancelOrder(item)"
              >
                取消订单
              </button>
              <!--            <button v-if="item.orderStatus === 'UN_RECEIVED'" class="btn" @click="confirm(item)">-->
              <!--              确认收货-->
              <!--            </button>-->
              <button
                v-if="!['FINISHED', 'CLOSED'].includes(item.orderStatus) && promoter.id"
                class="btn btn-primary"
                @click="$refs.promoter.open()"
              >
                联系我们
              </button>
            </div>
          </div>

          <div v-if="!list || !list.length" class="empty">
            <img
              class="img"
              :src="
                `${OSS_PREFIX}hishop/upload/9lUMYzegJh9LgY4yDCCVS.png?x-image-process=image/resize,m_lfit,w_170`
              "
              alt=""
            />
            <p class="title">取酒订单为空</p>
          </div>
        </scroll-view>
      </div>
    </div>

    <promoter ref="promoter" />
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import { cancelTake, confirmReceive, getTakeList } from '@/service/jingBrand/take'
import { OSS_PREFIX } from '@/config/system'
import Promoter from '@/pages/index/jingBrand/components/Promoter.vue'
import UniEasyinput from '@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue'

export default {
  name: 'list',
  components: { CustomNav, Promoter, UniEasyinput },
  data() {
    return {
      OSS_PREFIX,
      statusBarHeight: 168,
      orderStatuses: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '待确认',
          value: 'IN_CONFIRMED',
        },
        {
          label: '生产中',
          value: 'IN_PRODUCTION',
        },
        {
          label: '待收货',
          value: 'UN_RECEIVED',
        },
        {
          label: '已完成',
          value: 'FINISHED',
        },
        {
          label: '已取消',
          value: 'CLOSED',
        },
      ],
      query: {
        pageNo: 1,
        pageSize: 10,
        orderStatus: '',
        goodsName: '',
      },
      list: [],
      hasMore: true,
      promoter: {},
    }
  },
  computed: {
    tabLeft() {
      const index = this.orderStatuses.findIndex(t => t.value === this.query.orderStatus)
      const itemWidth = 100 / this.orderStatuses.length
      return `${itemWidth * index + itemWidth / 2}%`
    },
  },
  async onLoad({ orderStatus }) {
    this.query.orderStatus = orderStatus || ''
    this.init(true)
    this.promoter = await this.$refs.promoter.initMyPromoter()
  },
  methods: {
    async init(reset = false) {
      if (reset) {
        this.query.pageNo = 1
        this.hasMore = true
        this.list = []
      } else {
        this.query.pageNo++
      }

      if (!this.hasMore) return

      try {
        const res = await getTakeList(this.query)
        if (reset) {
          this.list = res.data.list
        } else {
          res.data.list.forEach(item => {
            this.list.push(item)
          })
        }

        this.hasMore = this.query.pageNo * this.query.pageSize < res.data.total
      } catch (e) {
        console.log(e)
        // this.list = []
      }
    },

    handleTabClick(value) {
      this.query.orderStatus = value
      this.init(true)
    },

    getDepositImg(item) {
      if (!item.extractionCustomDepositInfoVos) return ''
      return item.extractionCustomDepositInfoVos[0].depositImgList[0]
    },

    cancelOrder(item) {
      uni.showModal({
        title: '提示',
        content: '是否确认取消订单？',
        success: res => {
          if (res.confirm) {
            cancelTake({ id: item.id }).then(() => {
              this.init(true).then(() => {
                uni.showToast({ title: '取消成功' })
              })
            })
          }
        },
      })
    },

    confirm(item) {
      uni.showModal({
        title: '提示',
        content: '是否确认收货',
        success: res => {
          if (res.confirm) {
            confirmReceive({ id: item.id }).then(() => {
              this.init(true).then(() => {
                uni.showToast({ title: '确认收货成功' })
              })
            })
          }
        },
      })
    },

    goDetail(item) {
      uni.navigateTo({
        url: `/jingBrand/take/detail?id=${item.id}`,
      })
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  padding-bottom: 24rpx;
  background-color: #f5f4f3;
}

//
//.content {
//  margin: 24rpx;
//  padding: 32rpx 24rpx;
//  background-color: #fff;
//  border-radius: 16rpx;
//}
.card {
  margin: 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background-color: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }
}

.text-brown {
  color: #bc9173;
}

.text-gray {
  color: #b0b0b0;
}

.text-primary {
  color: #0b7d83;
}

.tabs {
  position: relative;
  display: flex;
  justify-content: space-around;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #faf9f7;

  .tab {
    flex: 1;
    line-height: 88rpx;
    text-align: center;

    //&--active {
    //  color: #0b7d83;
    //  border-bottom: 8rpx solid #0b7d83;
    //}
  }

  .line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 32rpx;
    height: 8rpx;
    background-color: #0b7d83;
    border-radius: 4rpx;
    transform: translateX(-50%);
    transition: left 0.2s ease;
  }
}

.inner {
  position: relative;
  margin: 24rpx auto;
  padding: 24rpx;
  background-color: #f9f9f9;
  border-radius: 4rpx;
}

.meta {
  display: flex;
}

.cover {
  width: 160rpx;
  height: 160rpx;
  min-width: 160rpx;
  margin-right: 16rpx;
  background-color: #f5f4f3;
}

.title {
  margin-bottom: 24rpx;
  font-size: 28rpx;
}

.desc {
  display: flex;
  justify-content: space-between;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120rpx;
  height: 120rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 280rpx;
    height: 280rpx;
  }

  .title {
    margin-top: 0;
    color: #999;
  }
}

.btn {
  margin: 24rpx 0 0;
  padding: 16rpx 32rpx;
  line-height: 40rpx;
  border: 1px solid #eee;
  &-primary {
    color: #0b7d83;
    border-color: #0b7d83;
    background-color: #fff;
  }
}
</style>
