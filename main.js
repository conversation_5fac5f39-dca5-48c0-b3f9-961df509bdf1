import Vue from 'vue'
import App from './App'
import store from './store'
import { checkLogin, phoneMasking, nameMasking } from '@/common/utils'
import navigateTo from '@/hooks/navigateTo'
import formatUrl from '@/hooks/formatUrl'
import ml2L from '@/hooks/ml2L'
Vue.filter('formatUrl', formatUrl)
Vue.filter('phoneMasking', phoneMasking)
Vue.filter('nameMasking', nameMasking)
Vue.filter('ml2L', ml2L)
Vue.prototype.$navigateTo = navigateTo
Vue.prototype.$checkLogin = checkLogin
App.mpType = 'app'

const app = new Vue({
  store,
  ...App,
})
app.$mount()
