<template>
  <div>
    <uni-popup ref="popup" type="bottom" @change="onClose">
      <div class="take-popup">
        <img
          src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/ZjQ1X7E9OD--xtzzTl4rf.png"
          class="close"
          @click="close"
        />

        <div class="goods-item">
          <img :src="(detail.imgList && detail.imgList[0]) | formatUrl" class="cover" />

          <div class="meta" style="padding-right: 48rpx">
            <p class="title">{{ detail.name }}</p>
            <p class="desc">
              <span>可取容量：</span>
              <span class="text-brown">{{ detail.sumOverCapacity | ml2L }}L</span>
            </p>
          </div>
        </div>

        <div v-if="isSpecialGoodId" class="form-item flex-wrap">
          <label class="label">取酒数量</label>
          <stepper
            :value.sync="takeNumber"
            :min="0"
            :max="takeMaxNumber"
            :input-disabled="!takeMaxNumber"
          />
        </div>

        <template v-else>
          <div class="form-item flex-wrap" @click="edit">
            <label class="label">已选坛号</label>
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/a5qUsM2OCKcF5zA204Beq.png"
              class="edit"
            />

            <ul class="tags">
              <li v-if="!deposits.length" style="font-size: 24rpx; color: #999">请先选择坛号</li>
              <li v-for="item in deposits" :key="item.id" class="tag">{{ item.depositNo }}</li>
            </ul>
          </div>
          <!--        {{ JSON.stringify(subpackageInfoList.map(t => t.max)) }}-->

          <!--        <span v-for="item in subpackageInfoList.map(t => t.max)" :key="item.id">{{ item }}</span>-->

          <div
            v-if="detail.subpackageInfoList && detail.subpackageInfoList.length"
            class="form-item flex-wrap"
          >
            <label class="label">产品规格</label>

            <scroll-view :scroll-y="true" style="height: 480rpx">
              <div v-for="group in subpackageGroups">
                <view v-if="group.label" class="subtitle">{{ group.label }}</view>

                <div
                  v-for="subpackage in group.list"
                  :key="subpackage.id"
                  class="goods-item goods-item--sm"
                >
                  <div
                    style="position: relative; border-radius: 4rpx; overflow: hidden"
                    @click="showContentPopup(subpackage)"
                  >
                    <img :src="subpackage.effectImgs | formatUrl" class="cover" />
                    <p
                      v-if="subpackage.subpackageType"
                      style="
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        width: 100%;
                        padding-block: 8rpx;
                        background-color: rgba(0, 0, 0, 0.55);
                        text-align: center;
                        font-size: 10px;
                        color: #fff;
                      "
                    >
                      {{ getSubpackageTypeLabel(subpackage.subpackageType) }}
                    </p>
                  </div>

                  <div class="meta" style="padding-right: 48rpx">
                    <p class="title">{{ subpackage.name }}</p>
                    <p class="desc">
                      <!--                {{ subpackage.capacity }}ml * {{ subpackage.num }}-->
                      <template v-if="subpackage.minCreateNum">
                        起订量{{ subpackage.minCreateNum }}{{ subpackage.unit }}
                      </template>
                    </p>
                  </div>

                  <div class="flex flex-align-center">
                    <stepper
                      :value.sync="subpackage.number"
                      :min="0"
                      :max.sync="subpackage.max"
                      :input-disabled="!deposits.length"
                      @change="calcSubpackageItemMaxNumber(subpackage)"
                    />
                    <span>{{ subpackage.unit }}</span>
                  </div>
                </div>
              </div>
            </scroll-view>
          </div>
        </template>

        <button class="btn" @click="confirm">下一步</button>
      </div>
    </uni-popup>

    <pick-jar-code
      ref="pickJarCode"
      :goods-id="goodsId"
      :customer-id="customerId"
      multiple
      @confirm="onPickJarCodeConfirm"
    />

    <uni-popup ref="contentPopup" type="bottom">
      <view class="take-popup">
        <scroll-view :scroll-y="true" style="height: 600rpx; overflow-y: auto">
          <rich-text :nodes="formatRichText(currentSubpackage.content)" />
        </scroll-view>
      </view>
    </uni-popup>
  </div>
</template>

<script>
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import PickJarCode from './PickJarCode.vue'
import Stepper from '@/components/stepper/stepper.vue'
import { OSS_PREFIX } from '@/config/system'
import { numAdd, numMultiply, numSubtract } from '@/common/utils'
import { getJarCodes, getStockDetail } from '@/service/jingBrand/stock'
import { mapState } from 'vuex'

export default {
  name: 'SendTo',
  components: { PickJarCode, UniPopup, Stepper },
  props: {
    goodsId: {
      type: Number | String,
      required: true,
    },
    customerId: {
      type: Number | String,
      required: true,
    },
  },
  data() {
    return {
      OSS_PREFIX,
      detail: {
        subpackageInfoList: [],
      },
      depositList: [],
      deposits: [],
      takeNumber: 0,
      takeMaxNumber: 0,
      currentSubpackage: {},
    }
  },
  computed: {
    ...mapState(['specialGoodsIds']),
    /**
     * 规格参数的总容量 => 每个项的容量×已选择的数量
     * @returns {number}
     */
    totalCapacity() {
      return (
        this.detail.subpackageInfoList.reduce(
          (prev, current) =>
            numAdd(
              prev,
              numMultiply(numMultiply(current.capacity, current.num), current.number || 0)
            ),
          0
        ) || 0
      )
    },
    /**
     * 酒坛里面的最小容量
     * @returns {number}
     */
    minJarCapacity() {
      return Math.min(...this.deposits.map(t => t.capacity)) || 0
    },
    /**
     * 酒坛所有的容量累加
     * @returns {number}
     */
    totalJarCapacity() {
      return this.deposits.reduce((prev, current) => numAdd(prev, current.capacity), 0) | 0
    },
    subpackageGroups() {
      const isAllFree = this.detail.subpackageInfoList.every(item => item.izFree)
      if (isAllFree) return [{ label: '', list: this.detail.subpackageInfoList }]
      const isAllPay = this.detail.subpackageInfoList.every(item => !item.izFree)
      if (isAllPay) return [{ label: '付费区', list: this.detail.subpackageInfoList }]

      return [
        {
          label: '免费区',
          list: this.detail.subpackageInfoList.filter(t => t.izFree),
        },
        {
          label: '付费区',
          list: this.detail.subpackageInfoList.filter(t => !t.izFree),
        },
      ]
    },
    /**
     * 特殊的藏酒id, 不用选择已选坛号, 根据填的取酒数量自动选择坛号
     */
    isSpecialGoodId() {
      return this.specialGoodsIds.includes(parseInt(this.goodsId))
    },
  },
  methods: {
    showContentPopup(subpackage) {
      if (!subpackage.content) return
      this.currentSubpackage = subpackage
      this.$refs.contentPopup.open()
    },
    /**
     * 处理富文本里的图片宽度自适应
     */
    formatRichText(html) {
      if (!html) return ''

      console.log(
        html.replace(
          /<img/gi,
          '<img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;"'
        )
      )

      return html
        .replace(
          /<img/gi,
          '<img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;"'
        )
        .replace(/\\"/gi, "'")
    },
    getSubpackageTypeLabel(value) {
      const item = [
        {
          label: '木箱分装',
          value: 'WOODEN',
        },
        {
          label: '主题分装',
          value: 'THEME',
        },
        {
          label: '坛藏分装',
          value: 'ALTAR',
        },
        {
          label: '通用分装',
          value: 'COMMON',
        },
        {
          label: '定向分装',
          value: 'DIRECTIONAL',
        },
        {
          label: '简易分装',
          value: 'SIMPLE',
        },
        {
          label: '百斤坛取',
          value: 'BAIJIN',
        },
      ].find(t => t.value === value)
      if (!item) return ''
      return item.label
    },
    async open() {
      this.$refs.popup.open()

      if (this.isSpecialGoodId) {
        getJarCodes({
          goodsId: this.goodsId,
          targetCustomId: this.customerId,
          capacityFilter: true,
        }).then(({ data, code }) => {
          if (code === '200') {
            this.takeNumber = 0
            this.takeMaxNumber = data?.length || 0
            this.depositList = data
          }
        })
      }

      const { code, data } = await getStockDetail({
        goodsId: this.goodsId,
        targetCustomId: this.customerId,
      })
      if (code === '200') {
        data.subpackageInfoList.forEach(subpackage => {
          subpackage.number = 0
          subpackage.max = 0
        })
        this.detail = data
      }
    },
    close() {
      this.$refs.popup.close()
    },
    onClose() {
      this.deposits = []
    },
    edit() {
      this.$refs.pickJarCode.open(this.goodsId)
    },
    /**
     *
     * @param value
     * @param value.id ID
     * @param value.depositNo 编号
     * @param value.capacity 剩余容量
     */
    onPickJarCodeConfirm(value) {
      this.deposits = value
      this.calcSubpackageItemMaxNumber()
    },
    /**
     * 计算规格每项的可设置的最大值
     */
    calcSubpackageItemMaxNumber(exclude) {
      if (!this.detail.subpackageInfoList) return
      this.detail.subpackageInfoList.forEach(subpackage => {
        if (exclude && exclude === subpackage) {
          return
        }

        const itemTotalCapacity = numMultiply(subpackage.capacity, subpackage.num)

        subpackage.max = Math.floor(
          (this.totalJarCapacity -
            (this.totalCapacity - numMultiply(itemTotalCapacity, subpackage.number || 0))) /
            itemTotalCapacity
        )
      })
      this.$forceUpdate()
    },
    async confirm() {
      // 如果是特殊藏酒，直接根据取酒数量自动取前几位的坛号
      if (this.isSpecialGoodId) {
        this.deposits = this.depositList.slice(0, this.takeNumber)
      }

      if (!this.deposits.length) {
        return uni.showToast({
          title: this.isSpecialGoodId ? '请选择数量' : '请选择坛号',
          icon: 'none',
        })
      }
	
      if (this.detail.subpackageInfoList && this.detail.subpackageInfoList.length) {
        if (this.totalCapacity === 0) {
          return uni.showToast({
            title: '请输入取酒规格',
            icon: 'none',
          })
        }
	

        if (this.deposits.length > 1 && this.totalCapacity <= this.minJarCapacity) {
          return uni.showToast({
            title: '当前取酒容量其中一坛已满足要求，请重新提交',
            icon: 'none',
            duration: 3000,
          })
        }

        const subpackage = this.detail.subpackageInfoList.find(
          subpackage => subpackage.number > 0 && subpackage.number < subpackage.minCreateNum
        )

        if (subpackage) {
          uni.showToast({
            title: `${this.getSubpackageTypeLabel(subpackage.subpackageType)}最低起订量${
              subpackage.minCreateNum
            }${subpackage.unit}，请重新选择`,
            icon: 'none',
            duration: 3000,
          })
          return
        }
      }
	  
	  if(this.subpackageGroups.length) {
	  		 
	  		  let sumCapacity = 0 //取的容量
	  		  let finished = true
	  		  this.subpackageGroups.forEach(group => {
	  			  group.list.forEach(item => {
	  				  sumCapacity= numAdd(sumCapacity,numMultiply(numMultiply(item.capacity,item.number),item.num))
	  				  if(item.max > item.number) finished = false
	  			  })
	  		  })
	  		  
	  		  if(!finished) { //没有取完
	  			  let deposits = [this.detail.sumOverCapacity] //容器大小
	  			  if(this.deposits.length) {
	  					deposits = this.deposits.sort(function (a, b) {
	  						return a.capacity - b.capacity
	  					}).map(p => {
	  						return p.capacity
	  					})
	  			  }
	  			  try {
	  				  deposits.forEach(item => {
	  					  if(item <= sumCapacity) {
	  						  sumCapacity =  numSubtract(sumCapacity,item)
	  					  }	
	  					  else {
	  						  const surplus = numSubtract(item,sumCapacity)
	  						  if(surplus < 10000)  throw new Error('此次取酒后剩余藏酒数量将低于10L，过少的酒体长时间储存会引发酒体质量不稳定的可能性，建议尽早取出')
	  					  }
	  				  })
	  			  } catch(e) {
					  uni.showModal({
					    title: '提示',
					    content: e.message,
					    success: res => {
					      if (res.confirm) {
					        this._submit()
					      }
					    },
					  })
	  				return
	  			  }
	  		  }
	  }
	  
	  this._submit()


    },
	_submit() {
		uni.setStorageSync('takeOrderSubmit', {
		  goodsId: this.goodsId,
		  goodsName: this.detail.name,
		  targetCustomId: this.customerId,
		  deposits: this.deposits,
		  subpackageInfoList: this.detail.subpackageInfoList.filter(t => t.number > 0),
		})
		
		this.close()
		uni.navigateTo({
		  url: `/jingBrand/take/submit`,
		})
	}
  },
}
</script>

<style lang="scss">
.take-popup {
  position: relative;
  padding: 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .text-brown {
    color: #bc9173;
  }

  .close {
    position: absolute;
    width: 48rpx;
    height: 48rpx;
    right: 24rpx;
    top: 24rpx;
  }

  .form-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    //min-height: 104rpx;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #eee;

    .label {
      width: 150rpx;
      color: #262626;
      font-weight: 500;
    }

    .subtitle {
      margin-top: 24rpx;
      font-size: 24rpx;
      color: #999;
    }

    .input {
      flex: 1;
      height: 104rpx;
    }

    .edit {
      width: 32rpx;
      height: 32rpx;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      min-width: 100%;
      padding: 0;
      margin: 24rpx auto 0;
    }

    .tag {
      padding: 16rpx 40rpx;
      list-style: none;
      color: #0b7d83;
      background-color: #f2f8f8;
      border-radius: 36rpx;
    }
  }

  .btn {
    margin-top: 32rpx;
    padding-top: 24rpx;
    padding-bottom: 24rpx;
    line-height: 40rpx;
    color: #fff;
    background-color: #0b7d83;
    border-color: #0b7d83;
  }

  .goods-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 8rpx auto;

    .cover {
      width: 160rpx;
      height: 160rpx;
      background-color: #ddd;
    }

    .meta {
      flex: 1;
      margin-left: 16rpx;
    }

    .title {
      font-size: 32rpx;
      font-weight: 600;
    }

    .desc {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #999;
    }

    &--sm {
      margin: 24rpx 0;
      .cover {
        width: 104rpx;
        height: 104rpx;
      }

      .title {
        font-size: 28rpx;
      }
    }
  }
}
</style>
