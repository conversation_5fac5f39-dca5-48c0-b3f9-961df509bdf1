<template>
  <view class="container-root 123">
		888
		<view class="vertical-input">
		  <textarea class="vertical-input-content" placeholder="请输入内容"></textarea>
		</view>
  </view>
</template>

<script>
import store from '@/store'
import { SET_ISLOGIN } from '@/store/mutation-types'
import { mapActions } from 'vuex'
export default {
  components: {},
  data() {
    return {}
  },
  computed: {},
  methods: {
    ...mapActions(['setUserInfo']),
    handleLogOut() {
     
    }
  }
}
</script>

<style lang="scss" scoped> 
.container-root{
	padding: 200rpx;
}
 .vertical-input {
   width: 200px;
   height: auto;
   writing-mode: vertical-lr; /* 文字从上到下排列 */
 }
 
 .vertical-input-content {
   width: 100%;
   resize: none; /* 禁止调整大小 */
   text-orientation: mixed; /* 控制文字方向 */
   white-space: pre-wrap; /* 保留换行符 */
 }
</style>
