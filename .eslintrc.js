// module.exports = {
// 	extends: [
// 		'plugin:vue/recommended'
// 	],
// 	parserOptions: {
// 		'ecmaVersion': 'latest',
// 		'sourceType': 'module',
// 		'ecmaFeatures': {
// 			'jsx': true
// 		},
// 		'allowImportExportEverywhere': false
// 	},
// 	rules: {
// 		'no-alert': 0,
// 		 // 禁止多个空格 
// 		'no-multi-spaces': 'error',
// 		// 自动补充分号
// 		'semi': [2, 'always'], 
// 		// 使用单引号
// 		'quotes': ['error', 'single'], 
// 		//不允许重复的keys
// 		'vue/no-dupe-keys': 'error',
// 	}
// };