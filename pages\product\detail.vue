<template>
  <view class="container-root" :class="{ transparent: !isInit }">
    <!-- 自定义导航 -->
    <custom-nav :data="{ barBgColor: 'transparent' }" />
    <!-- 礼品预览图 -->
    <view class="swiper-wrap" id="product">
      <swiper class="swiper" @change="handleSwiperChange" circular autoplay id="swiper">
        <swiper-item class="swiper-item" v-if="product.mainVideo">
          <image mode="aspectFit" :src="(product.videoCover || thumbnail) | formatUrl(750)"></image>
          <image @click="handleStartVideo" :src="'static/applet/start.png' | formatUrl" class="start-btn" />
        </swiper-item>
        <swiper-item v-for="(item, index) in productBanner" :key="index" class="swiper-item">
          <image @click="handlePrievew(index)" mode="aspectFit" :src="item | formatUrl(750)"></image>
        </swiper-item>
      </swiper>
      <view class="pagination">{{ productBannerPage }}/{{ product.mainVideo ? productBanner.length + 1 : productBanner.length }}</view>
      <!-- 视频 -->
      <view class="video-wrap" :class="{ 'video-init': video.init }" v-if="product.video">
        <video id="productVideo" objectFit="contain" :enable-progress-gesture="video.progress" :src="product.mainVideo | formatUrl" @ended="handleVideoEnded"></video>
        <view class="close"><text @click="handleCloseVideo">退出播放</text></view>
      </view>
    </view>

    <view class="summary">
      <view class="min">
        <text v-if="product.marketPrice">¥{{ product.marketPrice }}</text>
      </view>
      <view class="title-wrap">
        <view class="title">{{ product.productName }}</view>
        <button class="share" open-type="share">
          <text class="iconfont icon-share"></text>
        </button>
      </view>
    </view>

    <view class="block-title">
      <view class="title">商品详情</view>
    </view>
    <view class="product-detail">
      <image v-for="item in product.productDetailImgList" :key="item" :src="item | formatUrl" mode="widthFix"></image>
    </view>

    <!-- 底部固定导航 -->
    <view class="footer-nav-wrap">
      <view class="nav-list">
        <button class="nav-item" @click="handleToIndex()">
          <text class="iconfont icon-home-o"></text>
          <text>首页</text>
        </button>
        <button class="nav-item" hover-class="button-h" open-type="contact">
          <text class="iconfont icon-bubble"></text>
          <text>客服</text>
        </button>
      </view>
      <view class="handle">
        <button class="btn btn-disabled" :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }">您可前往最近的门店进行了解</button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { GetProductInfo } from '@/service/common'
import CustomNav from '@/components/customNav/customNav'

export default {
  components: {
    CustomNav
  },
  data() {
    return {
      isInit: false,
      product: {},
      productBanner: [],
      previewImages: [],
      productBannerPage: 1,
      productId: 0,
      video: {
        init: false,
        progress: false
      },
      thumbnail: ''
    }
  },
  computed: {
    ...mapState(['obsUrl', 'setting', 'theme', 'userInfo', 'isLogin'])
  },
  onLoad(options) {
    if (options.productId) {
      this.productId = parseInt(options.productId)
      this.getProductDetail()
    }
  },
  methods: {
    handleToIndex() {
      this.$navigateTo({
        path: '/pages/index/index'
      })
    },
    handleVideoEnded() {
      this.handleCloseVideo()
    },
    handleCloseVideo() {
      this.video.init = false
      this.videoContext.pause()
    },
    handleStartVideo() {
      this.video.init = true
      this.videoContext.play()
    },

    handleSwiperChange(event) {
      this.productBannerPage = parseInt(event.detail.current) + 1
    },
    handlePrievew(index) {
      uni.previewImage({
        current: index,
        urls: this.previewImages
      })
    },

    getProductDetail() {
      GetProductInfo(this.productId)
        .then((res) => {
          const data = res.data
          this.product = data

          this.thumbnail = data.productImg
          const productBanner = []
          const previewImages = []
          data.productImgList.map((item) => {
            productBanner.push(item)
            previewImages.push(this.$options.filters.formatUrl(item))
          })
          this.productBanner = productBanner
          this.previewImages = previewImages

          uni.setNavigationBarTitle({
            title: this.product.productName
          })

          this.isInit = true
        })
        .catch(() => {
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        })
    },
    initShare(isApplet) {
      let title = this.product.productName
      let path = `pages/product/detail?productId=${this.productId}`
      let imageUrl = this.productBanner[0]
      if (isApplet) {
        imageUrl = this.$options.filters.formatUrl(imageUrl, 750)
      }
      return {
        title,
        path,
        imageUrl
      }
    }
  },

  mounted() {},
  onShareAppMessage() {
    return this.initShare(true)
  },
  onShareTimeline() {
    return this.initShare(true)
  }
}
</script>

<style lang="scss" scoped>
.container-root {
  background-color: #f4f5f6;
  padding-bottom: 104rpx;
}

.swiper-wrap,
.swiper {
  width: 100%;
  height: 750rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.swiper-wrap {
  position: relative;
  background-color: #fff;

  .pagination {
    position: absolute;
    right: 24rpx;
    bottom: 24rpx;
    width: 64rpx;
    height: 48rpx;
    line-height: 48rpx;
    text-align: center;
    color: #fff;
    border-radius: 32rpx;
    background-color: rgba(0, 0, 0, 0.5);
    font-size: 20rpx;
  }
}

.summary {
  padding: 20rpx 24rpx 24rpx;
  position: relative;
  background-color: #fff;

  .min {
    color: #ff3d47;
    font-size: 72rpx;
    line-height: 88rpx;
    font-weight: bold;

    text {
      font-weight: normal;
      font-size: 48rpx;
    }
  }

  .title {
    flex: 1;
    overflow: hidden;
    line-height: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }

  .title-wrap {
    display: flex;

    .share {
      position: static;
      padding: 0;
      background: transparent;

      .iconfont {
        margin: 0;
        font-size: 48rpx;
        color: #f52440;
      }
    }

    .share-wrap {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      justify-content: flex-end;
      padding-left: 40rpx;
    }
  }

  .explain {
    margin-top: 16rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    color: #8f8f8f;
  }

  .share {
    position: absolute;
    top: 37rpx;
    right: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 56rpx;
    font-size: 28rpx;
    color: #8f8f8f;
    margin: 0;

    &::after {
      display: none;
    }

    .iconfont {
      font-size: 30rpx;
      margin-right: 12rpx;
    }
  }
}

.block-title {
  padding: 24rpx;
  margin-top: 24rpx;
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
  background-color: #fff;

  .title {
    color: #5c5c5c;
  }

  .right {
    flex: 1;
    overflow: hidden;
    text-align: right;
  }

  .iconfont {
    margin-left: 8rpx;
    font-size: 22rpx;
    color: #212121;
  }
}

.video-wrap {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 750rpx;
  display: none;
  z-index: 100;
  background-color: #000;

  video {
    width: 100%;
    height: 680rpx;
    overflow: hidden;
  }

  .close {
    text-align: center;
    padding-bottom: 8rpx;

    text {
      display: inline-block;
      padding: 6rpx 16rpx;
      line-height: 28rpx;
      font-size: 20rpx;
      border-radius: 28rpx;
      background-color: rgb(245, 245, 245);
    }
  }
}

.video-init {
  display: block;
}

.video-float {
  position: fixed;
  top: 126rpx;
  right: 24rpx;
  width: 352rpx;
  height: 352rpx;

  &::after {
    content: ' ';
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 4px solid $colorred;
    transform: scale(0.5);
    transform-origin: 0 0;
    box-sizing: border-box;
  }
}

.swiper-wrap {
  .start-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120rpx;
    height: 120rpx;
  }
}

.product-detail {
  image {
    display: block;
    width: 100%;
  }
}
</style>
