<template>
  <!-- 弹窗 -->
  <view :class="{ 'model-container': true, 'model-container-show': show }">
    <view class="mask" @click="handleCancel"></view>
    <view class="main">
      <view class="title">{{ title }}</view>
      <view class="body">
        <slot name="bd"></slot>
      </view>
      <view class="btn-wrap border-t">
        <view class="btn" v-if="showCancel" @click="handleCancel">取消</view>
        <view class="btn" @click="handleOk" :style="{ color: confirmColor || theme.primary.color6 + ' !important' }">{{ confirmText }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    display: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    confirmColor: {
      type: String,
      default: ''
    },
    showCancel: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    confirmText: {
      type: String,
      default: '确认'
    }
  },
  data() {
    return {
      show: false
    }
  },
  computed: {
    ...mapState(['setting', 'theme'])
  },
  watch: {
    show(value) {
      this.$emit('update:display', value)
    },
    display(value) {
      this.show = value
    }
  },
  methods: {
    handleOk() {
      this.$emit('ok', { success: true })
    },
    handleCancel() {
      this.show = false
      this.$emit('cancel', { success: false })
    }
  }
}
</script>

<style lang="scss" scoped>
.mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.3);
}
.model-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  min-height: 100vh;
  z-index: 200;
}

.main {
  position: absolute;
  top: 20%;
  left: 50%;
  z-index: 2;
  width: 603rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  transform: translateX(-50%);
}

.title {
  padding: 32rpx 34rpx;
  line-height: 48rpx;
  font-size: 32rpx;
  text-align: center;
}

.body {
  padding: 0 60rpx 48rpx;
}
.btn-wrap {
  display: flex;
  &:before {
    z-index: 10;
  }
  .btn {
    flex: 1;
    overflow: hidden;
    padding: 24rpx 0;
    text-align: center;
    color: $colorgray;
    line-height: 48rpx;
    font-size: 30rpx;
    position: relative;

    &::after {
      @include border-line(20rpx, auto, 20rpx, 0);
    }

    &:first-child {
      &:after {
        display: none;
      }
    }
  }
}

.model-container-show {
  display: block;
}
</style>
