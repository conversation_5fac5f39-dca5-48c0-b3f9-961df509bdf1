<template>
	<view class="timelineItem">
		<view class="timeItem">
	<!-- 		<view class="leftTime">
				{{leftTime}}
			</view> -->
			<view class="line">
				<view class="cur-out" v-if="current">
					 <image src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/images/basicSystem/1747470030485848064.svg"></image>
				</view>
				<view class="out" v-else :style="{background: color == '' ? '' : color}">
					<view class="inner" :style="{background: color == '' ? '' : color}"></view>
				</view>
			</view>
			<view class="rightContent">
				<slot></slot>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		props:{
			leftTime:{
				type:String,
				default:''
			},
			color:{
				type:String,
				default:''
			},
			current:{
				type:Boolean,
				default:false
			}
		},
		data(){
			return {
				
			}
		}
	}
</script>

<style scoped lang="less">
	.timelineItem {
		.timeItem {
			display: flex;
			.leftTime {
				width: 135rpx;
				padding: 0 10rpx;
				font-size:22rpx;
				font-family:PingFangSC-Medium,PingFang SC;
				font-weight:500;
				color:rgba(51,51,51,1);
				margin-right: 10rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.line {
				width: 1px;
				background:#EBE9E8;
				position: relative;
				.out {
					position: absolute;
					top: 6rpx;
					left: 50%;
					transform: translateX(-50%);
					display: flex;
					justify-content: center;
					align-items: center;
					width: 10rpx;
					height: 10rpx;
					border-radius: 50%;
					background: #FFFFFF;
					opacity: 1;
					border: 3px solid #D9D8D7;
				}
			}
			.rightContent {
				flex: 1;
				padding: 0 10rpx 40rpx;
				margin-left: 20rpx;
				min-height: 50rpx;
			}
		}
	}
	.cur-out{
		position: absolute;
		top: 0;
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40rpx;
		height: 40rpx;
	}
</style>
