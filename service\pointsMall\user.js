import request from '@/config/request'

// 获取用户信息
export const GetUserHome = (data) => request({ url: 'pointsMall/user/mini/home', data })
// 获取用户积分
export const GetUserPoints = (data) => request({ url: 'pointsMall/member/points/mini/getUserPoints', data, showLoading: false })
// 获取积分明细
export const GetPointsDetail = (data) => request({ url: 'pointsMall/member/points/mini/pageDetail', data, method: 'post' })

// 获取积分交易记录
export const GetTradeList = (data) => request({ url: 'pointsMall/mine/mini/tradeList', data, method: 'post' })
// 获取我的中奖记录
export const GetMyGift = (data) => request({ url: 'pointsMall/raffle/mini/myGift', data, method: 'post' })
