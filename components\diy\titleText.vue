<template>
  <view class="title-text" :style="{ backgroundColor: dataset.bgColor, textAlign: dataset.textAlign === 2 ? 'center' : dataset.textAlign === 3 ? 'right' : '' }">
    <template v-if="dataset.showType === 1">
      <view class="h3" :style="{ fontSize: dataset.titleSize + 'px', fontWeight: dataset.titleWeight ? '700' : '', color: dataset.titleColor }">
        <text class="title">{{ dataset.title }}</text>
        <text class="more" @click="skip(dataset.link)" v-if="dataset.showMore" :style="{ lineHeight: dataset.titleSize * 1.375 + 'px', color: dataset.moreStyle === 2 ? '#38f' : '' }">
          {{ dataset.moreStyle === 3 ? '' : dataset.moreText }}
          <text v-if="dataset.moreStyle !== 2" class="iconfont icon-arrow-right"></text>
        </text>
      </view>
      <view v-if="dataset.description" class="p" :style="{ fontSize: dataset.descSize + 'px', fontWeight: dataset.descWeight ? '700' : '', color: dataset.descColor }">{{ dataset.description }}</view>
      <view class="border-b" v-if="dataset.showBorder"></view>
    </template>
    <template v-else>
      <view class="h3">{{ dataset.title }}</view>
      <view class="p">
        <text>{{ dataset.date }}</text>
        <text>{{ dataset.author }}</text>
        <text @click="skip(dataset.link)">{{ dataset.linkText }}</text>
      </view>
    </template>
  </view>
</template>

<script>
export default {
  props: {
    dataset: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    skip(link) {
      if (!link) {
        return
      }
      this.$navigateTo({
        ...link
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.title-text {
  position: relative;
  padding: 32rpx;
  .h3 {
    font-size: 32rpx;
    line-height: 1.375;
    display: flex;
    .title {
      flex: 1;
      word-break: break-word;
    }
    .more {
      margin-left: 48rpx;
      white-space: nowrap;
      font-size: 24rpx;
      font-weight: normal;
      color: #969799;
      text {
        font-size: 24rpx;
      }
    }
  }
  .p {
    margin-top: 16rpx;
    font-size: 24rpx;
    color: #8c8c8c;
    word-break: break-word;
    text {
      margin-right: 24rpx;
      &:last-child {
        color: #607fa6;
      }
    }
  }
  .border-b {
    position: absolute;
    left: 32rpx;
    right: 32rpx;
    bottom: 0;
  }
}
</style>
