<template>
  <!-- 密码弹窗 -->
  <view class="model-container" :class="{ 'model-container-show': show }">
    <view class="mask"></view>
    <view class="main">
      <view class="password-body">
        <view class="password-wrap">
          <view class="password-inner" :class="{ 'password-inner-page': page === 2 }">
            <view class="body-item" @click="handleFocus">
              <view class="title" v-if="title">{{ title }}</view>
              <view class="input border">
                <text class="item" v-for="(item, index) in passwordList" :key="index" :class="{ active: password1.length >= index + 1 }"><text></text></text>
              </view>
            </view>
            <view class="body-item" @click="handleFocus">
              <view class="title" v-if="title1">{{ title1 }}</view>
              <view class="input border">
                <text class="item" v-for="(item, index) in passwordList" :key="index" :class="{ active: password2.length >= index + 1 }"><text></text></text>
              </view>
            </view>
          </view>
          <view class="password-input">
            <!-- 不用 v-modal 因为要存储上一次密码 -->
            <input type="number" class="input" :value="password" :focus="isFocus" password @input="handleInput" @focus="handleGetFocus" @blur="handleGetBlur" />
          </view>
        </view>
        <view class="error color-red">{{ passwordError }}</view>
        <view class="forget" v-if="type == 2" @click="handleOpenPassword">忘记密码?</view>
      </view>

      <view class="btn-wrap border-t">
        <view @click="handleCancel">取消</view>
        <view class="color-red" @click="handleOk" :style="{ color: theme.primary.color6 + ' !important' }">确认</view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    type: {
      type: [String, Number],
      default: 2 // 1 设置  2 验证
    },
    title: String,
    title1: String,
    display: {
      type: Boolean,
      default: false
    },
    passwordLethe: {
      type: String,
      default: '../user/payPassword'
    }
  },
  data() {
    return {
      show: this.display,
      password: '',
      isFocus: false,
      page: 1,
      password1: '',
      password2: '',
      passwordList: [1, 2, 3, 4, 5, 6],
      passwordError: '',
      timer: null
    }
  },
  computed: {
    ...mapState(['setting', 'theme']),
    passwordLen() {
      return this.password.length
    }
  },
  watch: {
    show(value) {
      this.$emit('update:display', value)
    },
    display(value) {
      this.show = value
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.isFocus = value
      }, 150)
    }
  },
  methods: {
    handleGetBlur() {
      this.isFocus = false
      // console.log('失去焦点', this.isFocus)
    },
    handleGetFocus() {
      // console.log('获得焦点成功', this.isFocus)
    },
    handleOpenPassword() {
      uni.navigateTo({
        url: this.passwordLethe
      })
    },
    handleInput(event) {
      let value = event.detail.value
      if (value.replace(/\s/g, '').length > 12) {
        return this.password
      }
      value = value.replace(/\s/g, '').replace(/(\d{6})(?=\d)/g, '$1 ')
      const reg = value.split(' ')
      this.password1 = ''
      this.password2 = ''
      if (reg) {
        if (reg[0]) {
          this.password1 = reg[0]
          this.page = 1
          if (this.password1.length > 5 && this.type === parseInt(1)) {
            this.page = 2
          }
        }
        if (reg[1]) {
          this.password2 = reg[1]
        }
      }
      this.password = value
    },
    handleFocus() {
      setTimeout(() => {
        this.isFocus = true
      }, 50)
    },
    handleCancel() {
      this.password = ''
      this.password1 = ''
      this.password2 = ''
      this.passwordError = ''

      this.$emit('cancel')
    },
    handleOk() {
      if (!this.password) {
        return (this.passwordError = '密码不能为空')
      }

      if (!/^\d+$/.test(this.password1)) {
        return (this.passwordError = '密码必须为纯数字')
      }

      // 如果是设置模式
      if (this.type === 1) {
        // 进行密码校验
        if (this.password1 != this.password2) {
          return (this.passwordError = '两次密码输入不一致！')
        }
        this.passwordError = ''
        return this.$emit('ok', this.password1)
      }

      //校验模式
      if (this.password1.length < 6) {
        return (this.passwordError = '密码输入不正确')
      }
      this.passwordError = ''
      this.$emit('ok', this.password1)
    }
  }
}
</script>

<style lang="scss" scoped>
.password-wrap {
  position: relative;
  width: 480rpx;
  height: 192rpx;
  margin: 0 auto;
  // overflow:hidden;
}

.password-input {
  position: absolute;
  bottom: 0;
  right: 0;
  left: -100%;
  height: 80rpx;
  z-index: 9;
  input {
    width: 100%;
    height: 80rpx;
    opacity: 0;
  }
}
.password-inner-page {
  transform: translateX(-100%);
}

.error {
  line-height: 40rpx;
  font-size: 24rpx;
}

.password-inner {
  position: relative;
  z-index: 2;
  background-color: #fff;
  display: flex;
  flex-wrap: nowrap;
  transition: all 0.3s;
  .body-item {
    .input {
      position: relative;
      height: 80rpx;
      width: 480rpx;
      display: flex;

      &::after {
        border-radius: 0;
      }
    }

    .item {
      width: 80rpx;
      height: 80rpx;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      &:after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 1px;
        height: 160rpx;
        transform: scale(0.5);
        transform-origin: 0 0;
        box-sizing: border-box;
        background-color: $colorborder;
      }
      &:first-child {
        &:after {
          display: none;
        }
      }
      text {
        display: block;
        width: 16rpx;
        height: 16rpx;
        background-color: #fff;
        border-radius: 50%;
      }
    }

    .active {
      text {
        background-color: #212121;
      }
    }
  }
}

.title {
  padding: 32rpx 34rpx;
  line-height: 46rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
}

.error {
  line-height: 40rpx;
  font-size: 24rpx;
}

.mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

.model-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  min-height: 100vh;
  z-index: 200;

  .main {
    position: absolute;
    top: 20%;
    left: 50%;
    width: 603rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    transform: translateX(-50%);
  }

  .title {
    padding: 32rpx 34rpx;
    line-height: 48rpx;
    font-size: 32rpx;
    text-align: center;
  }

  .password-body {
    width: 480rpx;
    padding-bottom: 48rpx;
    margin: 0 auto;
    overflow: hidden;
    .page1 {
      margin-right: 16rpx;
    }
  }
  .btn-wrap {
    display: flex;
    view {
      flex: 1;
      overflow: hidden;
      padding: 24rpx 0;
      text-align: center;
      color: $colorgray;
      line-height: 40rpx;
      font-size: 30rpx;
      position: relative;

      &::after {
        @include border-line(20rpx, auto, 20rpx, 0);
      }

      &:first-child {
        &:after {
          display: none;
        }
      }
    }
  }
}

.model-container-show {
  display: block;
}

.forget {
  margin-left: 300rpx;
  padding-top: 16rpx;
  text-align: right;
  color: #6a7fa6;
  line-height: 40rpx;
}
</style>
