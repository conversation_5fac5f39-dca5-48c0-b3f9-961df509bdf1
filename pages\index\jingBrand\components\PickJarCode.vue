<template>
  <div>
    <uni-popup ref="dialog" type="dialog" :mask-click="false">
      <div class="pick-jar-code-popup">
        <p class="title">选择坛号</p>

        <scroll-view scroll-y="true" class="list">
          <div v-for="item in list" :key="item.id" class="item" @click="onItemClick(item)">
            <radio style="transform: scale(0.833)" color="#0B7D83" :checked="item.checked" />
            <div>
              <p class="code">贮藏编号：{{ item.depositNo }}</p>
              <p class="subtitle">
                剩余容量：{{ item.capacity | ml2L }}L / {{ item.totalCapacity | ml2L }}L
              </p>
            </div>
          </div>

          <div v-if="!list.length" class="empty">
            <img :src="'hishop/upload/QNGtoFmiFJYuEd3VCl_UH.png' | formatUrl" class="img" />
            <p class="title">暂无数据</p>
          </div>
        </scroll-view>

        <div class="actions">
          <span class="btn" @click="close">取消</span>
          <span class="btn btn-primary" @click="confirm">确认</span>
        </div>
      </div>
    </uni-popup>
  </div>
</template>

<script>
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import { getJarCodes } from '@/service/jingBrand/stock'
import { OSS_PREFIX } from '@/config/system'

export default {
  name: 'PickJarCode',
  components: { UniPopup },
  props: {
    goodsId: {
      type: Number | String,
      required: true,
    },
    customerId: {
      type: Number | String,
      required: true,
    },
    // 目前赠酒是单选，取酒是多选
    multiple: {
      type: Boolean,
      default: true,
    },
    capacityFilter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      list: [],
    }
  },
  // computed: {
  //   // 这个字段是专门给后台准备的
  //   capacityFilter() {
  //     return this.multiple
  //   },
  // },
  methods: {
    OSS_PREFIX() {
      return OSS_PREFIX
    },
    open() {
      this.list = []
      getJarCodes({
        goodsId: this.goodsId,
        targetCustomId: this.customerId,
        capacityFilter: this.capacityFilter,
      }).then(({ code, data }) => {
        if (code === '200') {
          data.forEach(item => {
            item.checked = false
          })
          this.list = data
        }
      })
      this.$refs.dialog.open()
    },

    close() {
      this.$refs.dialog.close()
      this.$emit('close')
    },

    onItemClick(item) {
      if (this.multiple) {
        item.checked = !item.checked
      } else {
        this.list.forEach(_item => {
          _item.checked = false
          item.checked = true
        })
      }
    },

    confirm() {
      this.$refs.dialog.close()
      this.$emit(
        'confirm',
        this.list.filter(item => item.checked)
      )
    },
  },
}
</script>

<style lang="scss">
.pick-jar-code-popup {
  padding: 40rpx;
  background-color: #fff;

  .title {
    text-align: center;
    line-height: 48rpx;
    font-size: 36rpx;
    font-weight: 600;
  }

  .list {
    max-height: 400rpx;
  }

  .item {
    display: flex;
    align-items: center;
    gap: 16rpx;
    width: 480rpx;
    padding: 24rpx;
    margin: 24rpx auto;
    background-color: #f9f9f9;
    border-radius: 8rpx;
  }

  .code {
    font-size: 28rpx;
    color: #262626;
  }

  .subtitle {
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #999;
  }

  .actions {
    display: flex;
    justify-content: space-around;
    gap: 16rpx;
    margin-top: 16rpx;
  }

  .btn {
    padding: 16rpx 88rpx;
    line-height: 40rpx;
    border: 1rpx solid #ddd;
    border-radius: 36rpx;
  }

  .btn-primary {
    background-color: #0b7d83;
    border-color: #0b7d83;
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48rpx 0;

    .img {
      width: 280rpx;
      height: 280rpx;
    }

    .title {
      margin-top: 0;
      color: #999;
    }
  }
}
</style>
