<template>
  <view class="container-root">
    <view class="cell-list-item-small">
      <view class="aside">姓名</view>
      <view class="main">
        <input type="text" placeholder="收货人姓名" maxlength="10" v-model="form.receiveName" />
      </view>
    </view>
    <view class="cell-list-item-small">
      <view class="aside">电话</view>
      <view class="main">
        <input
          type="number"
          placeholder="收货人手机号"
          maxlength="11"
          v-model="form.receivePhone"
        />
      </view>
    </view>
    <view class="cell-list-item-small" @click="showDistpicker = true">
      <view class="aside">地区</view>
      <view :class="{ main: true, 'main-default': !form.areaCode }">
        <view class="address overflow-ellipsis">{{
          `${form.province}${form.city}${form.area}` || '请选择地址'
        }}</view>
      </view>
      <view class="other">
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
    <view class="cell-list-item-small">
      <view class="aside">详细地址</view>
      <view class="main">
        <input type="text" placeholder="街道门牌、楼层房间号等信息" v-model="form.address" />
      </view>
    </view>

    <view class="cell-list-item-small" v-if="!show">
      <view class="aside">是否默认</view>
      <view class="main align-right">
        <switch
          style="transform: scale(0.9)"
          color="#6FC246"
          :checked="form.izDefault"
          @change="switchChange"
        />
      </view>
    </view>

    <view class="btns">
      <button
        class="btn btn-primary"
        @click="addAddress"
        :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }"
      >
        确定
      </button>
      <button class="btn" v-if="form.id !== 0 && !show" @click="delAddress">
        <text class="border-line"></text>
        <text class="del">删除</text>
      </button>
    </view>

    <!-- 地址选择器 -->
    <address-select
      :display.sync="showDistpicker"
      :defaultId="form.areaCode || form.cityCode"
      :activeList="addressData"
      @ok="handleAddressOk"
    />
  </view>
</template>

<script>
import { DeleteAddress, GetAddressDetail, SaveAddress } from '@/service/common'
import AddressSelect from '@/components/addressSelect/addressSelect'
import { mapActions, mapState } from 'vuex'

export default {
  components: {
    AddressSelect,
  },
  data() {
    return {
      show: true,
      showDistpicker: false,
      city: 0,
      type: 1,
      form: {
        province: '',
        provinceCode: '',
        city: '',
        cityCode: '',
        area: '',
        areaCode: '',
      },
      addressData: [], // 选择地址的标准格式，用于默认地址展示
    }
  },
  computed: {
    ...mapState(['setting', 'theme']),
  },
  onLoad(option) {
    if (option.id) {
      this.loadData(option.id)
    }
    if (option.type) {
      this.type = option.type
    }
    this.city = parseInt(option.city)
  },
  methods: {
    ...mapActions(['setCacheAddressAction']),
    handleAddressOk(list) {
      const [provinceItem, cityItem, areaItem] = list

      this.form.province = provinceItem.name
      this.form.provinceCode = provinceItem.id

      this.form.city = cityItem.name
      this.form.cityCode = cityItem.id

      this.form.area = areaItem?.name || ''
      this.form.areaCode = areaItem?.id || null

      this.addressData = list
    },
    loadData(id) {
      GetAddressDetail(id).then(res => {
        const data = res.data
        this.form = data
        this.show = this.form.izDefault

        // 地址选择器的数据格式
        this.addressData = [
          {
            id: data.provinceCode,
            name: data.province,
            parentId: 0,
          },
          {
            id: data.cityCode,
            name: data.city,
            parentId: data.provinceCode,
          },
          data.areaCode
            ? {
                id: data.areaCode,
                name: data.area,
                parentId: data.cityCode,
              }
            : null,
        ].filter(Boolean)
      })
    },
    switchChange(e) {
      this.form.izDefault = e.target.value
    },

    addAddress() {
      const { form } = this
      if (!form.receiveName) {
        return uni.showToast({
          icon: 'none',
          title: '请填写收货人姓名',
        })
      }
      if (form.receivePhone?.length !== 11) {
        return uni.showToast({
          icon: 'none',
          title: '请填写11位收货人电话',
        })
      }
      if (!form.cityCode) {
        return uni.showToast({
          icon: 'none',
          title: '请选择地区',
        })
      }
      if (!form.address) {
        return uni.showToast({
          icon: 'none',
          title: '请填写详细地址',
        })
      }

      SaveAddress(form).then(res => {
        uni.showToast({
          title: '保存成功',
        })
        form.id = res.data

        if (this.type === '2') {
          this.setCacheAddressAction({
            ...form,
            id: res.data,
          })
          uni.navigateBack({
            delta: 2,
          })
        } else {
          uni.navigateBack({
            delta: 1,
          })
        }
      })
    },
    delAddress() {
      DeleteAddress({
        id: this.form.id,
      }).then(res => {
        uni.navigateBack()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.container-root {
  background-color: $colorbackground;
  min-height: 100vh;
}

.cell-list-item-small {
  position: relative;

  &::after {
    @include border-line(autp, 0, 0, 24rpx);
  }

  .aside {
    width: 168rpx;
    line-height: 40rpx;
  }

  .main {
    padding-right: 0;
  }

  .main-default {
    color: $colorgraylight;
    view {
      color: $colorgraylight;
    }
  }

  .address {
    font-size: 30rpx;
    // text-align: right;
  }

  .icon-location {
    font-size: 48rpx;
  }
}

.btns {
  margin-top: 24rpx;
  padding: 24rpx;

  .btn {
    margin-bottom: 24rpx;
    border-radius: 44rpx;
    line-height: 88rpx;
    .border-line {
      border-color: $colorborder;
      border-radius: 88rpx;
    }
  }

  .del {
    color: #ff3d47;
  }

  .btn-primary {
    .border-line {
      display: none;
    }
  }
}

input {
  font-size: 30rpx;
}
</style>
