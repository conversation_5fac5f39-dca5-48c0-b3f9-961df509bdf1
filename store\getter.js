export default {
  cartTotalQuantity: state => {
    // 计算购物车总数量
    let totalQuantity = 0

    if (state.cartData) {
      Object.keys(state.cartData).map(p => {
        const item = state.cartData[p]
        if (item.status === 1) {
          totalQuantity += parseInt(item.quantity)
        }
      })
    }
    // console.log('走计算', state.cartData)
    return totalQuantity
  },
  isRookie: state => {
    const isRookie = !state.isLogin || (state.userInfo && !state.userInfo.consumptionTime)
    return isRookie
  },
  rookiePages: state => { //1是首页，2商品详情，1|2首页和商品详情
    const rookiePages = state.setting ? (state.setting.rookiePages ? state.setting.rookiePages : 0) : 0
    return rookiePages
  }
}