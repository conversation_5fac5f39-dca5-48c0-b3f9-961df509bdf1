<template>
  <!-- 地址选择插件 -->
  <view :class="{ 'address-select-contaier': true, 'address-select-show': show }">
    <view class="mask" @click="handleClose"></view>
    <view class="address-select-body">
      <view class="title border-b">请选择所在地区</view>
      <view class="select-wrap">
        <text
          class="text"
          v-for="(item, index) in selectList"
          :key="index"
          :class="{ active: index === tabIndex }"
          @click="handleTabChange({ id: item.id, hasSub: false, index, name: item.name })"
        >
          <text class="bar" :style="{ backgroundColor: theme.primary.color6 }"></text>
          <text>{{ item.name ? item.name : item.default }}</text>
        </text>
      </view>
      <view class="select-list-wrap">
        <view
          class="item"
          v-for="item in list"
          :key="item.id"
          @click="handleGetChild({ id: item.id, hasSub: item.hasChild, name: item.name })"
        >
          <text
            class="iconfont icon-radio-cell"
            v-if="item.select"
            :style="{ color: theme.primary.color6 }"
          ></text>
          {{ item.name }}
        </view>
      </view>

      <text class="iconfont icon-cross" @click="handleClose"></text>
    </view>
  </view>
</template>

<script>
import { GetRegions } from '@/service/common'
import { getRegionByParentCode } from '@/service/jingBrand/market-area'
import { mapState } from 'vuex'

export default {
  props: {
    display: Boolean,
    activeList: Array,
    defaultId: String | Number,
  },
  data() {
    return {
      show: false,
      selectList: [
        {
          default: '请选择',
          hasSub: true,
          name: '',
        },
      ],
      list: [],
      activeId: 0,
      tabIndex: 0,
      nowIndex: 0,
      lastIndex: 0,
    }
  },
  watch: {
    show(value) {
      if (value) {
        this.setInit()
        this.getCitys(this.defaultId)
      }
      this.$emit('update:display', value)
    },
    display(value) {
      this.show = value
    },
  },
  computed: {
    ...mapState(['setting', 'theme']),
  },
  methods: {
    handleClose() {
      this.show = !this.show
    },
    handleTabChange({ id, index }) {
      this.tabIndex = index
      this.nowIndex = index
      const selectId = this.selectList[index].parentId
      this.getCitys(selectId)
    },
    handleGetChild(params) {
      // 结果设置选中
      this.setSelectList({
        id: params.id,
        name: params.name,
        hasSub: params.hasSub,
      })

      if (params.hasSub) {
        this.getCitys(params.id)
      }
    },
    setInit() {
      const selectList = [
        {
          name: '请选择',
          parentId: 0,
        },
      ]
      let nowIndex = 0
      let defaultId = 0
      if (this.activeList.length > 0) {
        selectList.length = 0
        this.activeList.map((item, index) => {
          const isLast = index < this.activeList.length - 1 ? false : true
          selectList.push({
            parentId: item.parentId,
            id: item.id,
            name: item.name,
            hasSub: isLast ? false : true,
            default: '请选择',
          })
        })
        nowIndex = this.activeList.length - 1
        defaultId = this.activeList[nowIndex].parentId
      }
      this.selectList = selectList
      this.nowIndex = nowIndex
      this.tabIndex = nowIndex

      this.defaultId = defaultId
    },
    setSelectList({ id, name, hasSub, index }) {
      // 更新当前层级数据，创建切选中下一级，如果没有下一级，不选中和创建下一级
      this.nowIndex += 1
      const data = this.selectList[this.nowIndex - 1]
      if (hasSub) {
        this.tabIndex = this.nowIndex
      }

      if (data) {
        ;(data.id = id), (data.name = name)
        data.hasSub = hasSub
        if (!data.parentId) {
          data.parentId = 0
        }
      }
      this.selectList.splice(this.nowIndex, this.selectList.length - this.nowIndex)
      if (hasSub && !this.selectList[this.nowIndex]) {
        this.selectList.push({
          parentId: id,
          default: '请选择',
        })
      }

      if (!hasSub) {
        this.show = false
        const result = []
        this.selectList.map(item => {
          result.push({
            parentId: item.parentId,
            name: item.name,
            id: item.id,
          })
        })
        this.$emit('ok', result)
      }
    },
    getCitys(id) {
      getRegionByParentCode({
        parentAdcode: id || '',
      }).then(res => {
        const newArray = []
        const activeData = this.selectList[this.tabIndex]
        res.data.forEach(item => {
          const obj = {
            id: item.adcode,
            name: item.name,
            hasChild: item.hasChild > 0,
            select: false,
          }
          if (activeData) {
            if (item.adcode === activeData.id) {
              obj.select = true
            }
          }
          newArray.push(obj)
        })
        this.list = newArray
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.address-select-contaier {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 200;
  transform: translateY(100%);

  .mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.address-select-show {
  transform: translateY(0);

  .address-select-body {
    transform: translateY(0);
  }
}

.address-select-body {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  padding: 0 24rpx;
  height: 824rpx;
  padding: 0 24rpx;
  background-color: #fff;
  flex-direction: column;
  transition: all 0.3s;
  transform: translateY(100%);

  .title {
    padding: 26rpx 0;
    line-height: 46rpx;
    font-weight: bold;
    font-size: 32rpx;
    text-align: center;
  }
}

.select-wrap {
  padding: 0 16rpx;
  line-height: 42rpx;
  white-space: nowrap;

  .text {
    position: relative;
    display: inline-block;
    padding: 50rpx 0 16rpx;
    margin-right: 48rpx;

    &:last-child {
      margin-right: 0;
    }

    .bar {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 8rpx;
      background-color: $colorred;
      border-radius: 4rpx;
      display: none;
    }
  }

  .active {
    .bar {
      display: block;
    }
  }
}

.select-list-wrap {
  padding-top: 20rpx;
  flex: 1;
  overflow-y: scroll;

  .item {
    display: flex;
    padding: 20rpx 16rpx;
    font-size: 26rpx;
    align-items: center;
    line-height: 36rpx;
    color: $colorgray;
  }

  .iconfont {
    margin-left: -16rpx;
    color: $colorred;
    font-size: 64rpx;
  }
}

.icon-cross {
  position: absolute;
  top: 29rpx;
  right: 24rpx;
  font-size: 40rpx;
  color: $colorgraydeep;
}
</style>
