{
    "name" : "劲牌藏酒",
    "appid" : "__UNI__D851F22",
    "description" : "劲牌封藏移动端",
    "versionName" : "1.0",
    "versionCode" : 1,
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "softinputNavBar" : "none",
        /* 模块配置 */
        "modules" : {
            "Payment" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Maps" : {},
            "Geolocation" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "autoSdkPermissions" : true
            },
            /* ios打包配置 */
            "ios" : {
                "idfa" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "获取相册图片用于上传图片",
                    "NSPhotoLibraryAddUsageDescription" : "保存海报图片到相册",
                    "NSCameraUsageDescription" : "拍照上传图片",
                    "NSLocationWhenInUseUsageDescription" : "获取当前位置匹配最近同城配送门店",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "获取当前位置匹配最近同城配送门店",
                    "NSMicrophoneUsageDescription" : "客服聊天发送语音"
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx992840e0ab735341",
                        "UniversalLinks" : "https://demo.histore.kuaidiantong.cn/"
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wx992840e0ab735341",
                        "UniversalLinks" : "https://demo.histore.kuaidiantong.cn/"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "12a61acf6029dcf8eb46105429462867",
                        "appkey_android" : "2d884882921aea3064fe59e2d45b77ec"
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "12a61acf6029dcf8eb46105429462867",
                        "appkey_android" : "2d884882921aea3064fe59e2d45b77ec"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx53bc232e2ecc4bf4",
        "setting" : {
            "urlCheck" : true,
            "es6" : true,
            "postcss" : false,
            "minified" : true
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "用于获取当前位置"
            }
        },
        "requiredPrivateInfos" : [ "chooseAddress", "chooseLocation", "getLocation" ],
        "uniStatistics" : {
            "enable" : false
        },
        "optimization" : {
            "subPackages" : true
        },
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "appid" : "tt2c5b5ae6b371ebc3"
    },
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "R63BZ-PAOCQ-UUR56-GINBO-DOLLQ-CEBTP"
                }
            }
        },
        "domain" : "https://javashop.hisiyouyun.top/m",
        "router" : {
            "mode" : "hash",
            "base" : "/m/"
        },
        "devServer" : {
            "disableHostCheck" : true,
            "proxy" : {
                "" : {
                    "target" : "http://**************:8080/",
                    "changeOrigin" : true,
                    "secure" : false
                }
            }
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        }
    }
}
/* 模块配置 */

