<template>
  <div>
    <uni-popup ref="popup" type="bottom">
      <div class="send-top-popup">
        <img
          src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/ZjQ1X7E9OD--xtzzTl4rf.png"
          class="close"
          @click="close"
        />
        <p class="title">赠酒</p>

        <div class="form-item flex-wrap" @click="edit">
          <label class="label">已选坛号</label>
          <img
            src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/a5qUsM2OCKcF5zA204Beq.png"
            class="edit"
          />

          <ul v-if="form.deposits" class="tags">
            <li v-for="item in form.deposits" :key="item.id" class="tag">{{ item.depositNo }}</li>
          </ul>
        </div>

        <p class="form-item">
          <label for="name" class="label">受赠人</label>
          <input
            v-model="form.toCustomName"
            id="name"
            type="text"
            placeholder="请输入受赠人姓名"
            :cursor-spacing="40"
            :always-embed="true"
            maxlength="20"
            class="input"
          />
        </p>

        <p class="form-item">
          <label for="phone" class="label">手机号</label>
          <input
            v-model="form.toCustomPhone"
            id="phone"
            type="number"
            placeholder="请输入手机号"
            :cursor-spacing="40"
            :always-embed="true"
            maxlength="11"
            class="input"
          />
        </p>

        <button class="btn" :loading="saving" @click="confirm">下一步</button>
      </div>
    </uni-popup>

    <pick-jar-code
      ref="pickJarCode"
      :goods-id="goodsId"
      :customer-id="customerId"
      multiple
      :capacityFilter="false"
      @confirm="onPickJarCodeConfirm"
    />

    <uni-popup ref="protocolPopup" type="bottom">
      <div class="protocol-popup">
        <img
          src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/ZjQ1X7E9OD--xtzzTl4rf.png"
          class="close"
          @click="protocolClose"
        />
        <p class="title">免责协议</p>

        <scroll-view scroll-y class="scroll-view">
          <article v-html="protocol"></article>
        </scroll-view>

        <div class="text-center mt-24">
          <radio
            style="transform: scale(0.833);"
            color="#0B7D83"
            :checked="isAgree"
            @click="isAgree = !isAgree"
          >
            我已阅读并同意 <span class="text-brown">《劲牌封藏转赠免责声明》</span>
          </radio>
        </div>

        <button :disabled="!isAgree" class="btn mt-24" @click="next">确定</button>
      </div>
    </uni-popup>
  </div>
</template>

<script>
import UniPopup from '@/components/uni-popup/uni-popup.vue'
import PickJarCode from './PickJarCode.vue'
import { GetAgreement } from '@/service/common'
import { checkCreateSendWineOrder } from '@/service/jingBrand/send'

export default {
  name: 'SendTo',
  components: { PickJarCode, UniPopup },
  props: {
    goodsId: {
      type: Number | String,
      required: true,
    },
    customerId: {
      type: Number | String,
      required: true,
    },
  },
  data() {
    return {
      form: {
        deposits: [],
        toCustomName: '',
        toCustomPhone: '',
      },
      saving: false,
      protocol: '',
      isAgree: false,
    }
  },
  async created() {
    const res = await GetAgreement(3)
    this.protocol = res.data
  },
  methods: {
    open() {
      this.$refs.popup.open()
    },
    close() {
      this.form.deposits = []
      this.form.toCustomName = ''
      this.form.toCustomPhone = ''
      this.$refs.popup.close()
    },
    protocolClose() {
      this.isAgree = false
      this.$refs.protocolPopup.close()
    },
    edit() {
      this.$refs.pickJarCode.open(this.goodsId)
    },
    onPickJarCodeConfirm(value) {
      console.log(value)
      this.form.deposits = value
    },
    async confirm() {
      if (!this.form.deposits.length) {
        return uni.showToast({
          title: '请选择坛号',
          icon: 'none',
        })
      }
      if (!this.form.toCustomName) {
        return uni.showToast({
          title: '请输入受赠人姓名',
          icon: 'none',
        })
      }
      if (!this.form.toCustomPhone) {
        return uni.showToast({
          title: '请输入受赠人手机号',
          icon: 'none',
        })
      }

      if (!/^1[3-9]\d{9}$/.test(this.form.toCustomPhone)) {
        return uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        })
      }

      await checkCreateSendWineOrder({
        depositIds: this.form.deposits.map(t => t.id),
        toCustomName: this.form.toCustomName,
        toCustomPhone: this.form.toCustomPhone,
      })

      this.$refs.protocolPopup.open()
    },

    async next() {
      this.saving = true
      try {
        uni.setStorageSync('sendOrderSubmit', this.form)
        this.close()
        this.protocolClose()
        uni.navigateTo({
          url: `/jingBrand/send/confirm?goodsId=${this.goodsId}&customerId=${this.customerId}&isReceive=false`,
        })
        // const { code, data } = await createSendWineOrder(this.form)
        // if (code === '200') {

        // }
      } finally {
        this.saving = false
      }
    },
  },
}
</script>

<style lang="scss">
.send-top-popup {
  position: relative;
  padding: 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .title {
    margin-bottom: 48rpx;
    text-align: center;
    line-height: 48rpx;
    font-size: 36rpx;
    font-weight: bold;
  }

  .close {
    position: absolute;
    width: 48rpx;
    height: 48rpx;
    right: 24rpx;
    top: 24rpx;
  }

  .form-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 104rpx;
    border-bottom: 1rpx solid #eee;

    .label {
      width: 150rpx;
      color: #262626;
      font-weight: 500;
    }

    .input {
      flex: 1;
      height: 104rpx;
    }

    .edit {
      width: 32rpx;
      height: 32rpx;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      min-width: 100%;
      padding: 0;
      margin: 24rpx auto;
    }

    .tag {
      padding: 16rpx 40rpx;
      list-style: none;
      color: #0b7d83;
      background-color: #f2f8f8;
      border-radius: 36rpx;
    }
  }

  .btn {
    margin-top: 182rpx;
  }
}

.btn {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
  line-height: 40rpx;
  color: #fff;
  background-color: #0b7d83;
  border-color: #0b7d83;
}

.text-brown {
  color: #bc9173;
}

.text-center {
  text-align: center;
}

.dialog {
  background-color: #fff;
}

.protocol-popup {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .title {
    margin-bottom: 48rpx;
    text-align: center;
    line-height: 48rpx;
    font-size: 36rpx;
    font-weight: bold;
  }

  .close {
    position: absolute;
    width: 48rpx;
    height: 48rpx;
    right: 24rpx;
    top: 24rpx;
  }

  .scroll-view {
    height: 800rpx;
  }
}
</style>
