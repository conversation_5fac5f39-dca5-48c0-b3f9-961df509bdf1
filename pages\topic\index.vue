<template>
  <view :style="{ paddingTop: page.header && page.header.enableBar === false ? 0 : statusBarHeight + 'rpx' }">
    <custom-nav v-if="header.barColor" :data="header" @init="statusBarHeight = $event" />

    <diy v-if="id" url="wine/micropage/mini/detail" :params="{ id }" @load="loadDiy" method="get" />
  </view>
</template>

<script>
import { mapState } from 'vuex'
import Diy from '@/components/diy/index'
import CustomNav from '@/components/customNav/customNav'

export default {
  components: {
    Diy,
    CustomNav
  },
  data() {
    return {
      id: 0,
      header: {},
      statusBarHeight: 128
    }
  },
  computed: {
    ...mapState('setting')
  },
  methods: {
    loadDiy(page) {
      this.header = page.header
    }
  },
  onLoad(options) {
    this.id = options.id
  },

  onShareAppMessage() {
    return {
      title: this.header.title
    }
  },
  onShareTimeline() {
    return {
      title: this.header.title
    }
  }
}
</script>

<style lang="scss" scoped></style>
