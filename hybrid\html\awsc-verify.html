<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<meta name="format-detection" content="telephone=no,email=no" />
		<title>阿里云滑动验证码</title>
		<style>
			html,
			body {
				margin: 0;
				padding: 0;
				background: tranparent;
			}

			.verify {
				position: fixed;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				z-index: 10;
				background-color: rgba(0, 0, 0, 0.3);
			}

			.verify-inner {
				position: absolute;
				top: 50%;
				left: 50%;
				width: 332px;
				transform: translate(-50%, -50%);
				border-radius: 8px;
				background-color: #fff;

			}

			.verify-inner .title {
				padding: 12px;
				line-height: 22px;
				font-size: 16px;
				font-weight: bold;
				text-align: center;
				position: relative;
			}

			.verify-inner .title::after {
				content: '';
				position: absolute;
				right: 12px;
				bottom: 0;
				left: 12px;
				background: #E5E5E5;
				height: 1px;
				transform: scaleY(.5);
			}

			.verify-inner .body {
				padding: 16px;
			}

			.verify-inner .body .text {
				text-align: center;
				line-height: 20px;
				color: #747474;
				font-size: 13px;
			}

			.nc-container {
				position: relative !important;
				height: 34px;
				margin: 16px auto 12px;
			}

			.nc-container .nc_scale {
				border-radius: 17px;
				overflow: hidden;
			}

			.nc-container .nc_scale div:first-child {
				padding-right: 28px;
			}

			.nc-container .nc_scale div:nth-last-child(2) {
				padding: 0;
			}

			.nc-container .nc_scale span {
				min-width: 40px;
				border-color: #e5e5e5;
				border-radius: 17px;
			}

			.nc-container .nc_scale span.btn_ok {
				font-size: 28px;
				color: #fff;
				background-color: #76c61d;
				border-color: #76c61d;
			}

			.nc-container .nc-align-center.scale_text2 {
				text-indent: -10px;
			}
		</style>
	</head>
	<script src="https://g.alicdn.com/AWSC/AWSC/awsc.js"></script>
	<script src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.1.js"></script>
	<script>
		document.addEventListener('plusready', function() {
			var wv = plus.webview.currentWebview()
			// 阿里云滑动验证
			window.AWSC.use('nc', function(state, module) {
				window.nc = module.init({
					appkey: wv.appkey,
					scene: wv.scene,
					renderTo: 'ncContainer',
					success: function(data) {
						uni.postMessage({
							data
						})
						wv.hide()
						window.nc.reset()
					}
				})
			})
		})
	</script>
	<body>
		<div class="verify">
			<div class="verify-inner">
				<div class="title">滑动验证</div>
				<div class="body">
					<div class="text">通过验证自动发送验证码</div>
					<div class="nc-container" id="ncContainer"></div>
				</div>
			</div>
		</div>
	</body>
</html>
