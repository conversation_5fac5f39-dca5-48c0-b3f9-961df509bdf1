<template>
  <view class="container">
    <view
      v-for="(item, index) in logisticsList"
      :key="item.id"
      class="cell"
      @click="goDetail(item)"
    >
      <text class="title">包裹{{ index + 1 }}</text>

      <view class="value">
        <text>{{ item.deliveryTrackNumber }}</text>
        <text>{{ item.deliveryCompanyName }}</text>
        <text class="iconfont icon-arrow-right" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      logisticsList: [],
      phoneNumber: '',
    }
  },
  onLoad(options) {
    this.logisticsList = JSON.parse(decodeURIComponent(options.logisticsList))
    this.phoneNumber = options.phoneNumber
  },
  methods: {
    goDetail(detail) {
      uni.navigateTo({
        url: `/jingBrand/take/package?detail=${JSON.stringify(detail)}&phoneNumber=${
          this.phoneNumber
        }`,
      })
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - 48rpx);
  padding: 24rpx 0;
  background-color: #f5f5f5;
}
.cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24rpx;
  margin-top: 0;
  padding: 32rpx 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.title {
  font-size: 32rpx;
}

.value {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.icon-arrow-right {
  font-size: 32rpx;
  color: #000;
}
</style>
