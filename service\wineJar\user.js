// Description: 商品等相关
import request from '@/config/request'
// 赠送和接收的酒票查询
export const queryUserWineTicketPage = (data) => request({ url: 'fengtanWine/miniTicket/queryUserWineTicketPage', data, method: 'post' })
// 我的藏酒
export const queryUserDepositPage = (data) => request({ url: 'fengtanWine/miniWineCellar/queryUserDepositPage', data, method: 'post' })
// 取酒记录
export const queryUserTakeWineRecordPage = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/queryUserTakeWineRecordPage', data, method: 'post' })
// 转让记录
export const depositTransPageList = (data) => request({ url: 'fengtanWine/depositTrans/mini/pageList', data, method: 'post' })
// 托管费账单年月列表信息-分页查询
export const queryUserStorageDetailPage = (data) => request({ url: 'fengtanWine/storageFeeBill/mini/queryUserStorageDetailPage', data, method: 'post' })
// 转让记录
export const queryStorageFeeBillPage = (data) => request({ url: 'fengtanWine/storageFeeBill/mini/queryStorageFeeBillPage', data, method: 'post' })
// 我的证书
export const certificatePageList = (data) => request({ url: 'fengtanWine/miniCertificate/pageList', data, method: 'post' })