/**
 * @description: 判断是否为tabBar选中的显示页面，如果是则加载页面
 * @param {String} path 页面路径
 * @return {Boolean} 是否为tabBar选中页面
 **/
import store from '@/store'

const tabBarCheck = (path) => {
  const pages = getCurrentPages()
  // 非底部菜单页面直接加载
  if (pages[pages.length - 1].route !== 'pages/index/index') {
    return true
  }
  // 判断tabBarSelected是否选中当前页面
  if (store.state.tabBarSelected.includes(path)) {
    return true
  }
  return false
}

export default tabBarCheck
