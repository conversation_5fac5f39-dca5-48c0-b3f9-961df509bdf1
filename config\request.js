import qs from 'qs'
import store from '@/store'
import { SET_ISLOGIN, SET_USERINFO, SET_LOGIN_PAGE } from '@/store/mutation-types'
import { getIdentity } from '@/common/utils'
import { APP_TYPES, baseUrl } from '@/config/system'

/*
参数：
	showLoading: 显示loading,
	loadingText: loading文本，
	intercept // 是否拦截业务异常并提示错误
	contentType：post提交类型，默认json，
	uuid：本地存储的唯一标识，true传入请求头
*/

export default ({
  url = '',
  data = {},
  method = 'get',
  showLoading = true,
  loadingText = '',
  intercept = true,
  contentType = 'application/json',
  uuid = false,
}) => {
  if (showLoading) {
    uni.showLoading({
      title: '加载中...',
      mask: true,
    })
  }

  const token = uni.getStorageSync('token')
  const header = {
    'content-type': contentType,
  }

  // 根据url判断所属系统模块
  const module = url.split('/')[0]
  const system = APP_TYPES[module]
  if (!system) {
    wx.hideLoading()
    return Promise.reject(new Error('请求地址不存在：' + url))
  }
  let domain = baseUrl['BASE_URL_' + system.toUpperCase()]
  header['moduleCode'] = system

  header.Authorization = token
  if (store.state.isLogin) {
    header.Authorization = token
  }
  if (data.constructor === Object) {
    const newData = {}
    for (let key in data) {
      if (data[key] !== undefined && data[key] !== 'undefined') {
        newData[key] = data[key]
      }
    }
    data = newData
  }
  if (uuid) {
    header.visitor = getIdentity()
  }
  return new Promise((resolve, reject) => {
    const options = {
      url: domain + url,
      method,
      data,
      header,
      success: res => {
        const data = res.data
        showLoading && uni.hideLoading()
        if (data.code === '401' || data.code === '402') {
          uni.setStorageSync('token', '')
          uni.setStorageSync('refreshToken', '')
          store.commit(SET_ISLOGIN, false)
          store.commit(SET_USERINFO, {})
          if (url === 'wine/user/mini/authLogin') {
            return
          }
          reject(res)

          if (store.state.isLoginPage) return

          let loginUrl = '/pages/login/login'
          store.commit(SET_LOGIN_PAGE, true)
          return uni.navigateTo({
            url: loginUrl,
          })
        }

        if (data.code === '403') {
          uni.showToast({
            title: '当前用户无权访问此页面',
            icon: 'none',
          })
          return reject(res.data)
        }
        if (data.code !== '200' && intercept) {
          uni.showToast({
            icon: 'none',
            title: data.msg || '系统错误，请联系管理员',
          })
          return reject(data)
        }
        resolve(data)
      },
      fail: error => {
        showLoading && uni.hideLoading()
        reject(error)
        if (intercept) {
          uni.showToast({
            icon: 'none',
            title: '请求失败，请检查网络',
          })
        }
      },
    }

    if (method === 'post' && contentType === 'application/x-www-form-urlencoded') {
      options.data = qs.stringify(data)
    }
    uni.request(options)
  })
}
