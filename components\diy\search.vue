<template>
  <view class="cap-search-box" :style="{ backgroundColor: dataset.bgColor, padding: dataset.verticalMargin * 2 + 'rpx 24rpx' }">
    <view class="cap-search" @click="navigateTo" :class="{ center: dataset.textValue !== 1 }" :style="{ height: dataset.inputValue * 2 + 'rpx', backgroundColor: dataset.frameColor, borderRadius: dataset.radioValue === 1 ? '8rpx' : '40rpx', color: dataset.textColor }">
      <text class="iconfont icon-search"></text>
      <text class="overflow-ellipsis">{{ dataset.keyword || '搜索商品' }}</text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    dataset: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    navigateTo() {
      uni.navigateTo({
        url: '/pointsMall/product/search'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cap-search-box {
  width: 100%;
  padding: 16rpx 24rpx;
  box-sizing: border-box;
  transition: top 0.3s linear;
  .cap-search {
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    &.center {
      justify-content: center;
    }
    .iconfont {
      margin-left: 20rpx;
      font-size: 40rpx;
    }
    .overflow-ellipsis {
      padding: 0 12rpx;
    }
  }
}
</style>
