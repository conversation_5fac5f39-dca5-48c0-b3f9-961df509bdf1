<template>
  <div class="content">
    <h1 class="title">{{ detail.title }}</h1>
    <p class="meta">{{ detail.createTime || '2029-29-923 00:00:00' }}</p>
    <div v-html="detail.content" class="article"></div>
  </div>
</template>
<script>
import request from '@/config/request'

export default {
  data() {
    return {
      id: null,
      detail: {
        title: '',
        content: '',
      },
    }
  },
  onLoad({ id }) {
    this.id = id
    request({
      url: 'jingBrand/information/getInformationDetail',
      data: {
        id,
      },
    }).then(res => {
      res.data.content = this.formatRichText(res.data.content)
      this.detail = res.data
    })
  },
  methods: {
    /**
     * 处理富文本里的图片宽度自适应
     */
    formatRichText(html) {
      return html.replace(
        /<img/gi,
        '<img style="max-width:100%;height:auto;display:block;margin-top:0;margin-bottom:0;"'
      )
    },
  },
}
</script>

<style scoped lang="scss">
.content {
  min-height: 100vh;
  padding: 24rpx;
  //background-color: #f5f5f5;
  box-sizing: border-box;
}

.title {
  line-height: 48rpx;
  font-size: 36rpx;
}

.meta {
  margin: 24rpx auto 40rpx;
  font-size: 24rpx;
  color: #999;
}
</style>

<!--<style>-->
<!--.article img {-->
<!--  max-width: 100% !important;-->
<!--}-->
<!--</style>-->
