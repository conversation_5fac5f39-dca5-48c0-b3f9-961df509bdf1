<template>
  <view v-if="dataset.html" class="rich-text" :style="{ padding: dataset.fullScreen ? 0: '24rpx', backgroundColor: dataset.bgColor }">
    <u-parse :content="dataset.html" :imageProp="{ lazyLoad: true, domain: obsUrl }" />
  </view>
</template>

<script>
import uParse from '../uParse/parse.vue'
import { mapState } from 'vuex'

export default {
  components: {
    uParse
  },
  props: {
    dataset: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
    }
  },
  computed: {
    ...mapState([
      'obsUrl'
    ])
  },
  watch: {

  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.rich-text {
  box-sizing: border-box;
}
</style>
