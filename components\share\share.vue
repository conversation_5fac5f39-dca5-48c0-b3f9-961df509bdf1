<template>
  <!-- 订单分享页 -->
  <view :class="{ 'share-container': true, 'share-show': show }" @click="handleClose">
    <image v-show="isShowGuide" class="guide" :src="'static/applet/guide.png' | formatUrl"></image>
    <view class="share-content" :class="{ 'share-brokerage': brokerage }" @click.stop v-show="!isShowGuide">
      <template v-if="brokerage">
        <view class="brokerage-title fw-b">分享后预计最高可赚取{{ distributionReName.brokerageName }}￥{{ brokerage }}</view>
        <view class="brokerage-summary">朋友通过你分享的页面成功购买后，你可获得对应的{{ distributionReName.brokerageName }}，{{ distributionReName.brokerageName }}可在“我的 - {{ distributionReName.center }}”里查看</view>
      </template>

      <view class="share-types">
        <!-- #ifndef H5 -->
        <button v-if="installedWechat" class="type-item" hover-class="" open-type="share" @click="handleWxShare">
          <text class="iconfont icon-weixin"></text>
          分享给朋友
        </button>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <button v-if="$isWechatH5" class="type-item" hover-class="" @click="isShowGuide = true">
          <text class="iconfont icon-weixin"></text>
          分享给朋友
        </button>
        <!-- #endif -->
        <button class="type-item" hover-class="" v-if="!noPoster" @click="handlePoster">
          <text class="iconfont icon-image"></text>
          生成海报
        </button>
      </view>
      <view class="join border" v-if="brokerage" @click="handleToDistribution">进入{{ distributionReName.center }}</view>
      <view class="btn border" @click="show = !show">取消</view>
    </view>

    <poster v-if="!noPoster" :cached="cached" ref="poster" :height="height" :canvasId="canvasId" />
  </view>
</template>

<script>
import Poster from '../poster'
import { calcTextLength, createQrcode } from '@/common/utils'
import { GenMaCode } from '@/service/pointsMall/raffle'
import { mapState, mapActions } from 'vuex'

export default {
  components: {
    Poster
  },
  props: {
    cached: {
      type: Boolean,
      default: true
    },
    display: Boolean,
    height: {
      type: Number,
      default: 1921
    },
    dataset: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'product'
    },
    noPoster: {
      type: Boolean,
      default: false
    },
    needCode: {
      type: Boolean,
      default: true
    },
    canvasId: {
      type: String,
      default: 'posterCanvasId'
    },
    brokerage: [String, Number]
  },
  data() {
    return {
      show: false,
      drawArr: null,
      isShowGuide: false,
      isDrew: false,
      installedWechat: true // 是否安装微信
    }
  },
  computed: {
    ...mapState(['userInfo', 'isLogin', 'theme', 'setting', 'obsUrl', 'distributionReName'])
  },
  watch: {
    display(value) {
      this.show = value
    },
    show(value) {
      this.$emit('update:display', value)
    }
  },
  methods: {
    ...mapActions(['getDistributionReName']),
    handleToDistribution() {
      uni.navigateTo({
        url: '../distribution/index'
      })
    },
    handleClose() {
      this.show = false
      this.isShowGuide = false
    },
    handleWxShare() {
      this.show = false
      // #ifdef APP-PLUS
      this.$wxShare(this.$parent.initShare())
      // #endif
    },
    handlePoster() {
      this.show = !this.show
      // 绘制数据默认使用内置的商品模板，自定义在父级中定义drawArr
      let parentDrawArr = this.$parent.drawArr
      // #ifdef H5
      parentDrawArr = this.$parent.$parent.drawArr
      // #endif
      // 父级传入绘制数据直接绘制
      if (parentDrawArr) {
        return this.$refs.poster.draw(parentDrawArr)
      }
      // 已经绘制过或者不需要二维码直接绘制
      if ((this.isDrew || !this.needCode) && this.cached) {
        this.initDrawData()
        return this.$refs.poster.draw(this.drawArr)
      }
      this.initQrcode()
    },
    initQrcode() {
      this.initDrawData()

      let toUrl = 'pages/product/detail?productId=' + (this.dataset ? this.dataset.productId : '')
      if (this.type === 'order') {
        toUrl = 'pages/order/share?orderId=' + this.dataset.orderId
      }

      // #ifndef H5
      uni.showLoading({
        title: '生成中'
      })
      GenMaCode({
        url: toUrl
      }).then((res) => {
        this.drawArr.push({
          type: 'image',
          url: this.$options.filters.formatUrl(res.data, 200),
          dx: 661,
          dy: 1365,
          dWidth: 288,
          dHeight: 288
        })
        this.$refs.poster.draw(this.drawArr)
      })
      // #endif
      // #ifdef H5
      const qrcode = createQrcode({
        path: toUrl,
        size: 288,
        x: 661,
        y: 1365
      })
      this.drawArr.push(qrcode)
      this.$refs.poster.draw(this.drawArr)
      // #endif
    },
    initDrawData() {
      this.isDrew = true
      let priceWidth = 0
      let markingPriceWidth = -12
      let tagWidth = 0
      priceWidth = calcTextLength(this.dataset.price, 69)
      if (this.dataset.markingPrice) {
        markingPriceWidth = calcTextLength('￥' + this.dataset.markingPrice, 37)
      }
      tagWidth = calcTextLength(this.dataset.shareTag, 30)
      this.drawArr = [
        {
          type: 'image',
          url: this.obsUrl + 'static/applet/poster-img.jpg',
          dx: 0,
          dy: 0,
          dWidth: 1080,
          dHeight: 1921
        },
        {
          type: 'roundFillRect',
          dx: 50,
          dy: 202,
          width: 979,
          height: 1544,
          r: 35,
          backgroundColor: '#F8F8F8'
        },
        {
          type: 'image',
          url: this.$options.filters.formatUrl(this.isLogin ? this.userInfo.avatarUrl : this.setting.webLogo, 138),
          dx: 96,
          dy: 260,
          dWidth: 138,
          dHeight: 138,
          roundRectSet: {
            r: 138
          }
        },
        {
          type: 'text',
          text: this.isLogin ? this.userInfo.nickname : this.setting.webName,
          size: 43,
          color: '#212121',
          dx: 258,
          dy: 260,
          lineFeed: {
            lineHeight: 69
          }
        },
        {
          type: 'text',
          text: '给你推荐了一个好东西',
          size: 37,
          color: '#BDBDBD',
          lineFeed: {
            lineHeight: 53
          },
          dx: 258,
          dy: 329
        },
        {
          type: 'roundFillRect',
          dx: 96,
          dy: 444,
          width: 887,
          height: 1256,
          r: 35,
          backgroundColor: '#ffffff'
        },
        {
          type: 'image',
          url: this.$options.filters.formatUrl(this.dataset.thumbnail || this.dataset.productImg, 887),
          infoCallBack(imageInfo) {
            const ratio = imageInfo.width / imageInfo.height
            const pos = {
              dWidth: 887,
              dHeight: 887,
              dx: 96,
              dy: 444,
              roundRectSet: {
                r: 35,
                radius: [1, 1, 0, 0] //部分圆角 1、圆角 0、不圆角 -- 同css border-radius
              },
              alpha: 1
            }
            if (ratio > 1) {
              pos.dHeight = (imageInfo.height * 887) / imageInfo.width
              pos.dy += (887 - pos.dHeight) / 2
            }
            if (ratio < 1) {
              pos.dWidth = (imageInfo.width * 887) / imageInfo.height
              pos.dx += (887 - pos.dWidth) / 2
            }
            if (Math.abs(imageInfo.width - imageInfo.height) > 35) {
              pos.roundRectSet = null
            }
            return pos
          }
        },
        {
          type: 'text',
          text: this.dataset.price ? '￥' : '',
          size: 46,
          color: '#FF3D47',
          lineFeed: {
            lineHeight: 65
          },
          dx: 131,
          dy: 1383
        },
        {
          type: 'text',
          text: (this.dataset.price || '') + '',
          size: 69,
          color: '#FF3D47',
          fontWeight: 'bold',
          dx: 177,
          dy: 1368,
          lineFeed: {
            lineHeight: 82
          }
        },
        {
          type: 'text',
          text: this.dataset.integral ? (this.dataset.price ? '+ ' : '') + this.dataset.integral + this.setting.pointsName : '',
          size: 54,
          color: '#FF3D47',
          dx: !this.dataset.price ? 131 : priceWidth + 200,
          dy: 1376,
          lineFeed: {
            lineHeight: 76
          }
        },
        {
          type: 'text',
          text: this.dataset.markingPrice ? '￥' + this.dataset.markingPrice : '',
          size: 37,
          color: '#BDBDBD',
          lineThrough: {},
          dx: 131,
          dy: 1460,
          lineFeed: {
            lineHeight: 53
          }
        },
        {
          type: 'roundFillRect',
          backgroundColor: this.theme.primary.color6,
          r: 23,
          dx: markingPriceWidth + 160,
          dy: 1463,
          height: this.dataset.shareTag ? 46 : 0,
          width: this.dataset.shareTag ? tagWidth + 36 : 0
        },
        {
          type: 'text',
          text: this.dataset.shareTag,
          size: 30,
          color: '#fff',
          dx: 178 + markingPriceWidth,
          dy: 1460,
          lineFeed: {
            lineHeight: 53
          }
        },
        {
          type: 'text',
          text: this.dataset.productName,
          size: 40,
          color: '#000000',
          lineFeed: {
            maxWidth: 461,
            lineHeight: 58,
            lineNum: 2
          },
          dx: 131,
          dy: 1537
        },
        {
          type: 'image',
          url: this.obsUrl + 'static/common/logo-white.png',
          dx: 425,
          dy: 1790,
          dWidth: 230,
          dHeight: 81,
          alpha: 0.16
        }
      ]
    }
  },
  mounted() {
    if (this.brokerage) {
      this.getDistributionReName()
    }

    // #ifdef APP-PLUS
    if (!plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {
      this.installedWechat = false
    }
    // #endif
  }
}
</script>

<style lang="scss" scoped>
.guide {
  width: 640rpx;
  height: 190rpx;
  position: absolute;
  top: 0;
  right: 0;
}
.join {
  margin: 0 24rpx 48rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 44rpx;
  &::after {
    border-radius: 88rpx;
  }
}

.brokerage-title {
  padding: 24rpx 24rpx 0;
  line-height: 46rpx;
  text-align: center;
  font-size: 32rpx;
}

.brokerage-summary {
  text-align: center;
  line-height: 40rpx;
  color: #424242;
  padding: 24rpx 24rpx 64rpx;
}

.share-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 200;
  transform: translateY(100%);
}

.share-content {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 80rpx 0 24rpx;
  border-radius: 16rpx 16rpx 0 0;
  transform: translateY(100%);
  transition: all 0.3s;
  .share-types {
    display: flex;
    padding: 0 24rpx 40rpx;
  }

  .type-item {
    flex: 1;
    padding: 0 24rpx;
    max-width: 50%;
    overflow: hidden;
    box-sizing: border-box;
    line-height: 84rpx;
    text-align: center;
    border-radius: 0;
    background-color: #fff;
    font-size: $size;
    &:after {
      display: none;
    }
    .iconfont {
      display: flex;
      width: 100%;
      height: 176rpx;
      justify-content: center;
      align-items: center;
      font-size: 60rpx;
      color: #339dff;
      border-radius: 8rpx;
      background-color: $colorbackground;
    }

    &:first-child {
      .iconfont {
        color: #4cbf00;
      }
    }
  }
}

.btn {
  margin: 0 24rpx;
  padding: 24rpx 0;
  line-height: 40rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  text-align: center;
  &::after {
    border-radius: 32rpx;
  }
}

.share-show {
  transform: translateY(0);
  .share-content {
    transform: translateY(0);
  }
}

.share-brokerage {
  padding-top: 0;
  .share-content {
    padding-bottom: 24rpx;
  }
}
</style>
