<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '藏酒详情' }"
      @init="statusBarHeight = $event"
    />
    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <swiper v-if="detail.imgList && detail.imgList.length" style="height: 654rpx">
          <swiper-item v-for="(img, index) in detail.imgList" :key="index" circular indicator-dots>
            <image :src="img | formatUrl" class="cover" mode="aspectFill" />
          </swiper-item>
        </swiper>

        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/7oBHWN6QoTtGAbH8OwHqw.png"
              class="icon"
            />
            藏酒名称:
          </span>
          <span class="value name">{{ detail.name || '--' }}</span>
        </p>

        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/Nxlo2az8Tsx-fCzoUU233.png"
              class="icon"
            />
            藏酒容量：
          </span>
          <span class="value">
            <span class="text-primary">{{ detail.sumOverCapacity | ml2L }}L</span>
            <span> / {{ detail.sumTotalCapacity | ml2L }}L</span>
          </span>
        </p>

        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/IARQw7BGXsXAVLgyt3ZLI.png"
              class="icon"
            />
            藏酒数量：
          </span>
          <span class="value">{{ detail.assetsAltarNum || '--' }}坛</span>
        </p>

        <!--        <p class="item">-->
        <!--          <span class="label">-->
        <!--            <img-->
        <!--              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/obJ2YKOaGN9V8rv1uNCLM.png"-->
        <!--              class="icon"-->
        <!--            />-->
        <!--            藏酒容量：-->
        <!--          </span>-->
        <!--          <span class="value">-->
        <!--            <span class="text-primary">{{ detail.sumOverCapacity | ml2L }}L</span>-->
        <!--            /-->
        <!--            {{ detail.sumTotalCapacity | ml2L }}L-->
        <!--          </span>-->
        <!--        </p>-->

        <!--        <p class="item">-->
        <!--          <span class="label">-->
        <!--            <img-->
        <!--              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/np_SxEakzaqrMzLleYb7e.png"-->
        <!--              class="icon"-->
        <!--            />-->
        <!--            封藏日期：-->
        <!--          </span>-->
        <!--          <span class="value">{{ detail.sealTime || '&#45;&#45;' }}</span>-->
        <!--        </p>-->

        <template v-if="!isSpecialGoodId">
          <p class="divider" />
          <p v-for="(item, index) in detail.depositCerList" :key="index" class="item">
            <span class="label">
              <img
                src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/XR7VeYgqDjZ86bTLuNhBS.png"
                class="icon"
              />
              贮藏编号：
            </span>
            <span class="value" @click="goDepositDetail(item)">
              {{ item.depositNo || '--' }}
              <span class="tag">详情</span>
            </span>
          </p>
        </template>

        <!--        <p class="item">-->
        <!--          <span class="label">-->
        <!--            <img src="" class="icon" />-->
        <!--            贮藏编号：-->
        <!--          </span>-->
        <!--          <span class="value"></span>-->
        <!--        </p>-->
      </div>

      <div class="actions">
        <span class="btn" :style="{ opacity: detail.monitorUrl ? '100' : '0' }" @click="goMonitor">
          看监控
        </span>
        <span class="btn" @click="openSentToPopup">赠酒</span>
        <span class="btn btn-primary" @click="openTakePopup">取酒</span>
      </div>
    </div>

    <send-to ref="sendTo" :goods-id="goodsId" :customer-id="customerId" />

    <take ref="take" :goods-id="goodsId" :customer-id="customerId" />

    <!--    :goods-name="detail.name"-->
    <!--    :goods-cover="cover"-->
    <!--    :available-capacity="detail.sumOverCapacity"-->
    <!--    :subpackage-info-list="detail.subpackageInfoList"-->
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import { getStockDetail } from '@/service/jingBrand/stock'
import SendTo from '@/pages/index/jingBrand/components/SendTo.vue'
import Take from '@/pages/index/jingBrand/components/Take.vue'
import { mapState } from 'vuex'

export default {
  name: 'detail',
  components: { CustomNav, SendTo, Take },
  data() {
    return {
      statusBarHeight: 168,
      detail: {},
      goodsId: null,
      customerId: null,
    }
  },
  computed: {
    ...mapState(['specialGoodsIds']),
    unit() {
      return this.detail.subpackageInfoList?.at(0)?.unit
    },
    /**
     * 特殊的藏酒id
     */
    isSpecialGoodId() {
      return this.specialGoodsIds.includes(parseInt(this.goodsId))
    },
  },
  async onLoad({ id, customerId }) {
    this.goodsId = id
    this.customerId = customerId
    const { code, data } = await getStockDetail({ goodsId: id, targetCustomId: customerId })
    if (code === '200') {
      data.subpackageInfoList.forEach(subpackage => {
        subpackage.number = 0
        subpackage.max = 0
      })
      this.detail = data
    }
  },
  methods: {
    goDepositDetail(item) {
      uni.setStorageSync('depositDetail', {
        ...item,
        certificate: this.detail.certificate,
      })
      uni.navigateTo({
        url: '/jingBrand/stock/depositDetail',
      })
    },
    goMonitor() {
      if (!this.detail.monitorUrl) return
      const urls = [this.detail.monitorUrl, this.detail.monitorUrlTwo]
      uni.navigateTo({
        url: `/jingBrand/stock/monitor?urls=${encodeURIComponent(JSON.stringify(urls))}`,
      })
    },
    openSentToPopup() {
      this.$refs.sendTo.open()
    },
    openTakePopup() {
      this.$refs.take.open()
    },
  },
}
</script>

<style scoped lang="scss">
.text-primary {
  color: #0b7d83;
}

.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  background-color: #f5f4f3;
}
.content {
  margin: 24rpx;
  padding: 32rpx 24rpx 150rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.cover {
  width: 100%;
  height: 654rpx;
  border-radius: 4rpx;
}

.item {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  margin: 40rpx auto;

  .label {
    display: flex;
    align-items: center;
    white-space: nowrap;
    font-size: 28rpx;
    color: #262626;
  }

  .icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }

  .value {
    font-size: 28rpx;
    color: #666;
  }

  .name {
    font-weight: 600;
    font-size: 32rpx;
    color: #000;
  }

  .tag {
    margin-left: 16rpx;
    padding: 2rpx 8rpx;
    font-size: 20rpx;
    color: #0b7d83;
    line-height: 28rpx;
    background: #f2f8f8;
    border-radius: 4rpx;
  }
}

.actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-around;
  gap: 16rpx;
  padding: 16rpx 24rpx env(safe-area-inset-bottom);
  background-color: #fff;
  border-top: 1rpx solid #eee;

  .btn {
    padding: 24rpx 70rpx;
    line-height: 1;
    border-radius: 44rpx;
    border: 1rpx solid #f0f0f0;
    background-color: #fff;
  }

  .btn-primary {
    color: #fff;
    background-color: #0b7d83;
    border-color: #0b7d83;
  }
}
</style>
