### hxe-image-video适用于uni-app项目的图片展示组件
### 本组件目前兼容微信小程序、H5
### 简单好用的图片显示组件，基于uni-image的二次开发，将图片和视频的展示二合一，支持自动区分是图片还是视频，传入对应的 Url 即可。
# --- 扫码、关注我们 ---

## 扫码关注公众号，有问题可随时沟通！ 

![](https://hxeproj.oss-cn-shanghai.aliyuncs.com/image/common/gzh_code.jpeg)

### 使用方式
``` html
<hxe-image-video v-for="(item, index) in imgList" :key="index" :src="item" @click="imgPreview(index, imgList)"></hxe-image-video>
```
``` javascript
export default {
	data() {
		return {
			imgList: ['https://cdn.pixabay.com/photo/2020/05/19/13/32/cartoon-5190837_1280.jpg', 'https://cdn.pixabay.com/photo/2022/03/31/14/53/camp-7103189_1280.png']
		};
	},
	methods: {
		/**
		 * 图片预览
		 */
		imgPreview(index, list) {
			const urlList = []
			list.forEach(item => {
				if (this.checkFile(item, 'image')) {
					urlList.push(item)
				}
			})
			uni.previewImage({
				current: index,
				loop:true,
				urls: urlList
			})
		},
		/**
		 * 判断文件类型，判断传入的路径是图片还是视频
		 * @param {*} fileValue 文件名称
		 * @param {*} type 文件类型 video Or image
		 */
		checkFile(fileValue, type) {
			var index = fileValue.indexOf("."); //（考虑严谨用lastIndexOf(".")得到）得到"."在第几位
			var fileValueSuffix = fileValue.substring(index); //截断"."之前的，得到后缀
			if(type == 'video') {
				if (!/(.*)\.(mp4|rmvb|avi|ts)$/.test(fileValueSuffix)) { //根据后缀，判断是否符合视频格式
					return false;
				}
			}
			if(type == 'image') {
				if (!/(.*)\.(gif|jpg|jpeg|png|GIF|JPG|PNG|image)$/.test(fileValueSuffix)) { //根据后缀，判断是否符合图片格式
					return false;
				}
			}
			return true;
		},
	}
}
```

### 属性说明
| 名称                         | 类型           | 默认值                  | 描述             |
| ----------------------------|--------------- | ---------------------- | ---------------|
| src                        | String          |                        | 图片地址
| mode                       | String          | 请输入                  | 裁剪模式，见官网说明
| width                      | String / Number | 100%                   | 宽度，单位任意，如果为数值，则为rpx单位（默认100%）
| height                     | String / Number | auto                   | 高度，单位任意，如果为数值，则为rpx单位（默认 auto）
| shape                      | String          | square                 | 图片形状，circle-圆形，square-方形（默认square）
| lazy-load                  | Boolean         | true                   | 是否懒加载，仅微信小程序、App、百度小程序、字节跳动小程序有效（默认 true）
| show-menu-by-longpress     | Boolean         | false                  | 是否开启长按图片显示识别小程序码菜单，仅微信小程序有效（默认 false）
| loading-icon               | String          | photo                  | 加载中的图标，或者小图片（默认 photo）
| error-icon                 | String          | error-circle           | 加载失败的图标，或者小图片（默认 error-circle）
| show-loading               | Boolean         | true                   | 是否显示加载中的图标或者自定义的slot（默认 true）
| show-error                 | Boolean         | true                   | 是否显示加载错误的图标或者自定义的slot（默认 true）
| fade                       | Boolean         | true                   | 是否需要淡入效果（默认 true）
| webp                       | Boolean         | false                  | 只支持网络资源，只对微信小程序有效（默认 false）
| duration                   | tring / Number  | 500                    | 搭配fade参数的过渡时间，单位ms（默认 500）
| @click                     | Function        |                        | 点击图片时触发,视频无效
| @error                     | Function        |                        | 图片加载失败时触发，视频无效
| @load                      | Function        |                        | 图片加载成功时触发，视频无效
 

