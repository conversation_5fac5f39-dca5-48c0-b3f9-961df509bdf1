<template>
  <view class="container-root" :class="{ transparent: !isInit }">
    <view class="header-wrap">
      <view class="header" :style="{ paddingTop: statusBarHeight + 'rpx' }">
        <custom-nav
          :data="{ barBgColor: 'transparent', barType: 1 }"
          @init="statusBarHeight = $event"
        />
        <view class="user-info">
          <image
            class="aside"
            @click="handleShowUserInfo"
            :src="
              (userInfo.avatarUrl ? userInfo.avatarUrl : 'static/applet/default-avatar.png')
                | formatUrl
            "
            mode="aspectFill"
          />
          <view class="main">
            <view class="action-btn" @click="$checkLogin('')" v-if="!isLogin">授权登录</view>
            <template v-else>
              <view class="action-btn" v-if="!userInfo.avatarUrl" @click="handleShowUserInfo">
                点击显示微信头像
              </view>
              <template v-else>
                <view class="b">{{ userInfo.nickName || '微信用户' }}</view>
                <view class="phone-row">
                  {{ userInfo.mobile }}
                </view>
              </template>
            </template>
          </view>
          <view class="setting" @click="goSetting">
            <image
              mode="widthFix"
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/1YFDduITA_b4EybaCP_Rp.png"
            />
          </view>
        </view>
      </view>
      <image
        class="img-bg"
        src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/mpX8fx8E1rlpHBR-YiseO.png?x-image-process=image/resize,m_lfit,w_170"
      />
    </view>
    <div style="margin: 0 24rpx">
      <div class="first-item">
        <div class="card" @click="goMyStock">
          <img
            src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/NN1U9eUFfSjvmiUnd3amL.png?x-image-process=image/resize,m_lfit,w_128"
          />
          <span>我的藏酒</span>
        </div>
        <div class="card" @click="handleOpen('/jingBrand/send/list')">
          <img
            src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/WC5x7Z4uF6YfOtgL87bvJ.png?x-image-process=image/resize,m_lfit,w_128"
          />
          <span>我的转赠</span>
        </div>
      </div>

      <div class="card">
        <p class="card__header">
          <span>我的取酒</span>
          <span class="more" @click="handleOpen('/jingBrand/take/list')">
            查看更多 <i class="iconfont icon-arrow-right" />
          </span>
        </p>

        <div class="second-item">
          <div
            v-for="item in orderStatuses"
            :key="item.value"
            class="flex flex-column flex-align-center"
            @click="handleOpen(`/jingBrand/take/list?orderStatus=${item.value}`)"
          >
            <img :src="item.icon" class="icon" />
            <p class="text">{{ item.label }}</p>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card__header">我的服务</div>

        <div
          v-if="promoter.id"
          class="flex flex-justify-between flex-align-center"
          style="height: 56rpx; margin: 24rpx auto"
          @click="$refs.promoterPopup.open()"
        >
          <div class="flex flex-align-center">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/RFkB6tno6XgDsFdZvH2QF.png?x-image-process=image/resize,m_lfit,w_80"
              style="width: 40rpx; height: 40rpx; margin-right: 16rpx"
            />
            封藏管家
          </div>

          <i class="iconfont icon-arrow-right" style="font-size: 24rpx" />
        </div>

        <div
          class="flex flex-justify-between flex-align-center"
          style="height: 56rpx; margin: 24rpx auto"
          @click="handleOpen('/pages/address/list')"
        >
          <div class="flex flex-align-center">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/sZkFYaoBh9hBj_bC9OKRk.png?x-image-process=image/resize,m_lfit,w_80"
              style="width: 40rpx; height: 40rpx; margin-right: 16rpx"
            />
            地址管理
          </div>

          <i class="iconfont icon-arrow-right" style="font-size: 24rpx" />
        </div>

        <!-- <div
          class="flex flex-justify-between flex-align-center"
          style="height: 56rpx; margin: 24rpx auto;"
          @click="handleOpen('/pages/user/agreement?type=2')"
        >
          <div class="flex flex-align-center">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/aoyvvGwj0d5bfij15yuaR.png?x-image-process=image/resize,m_lfit,w_80"
              style="width: 40rpx; height: 40rpx; margin-right: 16rpx"
            />
            隐私协议
          </div>

          <i class="iconfont icon-arrow-right" style="font-size: 24rpx" />
        </div> -->
      </div>
    </div>
    <promoter-popup ref="promoterPopup" />
    <user-info-popup ref="user" />
  </view>
</template>

<script>
import { mapMutations, mapState } from 'vuex'
// import { GetHomeData } from '@/service/scanMarketing/user'
import CustomNav from '@/components/customNav/customNav'
import UserInfoPopup from '@/components/userInfo/userInfo'
import PromoterPopup from './components/Promoter.vue'
import request from '@/config/request'

export default {
  components: {
    CustomNav,
    UserInfoPopup,
    PromoterPopup,
  },
  data() {
    return {
      isInit: false,
      data: {},
      statusBarHeight: 128,
      orderStatuses: [
        {
          label: '待确认',
          value: 'IN_CONFIRMED',
          icon: 'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/NYEhoEz6OgpxrwZjCBSG8.png?x-image-process=image/resize,m_lfit,w_56',
        },
        {
          label: '生产中',
          value: 'IN_PRODUCTION',
          icon: 'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/FhX8_OyJtVvrrawaE6aqI.png?x-image-process=image/resize,m_lfit,w_56',
        },
        {
          label: '待收货',
          value: 'UN_RECEIVED',
          icon: 'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/jKhSZGQj1Uy7fZFat4mtH.png?x-image-process=image/resize,m_lfit,w_56',
        },
        {
          label: '已完成',
          value: 'FINISHED',
          icon: 'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/NQ0x_pbY3gpVKFw1DMbZY.png?x-image-process=image/resize,m_lfit,w_56',
        },
        {
          label: '已取消',
          value: 'CLOSED',
          icon: 'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/_8rNPCck6tqWrwzr2Ql0-.png?x-image-process=image/resize,m_lfit,w_56',
        },
      ],
      promoter: {},
    }
  },
  computed: {
    ...mapState(['isLogin', 'userInfo', 'setting', 'theme']),
  },
  methods: {
    ...mapMutations(['SET_TAB_BAR_SELECTED', 'SET_USERINFO']),
    goSetting() {
      this.$navigateTo({
        path: '/pages/user/setting',
      })
    },
    handleShowUserInfo() {
      this.$refs.user.handleOpen()
    },
    handleOpen(url) {
      uni.navigateTo({
        url,
      })
    },
    goMyStock() {
      this.SET_TAB_BAR_SELECTED('/pages/index/jingBrand/stock')
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
    async initPage() {
      // GetHomeData().then(res => {
      //   this.data = res.data
      //   this.isInit = true
      //   uni.stopPullDownRefresh()
      // })
      this.isInit = true
      uni.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: '#000',
      })

      if (!this.userInfo.avatarUrl) {
        request({
          url: 'wine/user/mini/userInfo',
          method: 'GET',
        }).then(({ data }) => {
          if (data.avatarUrl) {
            this.SET_USERINFO(data)
            uni.setStorageSync('userInfo', data)
          }
        })
      }

      this.promoter = await this.$refs.promoterPopup.initMyPromoter()
    },
    refresh() {
      this.initPage()
    },
  },
}
</script>

<style lang="scss">
page {
  background-color: #f5f4f3;
}
</style>
<style lang="scss" scoped>
.container-root {
  padding-bottom: 32rpx;
}

.card {
  margin: 24rpx 0;
  padding: 24rpx;
  color: #262626;
  background-color: #fff;
  border-radius: 16rpx;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 24rpx;
    border-bottom: 1px solid #eee;
  }
}

.more {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;

  .iconfont {
    font-size: 24rpx;
    color: #666;
  }
}

.first-item {
  display: flex;
  gap: 16rpx;

  .card {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 32rpx 0;
    font-size: 32rpx;
  }

  img {
    width: 64rpx;
    height: 64rpx;
    margin-right: 8rpx;
  }
}

.second-item {
  padding: 24rpx 24rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .icon {
    width: 56rpx;
    height: 56rpx;
  }

  .text {
    margin-top: 8rpx;
    color: #999;
    font-size: 24rpx;
  }
}

.header-wrap {
  position: relative;
  z-index: 3;

  .img-bg {
    width: 100%;
    height: 380rpx;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
  }
}

.header {
  position: relative;
  padding-top: 128rpx;
  height: 380rpx;
  box-sizing: border-box;
  overflow: hidden;
  z-index: 4;

  .user-info {
    display: flex;
    position: relative;
    justify-content: space-between;
    align-items: center;
    padding: 0 24rpx 0;
    margin-top: 24rpx;

    .action-btn {
      width: 260rpx;
      height: 56rpx;
      text-align: center;
      line-height: 56rpx;
      background: #0b7d83;
      color: #fff;
      border-radius: 28rpx;
    }

    .aside {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      border: 2rpx solid rgba(255, 255, 255, 0.2);
      margin-right: 16rpx;
    }

    .setting {
      width: 48rpx;
      height: 48rpx;

      image {
        width: 100%;
      }
    }

    .main {
      flex: 1;
      overflow: hidden;

      .b {
        font-size: 36rpx;
        line-height: 56rpx;
        font-weight: 600;
        color: #000000;
      }

      .phone-row {
        opacity: 0.8;
        font-size: 28rpx;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        color: #262625;
        margin-top: 8rpx;
      }
    }
  }
}
</style>
