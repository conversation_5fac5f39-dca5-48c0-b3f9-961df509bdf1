import request from '@/config/request'

// 获取提交订单信息
export const GetOrderConfirm = (data) => request({ url: 'pointsMall/pointsOrder/mini/confirm', data, method: 'post' })
// 发起支付
export const PostOrderPay = (data) => request({ url: 'pointsMall/pointsOrder/mini/pay', data, method: 'post' })
// 订单详情
export const GetOrderDetail = (data) => request({ url: 'pointsMall/pointsOrder/mini/detail', data })
// 提交订单
export const SubmitOrder = (data) => request({ url: 'pointsMall/pointsOrder/mini/submit', data, method: 'post' })

// 获取订单列表
export const GetPageList = (data) => request({ url: 'pointsMall/pointsOrder/mini/pageList', data, method: 'post' })
// 取消订单
export const CloseOrder = (data) => request({ url: 'pointsMall/pointsOrder/mini/close', data, method: 'post' })
// 确认收货
export const ConfirmReceipt = (data) => request({ url: 'pointsMall/pointsOrder/mini/confirmReceipt', data, method: 'post' })
// 获取晒单详情
export const GetShareDetail = (data) => request({ url: 'pointsMall/pointsOrder/mini/getShareDetail', data })
// 查看物流信息
export const GetLogistics = (orderId) => request({ url: 'pointsMall/pointsOrder/mini/getLogistics', data: {orderId} })
// 订单申请售后
export const ApplyRefund = (data) => request({ url: 'pointsMall/pointsOrderRefund/mini/applyRefund', data, method: 'post' })
// 售后列表
export const GetRefundList = (data) => request({ url: 'pointsMall/pointsOrderRefund/mini/pageList', data, method: 'post' })
// 售后详情
export const GetRefundDetail = (data) => request({ url: 'pointsMall/pointsOrderRefund/mini/detail', data })
// 撤销售后
export const CancelRefund = (refundId) => request({ url: `pointsMall/pointsOrderRefund/mini/cancelRefund?refundId=${refundId}`, method: 'post' })
// 填写售后物流信息
export const FillLogistics = (data) => request({ url: 'pointsMall/pointsOrderRefund/mini/fillLogistics', data, method: 'post' })
// 获取售后日志列表
export const GetListRefundLog = (refundId) => request({ url: `pointsMall/pointsOrderRefund/mini/listRefundLog?refundId=${refundId}` })
