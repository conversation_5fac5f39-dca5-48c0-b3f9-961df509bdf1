import store from '@/store'
import NewGuid from '@/assets/js/guid'
import { encode, decode } from 'js-base64'
/**
 * 重写js加减乘除方法
 */
// 加
export const numAdd = (a = 0, b = 0) => {
  let c
  let d
  try {
    c = a.toString().split('.')[1].length
  } catch (f) {
    c = 0
  }
  try {
    d = b.toString().split('.')[1].length
  } catch (f) {
    d = 0
  }
  const e = Math.pow(10, Math.max(c, d))
  return (numMultiply(a, e) + numMultiply(b, e)) / e
}

// 减
export const numSubtract = (a = 0, b = 0) => {
  let c
  let d
  try {
    c = a.toString().split('.')[1].length
  } catch (f) {
    c = 0
  }
  try {
    d = b.toString().split('.')[1].length
  } catch (f) {
    d = 0
  }
  const e = Math.pow(10, Math.max(c, d))
  return (numMultiply(a, e) - numMultiply(b, e)) / e
}

// 乘
export const numMultiply = (a = 0, b = 0) => {
  let c = 0
  const d = a.toString()
  const e = b.toString()
  try {
    c += d.split('.')[1].length
  } catch (f) {}
  try {
    c += e.split('.')[1].length
  } catch (f) {}
  return (Number(d.replace('.', '')) * Number(e.replace('.', ''))) / Math.pow(10, c) || 0
}

// 除
export const numDivide = (a = 0, b = 0) => {
  let e = 0
  let f = 0
  try {
    e = a.toString().split('.')[1].length
  } catch (g) {}
  try {
    f = b.toString().split('.')[1].length
  } catch (g) {}
  const c = Number(a.toString().replace('.', ''))
  const d = Number(b.toString().replace('.', ''))
  return numMultiply(c / d, Math.pow(10, f - e))
}

// 计算文本长度
export const calcTextLength = (text, size = 28) => {
  if (!text) return
  text = text + ''
  let l = 0
  for (let j = 0; j < text.length; j++) {
    let t = text.substr(j, 1)
    if (/[a-zA-Z]/.test(t)) {
      l += 0.7
    } else if (/[0-9]/.test(t)) {
      l += 0.55
    } else if (/\./.test(t)) {
      l += 0.27
    } else if (/-/.test(t)) {
      l += 0.325
    } else if (/[\u4e00-\u9fa5]/.test(t)) {
      //中文匹配
      l += 1
    } else if (/\(|\)/.test(t)) {
      l += 0.373
    } else if (/\s/.test(t)) {
      l += 0.25
    } else if (/%/.test(t)) {
      l += 0.8
    } else {
      l += 1
    }
  }
  return l * size
}

/**
 * 计算剩余时间
 */
export const getTimeRemaining = (totalseconds, format) => {
  let day = parseInt(totalseconds / 86400)
  let hour = parseInt((totalseconds % 86400) / 3600)
  let min = parseInt((totalseconds % 3600) / 60)
  let sec = parseInt((totalseconds % 3600) % 60)
  if (hour < 10) {
    hour = '0' + hour
  }
  if (min < 10) {
    min = '0' + min
  }
  if (sec < 10) {
    sec = '0' + sec
  }
  if (!format) {
    return {
      day,
      hour,
      min,
      sec,
    }
  }

  var result = ''
  if (day > 0) {
    result += day + '天'
  }
  result += hour + ':' + min + ':' + sec + ''
  return result
}

/**
 * 获取当前日期
 */
export const getCurrentDate = () => {
  const date = new Date()
  let year = date.getFullYear()
  let month = date.getMonth() + 1
  let day = date.getDate()
  if (month < 10) {
    month = '0' + month
  }
  if (day < 10) {
    day = '0' + day
  }
  return `${year}-${month}-${day}`
}

/**
 * 手机号脱敏处理
 */
export const phoneMasking = phone => {
  if (!phone) return
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 昵称、姓名脱敏处理
 */
export const nameMasking = name => {
  if (!name) return
  return name.replace(/(.{1}).*(.{1})/, '$1**$2')
}

/**
 * 查询是否本地登录
 */
export const checkLogin = callback => {
  if (!store.state.isLogin) {
    let url = '/pages/login/login'
    // #ifdef H5
    url += '?backUrl=' + encodeURIComponent(window.location.href.split('#')[1])
    // #endif
    return uni.navigateTo({
      url,
    })
  }
  callback && callback()
}

/**
 * 16进制颜色转换rgba
 */
export const hex2rgb = (hex, alpha) => {
  let rgb = [0, 0, 0]
  if (/#(..)(..)(..)/g.test(hex)) {
    rgb = [parseInt(RegExp.$1, 16), parseInt(RegExp.$2, 16), parseInt(RegExp.$3, 16)]
    rgb.push(alpha)
  }
  return 'rgba(' + rgb.join(',') + ')'
}

/**
 * 计算颜色
 */
export const getThemeColor = {
  hex2rgb: (hex, alpha) => {
    let rgb = [0, 0, 0]
    if (/#(..)(..)(..)/g.test(hex)) {
      rgb = [parseInt(RegExp.$1, 16), parseInt(RegExp.$2, 16), parseInt(RegExp.$3, 16)]
      rgb.push(alpha)
    }
    return 'rgba(' + rgb.join(',') + ')'
  },
  rgb2hsl: (r, g, b) => {
    let i = 0
    let l = 0
    let s = 0
    let h
    const rgb = [r / 255, g / 255, b / 255]
    let min = rgb[0]
    let max = rgb[0]
    let maxcolor = 0
    for (i = 0; i < rgb.length - 1; i++) {
      if (rgb[i + 1] <= min) {
        min = rgb[i + 1]
      }
      if (rgb[i + 1] >= max) {
        max = rgb[i + 1]
        maxcolor = i + 1
      }
    }
    if (maxcolor === 0) {
      h = (rgb[1] - rgb[2]) / (max - min)
    }
    if (maxcolor === 1) {
      h = 2 + (rgb[2] - rgb[0]) / (max - min)
    }
    if (maxcolor === 2) {
      h = 4 + (rgb[0] - rgb[1]) / (max - min)
    }
    if (isNaN(h)) {
      h = 0
    }
    h = h * 60
    if (h < 0) {
      h = h + 360
    }
    l = (min + max) / 2
    if (min === max) {
      s = 0
    } else {
      if (l < 0.5) {
        s = (max - min) / (max + min)
      } else {
        s = (max - min) / (2 - max - min)
      }
    }
    // eslint-disable-next-line no-self-assign
    s = s
    return {
      h,
      s,
      l,
    }
  },
  hueToRgb(t1, t2, hue) {
    if (hue < 0) hue += 6
    if (hue >= 6) hue -= 6
    if (hue < 1) return (t2 - t1) * hue + t1
    else if (hue < 3) return t2
    else if (hue < 4) return (t2 - t1) * (4 - hue) + t1
    else return t1
  },
  hsl2rgb: (h, s, l) => {
    let t1
    let t2
    let r
    let g
    let b

    h = h / 60
    if (l <= 0.5) {
      t2 = l * (s + 1)
    } else {
      t2 = l + s - l * s
    }
    t1 = l * 2 - t2
    r = getThemeColor.hueToRgb(t1, t2, h + 2) * 255
    g = getThemeColor.hueToRgb(t1, t2, h) * 255
    b = getThemeColor.hueToRgb(t1, t2, h - 2) * 255
    return {
      r,
      g,
      b,
    }
  },
  TxtColor: hsl => {
    var h = hsl['h']
    var s = hsl['s']
    var l = hsl['l']
    if (l - 0.6 > 0) {
      l = l - 0.6

      if (s + 0.5 > 1) {
        s = 1
      } else {
        s = s + 0.5
      }
    } else {
      l = 0
      s = 0.5
    }
    return getThemeColor.rgb2hex(getThemeColor.hsl2rgb(h, s, l))
  },
  rgb2hex: rgb => {
    var r = parseInt(rgb['r'])
    var g = parseInt(rgb['g'])
    var b = parseInt(rgb['b'])
    var hex = '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
    return hex
  },
  hexToRgb(hex) {
    const rgb = {}
    if (/#(..)(..)(..)/g.test(hex)) {
      rgb['r'] = parseInt(RegExp.$1, 16)
      rgb['g'] = parseInt(RegExp.$2, 16)
      rgb['b'] = parseInt(RegExp.$3, 16)
    }
    return rgb
  },
  get: rgb => {
    if (rgb.indexOf('#') != -1) {
      rgb = getThemeColor.hexToRgb(rgb)
    }
    let r = Math.floor(rgb['r'])
    let g = Math.floor(rgb['g'])
    let b = Math.floor(rgb['b'])

    let shallowColor = ''
    // 计算字体颜色
    var lightness = (Math.sqrt(0.299 * r * r + 0.587 * g * g + 0.114 * b * b) * 100) / 255
    if (lightness <= 70) {
      shallowColor = '#ffffff'
    } else {
      if (r === g && r === b) {
        shallowColor = '#212121'
      } else {
        shallowColor = getThemeColor.TxtColor(getThemeColor.rgb2hsl(r, g, b))
      }
    }
    return shallowColor
  },
}

// 获取唯一Guid
export const getIdentity = function() {
  let identity = uni.getStorageSync('identity')
  if (!identity) {
    identity = NewGuid()
    uni.setStorageSync('identity', identity)
  }
  return identity
}

export const getUrlQuery = name => {
  const vars = window.location.search.substring(1).split('&')
  for (let i = 0; i < vars.length; i++) {
    const params = vars[i].split('=')
    if (params[0].toLowerCase() === name.toLowerCase()) {
      return params[1]
    }
  }
  return ''
}
// 防抖函数
export function debounced(fn, wait = 100) {
  if (typeof fn !== 'function') {
    console.error({
      name: 'params is not a function',
      message: '输入参数不是一个函数！',
    })
    return () => {}
  }
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, wait)
  }
}

//节流函数
export function throttle(fn, wait = 1000) {
  if (typeof fn !== 'function') {
    console.error({
      name: 'params is not a function',
      message: '输入参数不是一个函数！',
    })
    return () => {}
  }
  let preTime = 0
  return function(...args) {
    const now = new Date()
    if (now - preTime > wait) {
      fn.apply(this, args)
      preTime = now
    }
  }
}

export function setOpacity(color, opacity) {
  const rgbaColor =
    'rgba(' +
    parseInt(color.slice(1, 3), 16) +
    ',' +
    parseInt(color.slice(3, 5), 16) +
    ',' +
    parseInt(color.slice(5, 7), 16) +
    ',' +
    opacity +
    ')'
  return rgbaColor
}

export function countDown(time) {
  console.log('传入的时间：', time)
  return new Promise(function(resolve, reject) {
    var countDownDate = new Date().getTime() + time // 设置倒计时的结束时间（当前时间 + 600000ms）
    var now = new Date().getTime() // 获取当前时间
    var distance = countDownDate - now // 计算剩余时间

    // 将剩余时间转换为时、分、秒
    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    var seconds = Math.floor((distance % (1000 * 60)) / 1000)

    // 在页面上显示倒计时
    console.log(hours + '小时 ' + minutes + '分钟 ' + seconds + '秒 ')

    // 倒计时结束，清除定时器
    if (distance < 0) {
      // clearInterval(x);
      console.log('倒计时结束')
      reject(null)
    } else {
      resolve({
        hours,
        minutes,
        seconds,
      })
    }
  })
}

export function toRise(val) {
  if (!val) {
    return '0L'
  }
  val = parseInt(val)
  if (val < 1000) {
    return `${val}ML`
  } else {
    return `${(val / 1000).toFixed(2)}L`
  }
}

export function clipboard(data) {
  uni.setClipboardData({
    data,
    success: function() {
      uni.showToast({
        title: '已复制到剪切板',
        icon: 'none',
      })
    },
  })
}

export function checkPhone(phone) {
  var reg = /^1[3-9]\d{9}$/
  return reg.test(phone)
}
