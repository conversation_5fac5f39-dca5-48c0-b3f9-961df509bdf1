import request from '@/config/request'

// 获取活动详情
export const RaffleDetail = (data) => request({ url: 'pointsMall/raffle/mini/detail', data })
// 抽奖
export const RaffleDraw = (data) => request({ url: 'pointsMall/raffle/mini/draw', data, method: 'post', showLoading: false })
// 中奖记录
export const RaffleWinner = (data) => request({ url: 'pointsMall/raffle/mini/raffleWinner', data, showLoading: false })
// 我的抽奖记录
export const UserRafflePrize = (data) => request({ url: 'pointsMall/raffle/mini/userRafflePrize', data })
// 生成二维码
export const GenMaCode = (data) => request({ url: 'pointsMall/raffle/mini/genMaCode', data, method: 'post', showLoading: false })
