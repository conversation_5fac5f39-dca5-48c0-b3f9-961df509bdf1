<template>
  <home
    ref="home"
    :type="setting.homePageModuleCode"
    path="/pages/index/basicSystem/home"
    @load="loadData"
  >
    <template v-if="setting.homePageModuleCode === 'points_mall'" slot-scope="{ data }">
      <view
        class="integral-top"
        v-if="data.top"
        :style="{ background: data.top.bgColor, color: data.top.color }"
      >
        <view class="p">当前{{ setting.pointsName }}</view>
        <view class="h3" @click="$checkLogin('')">{{
          !isLogin ? '未登录' : userAccount.availablePoints
        }}</view>
        <view
          class="span"
          @click="skipRecord"
          v-show="data.top.visibleBtn"
          :style="{ borderColor: data.top.color }"
          >交易记录</view
        >
        <image :src="'static/common/bg-strip.png' | formatUrl" />
      </view>
    </template>
    <template v-if="setting.homePageModuleCode === 'fans_club'" slot-scope="{ data }">
      <view class="fans-club-top" v-if="data.top && data.header.isShowUser">
        <image class="bg" :src="data.top.bgImage | formatUrl" mode="aspectFill" />
        <view class="wrap" :style="{ backgroundImage: `url(${obsUrl}${data.top.userBgImage})` }">
          <view class="user-info">
            <image
              class="avatar"
              :src="userInfo.avatarUrl || 'static/common/member-default.png' | formatUrl"
              mode="aspectFill"
            />
            <view class="info" v-if="isLogin">
              <view class="name" :style="{ color: data.top.nickColor }">{{
                userInfo.nickName || '微信用户'
              }}</view>
              <view class="level" v-if="userInfo.rankName">
                <text class="iconfont icon-badge"></text>
                {{ userInfo.rankName }}
              </view>
            </view>
            <view class="info" v-else>
              <view class="name" @click="$checkLogin('')"
                ><button
                  class="btn btn-primary btn-small"
                  :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }"
                >
                  授权登录
                </button></view
              >
              <view class="tip">登录后解锁更多功能</view>
            </view>
            <template v-if="isLogin">
              <view class="points" :style="{ color: data.top.textColor }">
                <view class="h3">{{ userAccount.availablePoints }}</view>
                <view class="p">{{ setting.pointsName }}</view>
              </view>
              <button
                v-if="!isSignIn"
                class="btn-sign"
                @click="skipLink({ path: '/pages/index/pointsMall/user' })"
                :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }"
              >
                去签到
              </button>
              <button
                v-else
                class="btn-sign disabled"
                :style="{ backgroundColor: theme.primary.color6, color: theme.primaryTextColor }"
              >
                已签到
              </button>
            </template>
          </view>
          <view
            class="border"
            v-if="data.top.borderShow"
            :style="{ backgroundColor: data.top.borderColor }"
          ></view>

          <view class="navs 222cs" :style="{ color: data.top.navsColor }">
            <view
              class="item"
              v-for="(item, index) in data.top.navs"
              :key="index"
              @click="skipLink(item.link)"
            >
              <image :src="item.imgSrc | formatUrl(60)" mode="aspectFill" />
              <view class="overflow-ellipsis">{{ item.title }}</view>
            </view>
          </view>
        </view>
      </view>
    </template>
  </home>
</template>

<script>
import Home from '../components/home'
import { GetUserPoints } from '@/service/pointsMall/user'
import { GetSignInStatus } from '@/service/common'
import { mapState } from 'vuex'

export default {
  components: {
    Home,
  },
  data() {
    return {
      userAccount: {},
      isSignIn: false,
    }
  },
  computed: {
    ...mapState(['setting', 'isLogin', 'obsUrl', 'userInfo', 'theme']),
  },

  mounted() {
    console.log('home mounted')
  },
  methods: {
    initPage() {
      this.$refs.home.loadData()
    },
    refresh() {
      this.$refs.home.refresh()
    },

    loadData() {
      if (
        this.setting.homePageModuleCode === 'points_mall' ||
        this.setting.homePageModuleCode === 'fans_club'
      ) {
        this.loadUserInfo()
      }
    },
    loadUserInfo() {
      if (this.isLogin) {
        GetUserPoints().then(res => {
          this.userAccount = res.data
        })
        GetSignInStatus().then(res => {
          this.isSignIn = res.data
        })
      }
    },
    skipRecord() {
      this.$checkLogin(() => {
        uni.navigateTo({
          url: '/pointsMall/user/tradeRecord',
        })
      })
    },
    skipLink(link) {
      if (!link) {
        return
      }
      this.$navigateTo({
        ...link,
      })
    },
  },
  onShareAppMessage() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.integral-top {
  position: relative;
  height: 216rpx;
  padding-left: 40rpx;
  color: #fff;
  background: linear-gradient(to right, #f52440, #ff0087);
  font-size: 30rpx;
  image {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .p {
    padding-top: 48rpx;
    line-height: 40rpx;
    color: inherit;
  }
  .h3 {
    position: relative;
    z-index: 2;
    margin-top: 24rpx;
    font-size: 56rpx;
    line-height: 64rpx;
    font-weight: bold;
    color: inherit;
  }
  .span {
    position: absolute;
    z-index: 2;
    display: inline-block;
    top: 48rpx;
    right: 48rpx;
    border: 2rpx solid #fff;
    width: 144rpx;
    border-radius: 28rpx;
    line-height: 56rpx;
    text-align: center;
    font-size: 24rpx;
    color: inherit;
  }
}

.fans-club-top {
  .bg {
    width: 100%;
    height: 750rpx;
  }
  .wrap {
    position: relative;
    z-index: 2;
    padding: 48rpx 32rpx;
    box-sizing: border-box;
    margin: -174rpx 24rpx 0;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-color: #ffffff;
    border-radius: 16rpx;
  }

  .user-info {
    display: flex;
    align-items: center;
    .avatar {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }
    .info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .name {
        font-size: 32rpx;
        color: #333333;
        line-height: 48rpx;
      }
      .level {
        display: flex;
        align-items: center;
        margin-top: 8rpx;
        padding: 0 8rpx;
        height: 32rpx;
        background: linear-gradient(270deg, #d6caab 0%, #bda16a 100%);
        border-radius: 4rpx;
        font-size: 20rpx;
        color: #6d4500;
        text {
          position: relative;
          top: 2rpx;
          margin-right: 2rpx;
          line-height: 1;
          font-size: 24rpx;
        }
      }
      .tip {
        margin-top: 4rpx;
        line-height: 28rpx;
        font-size: 20rpx;
        color: #8c8c8c;
      }
    }
    .points {
      margin-left: auto;
      padding: 0 24rpx;
      text-align: center;
      color: #000;
      .h3 {
        font-size: 36rpx;
        font-weight: 600;
        line-height: 56rpx;
      }
      .p {
        font-size: 24rpx;
        line-height: 32rpx;
      }
    }
    .btn-sign {
      width: 120rpx;
      padding: 0;
      margin: 0 0 0 32rpx;
      line-height: 56rpx;
      background: #b8333d;
      border-radius: 28rpx;
      text-align: center;
      color: #fff;
      font-size: 24rpx;
      &::after {
        display: none;
      }

      &.disabled {
        opacity: 0.7;
      }
    }
  }

  .border {
    height: 2rpx;
    margin-top: 32rpx;
    transform: scaleY(0.5);
  }

  .navs {
    display: flex;
    align-items: center;
    margin-top: 48rpx;
    .item {
      flex: 1;
      text-align: center;
      padding: 0 16rpx;
      overflow: hidden;
      font-size: 0;
      image {
        width: 56rpx;
        height: 56rpx;
      }
      view {
        margin-top: 16rpx;
        line-height: 32rpx;
        font-size: 24rpx;
      }
    }
  }
}
</style>
