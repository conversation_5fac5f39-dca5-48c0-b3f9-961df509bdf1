<template>
  <view class="container-root" v-if="isInit">
    <view class="cell-list-item-big" v-for="item in list" :key="item.id">
      <view class="main" @click="handleChoice(item)">
        <view class="name">
          <text class="default border" v-if="item.izDefault">默认</text>
          {{ item.receiveName }}, {{ item.receivePhone }}
        </view>
        <view class="address">
          {{ item.province }}{{ item.city }}{{ item.area }}{{ item.address }}
        </view>
      </view>
      <view class="other">
        <text class="iconfont icon-edit" @click="handleEdit(item.id)"></text>
      </view>
    </view>

    <view class="no-address" v-if="list.length === 0">
      <image :src="'static/applet/empty-address.png' | formatUrl" />
      <text>暂无收货地址</text>
    </view>

    <view class="footer-btn">
      <!--      <button class="btn" @click="getChooseAddress">-->
      <!--        <text class="border-line"></text>-->
      <!--        <text>获取微信地址</text>-->
      <!--      </button>-->
      <button
        class="btn"
        @click="handleEdit('')"
        :style="{ backgroundColor: theme.primary.color6 }"
      >
        <text class="border-line" :style="{ borderColor: theme.primary.color6 }"></text>
        <text :style="{ color: theme.primaryTextColor }">新增地址</text>
      </button>
    </view>
  </view>
</template>

<script>
import { GetAddressList, SaveWechatAddress } from '@/service/common'
import { mapState, mapActions } from 'vuex'

export default {
  data() {
    return {
      list: [],
      type: 1,
      isInit: false,
    }
  },
  computed: {
    ...mapState(['setting', 'theme']),
  },
  onLoad(option) {
    if (option.type) {
      this.type = parseInt(option.type)
      if (this.type === 2) {
        uni.setNavigationBarTitle({
          title: '选择地址',
        })
      }
    }
  },
  onShow() {
    this.loadData()
  },
  methods: {
    ...mapActions(['setCacheAddressAction']),
    loadData() {
      GetAddressList({
        pageSize: 9999,
        pageNo: 1,
      }).then(res => {
        this.isInit = true
        this.list = res.data.list
      })
    },
    handleEdit(id) {
      uni.navigateTo({
        url: `../address/detail?id=${id}&type=${this.type}`,
      })
    },
    getChooseAddress() {
      uni.getSetting({
        success: res => {
          if (res.authSetting['scope.address'] != undefined && !res.authSetting['scope.address']) {
            uni.showModal({
              content: '授权失败，请手动授权',
              showCancel: false,
            })
          }
        },
      })

      uni.chooseAddress({
        success: res => {
          if (res.errMsg === 'chooseAddress:ok') {
            SaveWechatAddress({
              provinceName: res.provinceName,
              cityName: res.cityName,
              countyName: res.countyName,
              detailInfoNew: res.detailInfo,
              telNumber: res.telNumber,
              userName: res.userName,
            }).then(() => {
              this.loadData()
            })
          }
        },
      })
    },
    handleChoice(item) {
      if (this.type === 2) {
        console.log('item', item)
        this.setCacheAddressAction(item)
        uni.navigateBack({
          delta: 1,
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.container-root {
  background-color: $colorbackground;
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: 112rpx;
}

.name {
  line-height: 40rpx;
  font-weight: bold;
  .default {
    display: inline-block;
    height: 32rpx;
    padding: 0 8rpx;
    margin-right: 8rpx;
    line-height: 32rpx;
    font-size: 20rpx;
    border-radius: 16rpx;
    background-color: #fff1f1;
    border-color: #ff3d47;
    text-align: center;
    overflow: hidden;
    color: #ff3d47;
    vertical-align: middle;
    font-weight: normal;
    &::after {
      border-radius: 32rpx;
    }
  }
}

.address {
  margin-top: 8rpx;
  line-height: 40rpx;
  color: $colorgray;
  @include multiline-overflow(2);
}

.cell-list-item-big {
  position: relative;

  &::after {
    @include border-line(auto, 0, 0, 24rpx);
  }
}

.footer-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  height: 80rpx;
  padding: 16rpx 24rpx;
  background-color: #fff;
  display: flex;

  .btn {
    padding: 0;
    height: 80rpx;
    flex: 1;
    overflow: hidden;
    line-height: 80rpx;
    font-size: $size;
    color: $colortext;
    border-radius: 0;
    position: relative;
    border-radius: 80rpx;

    .border-line {
      border-color: #e6e6e6;
      border-radius: 160rpx;
    }

    &:last-child {
      background-color: #f52440;
      margin-left: 24rpx;
      color: #fff;
      .border-line {
        border-color: #f52440;
      }
    }
  }
}

.no-address {
  display: flex;
  padding-top: 200rpx;
  align-items: center;
  flex-direction: column;

  image {
    width: 284rpx;
    height: 198rpx;
    margin-bottom: 40rpx;
  }

  text {
    line-height: 40rpx;
    color: $colorgraylight;
  }
}
</style>
