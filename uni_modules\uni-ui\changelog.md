## 1.5.0（2024-01-13）
- 修复 npm包结构目录错误的问题
- uni-calendar 修复 回到今天时，月份显示不一致问题
- uni-data-picker 新增 支持 uni-app-x
- uni-datetime-picker 优化 增加noChange事件，当进行日期范围选择时，若有空值，则触发该事件 [详情](https://github.com/dcloudio/uni-ui/issues/815)
- uni-datetime-picker 修复 字节小程序时间选择范围器失效问题 [详情](https://github.com/dcloudio/uni-ui/issues/834)
- uni-datetime-picker 修复 PC端初次修改时间，开始时间未更新的Bug [详情](https://github.com/dcloudio/uni-ui/issues/737)
- uni-datetime-picker 修复 部分情况修改时间，开始、结束时间显示异常的Bug [详情](https://ask.dcloud.net.cn/question/171146)
- uni-datetime-picker 优化 当前月可以选择上月、下月的日期的Bug
- uni-file-picker 新增 微信小程序不再调用chooseImage,而是调用chooseMedia
- uni-file-picker 新增 上传文件至云存储携带本地文件名称
- uni-forms 优化 labelWidth 描述错误
fix: 修复图标大小默认值错误的问题
- uni-icons 修复 项目未使用 ts 情况下，打包报错的bug
- uni-icons 修复 size 属性为 string 时，不加单位导致尺寸异常的bug
- uni-icons 优化 兼容老版本icon类型，如 top ，bottom 等
- uni-icons 优化 兼容老版本icon类型，如 top ，bottom 等
- uni-icons 优化 uni-app x 下示例项目图标排序
- uni-icons 修复 nvue下引入组件报错的bug
-优化 size 属性支持单位
- uni-icons 新增 uni-app x 支持定义图标
- uni-notice-bar 修复动态绑定title时，滚动速度不一致的问题
更新示例工程
- uni-popup 新增 uni-popup 支持uni-app-x 注意暂时仅支持 `maskClick` `@open` `@close`
- uni-table 修复 uni-tr只有一列时minWidth计算错误，列变化实时计算更新
## 1.4.27（2023-04-21）
- uni-calendar 修复 某些情况 monthSwitch 未触发的Bug
- uni-calendar 修复 某些情况切换月份错误的Bug
- uni-data-picker 修复 更改 modelValue 报错的 bug
- uni-data-picker 修复 v-for 未使用 key 值控制台 warning
- uni-data-picker 修复代码合并时引发 value 属性为空时不渲染数据的问题
- uni-data-picker 修复 localdata 不支持动态更新的bug
- uni-data-select 修复 微信小程序点击时会改变背景颜色的 bug
- uni-data-select 修复 禁用时会显示清空按钮
- uni-data-select 优化 查询条件短期内多次变更只查询最后一次变更后的结果
- uni-data-select 调整 内部缓存键名调整为 uni-data-select-lastSelectedValue
- uni-datetime-picker 修复 日历 picker 修改年月后，自动选中当月1日 [详情](https://ask.dcloud.net.cn/question/165937)
- uni-datetime-picker 修复 小程序端 低版本 ios NaN [详情](https://ask.dcloud.net.cn/question/162979)
- uni-datetime-picker 修复 firefox 浏览器显示区域点击无法拉起日历弹框的Bug [详情](https://ask.dcloud.net.cn/question/163362)
- uni-datetime-picker 优化 值为空依然选中当天问题
- uni-datetime-picker 优化 提供 default-value 属性支持配置选择器打开时默认显示的时间
- uni-datetime-picker 优化 非范围选择未选择日期时间，点击确认按钮选中当前日期时间
- uni-datetime-picker 优化 字节小程序日期时间范围选择，底部日期换行问题
- uni-datetime-picker 修复 2.2.18 引起范围选择配置 end 选择无效的Bug [详情](https://github.com/dcloudio/uni-ui/issues/686)
- uni-datetime-picker 修复 移动端范围选择change事件触发异常的Bug [详情](https://github.com/dcloudio/uni-ui/issues/684)
- uni-datetime-picker 优化 PC端输入日期格式错误时返回当前日期时间
- uni-datetime-picker 优化 PC端输入日期时间超出 start、end 限制的Bug
- uni-datetime-picker 优化 移动端日期时间范围用法时间展示不完整问题
- uni-datetime-picker 修复 小程序端绑定 Date 类型报错的Bug [详情](https://github.com/dcloudio/uni-ui/issues/679)
- uni-datetime-picker 修复 vue3 time-picker 无法显示绑定时分秒的Bug
- uni-datetime-picker 修复 字节小程序报错的Bug
- uni-datetime-picker 修复 某些情况切换月份错误的Bug
- uni-easyinput 修复 vue3 下 keyboardheightchange 事件报错的bug
- uni-easyinput 优化 trim 属性默认值
- uni-easyinput 新增 cursor-spacing 属性
- uni-fab 新增 pattern.icon 属性，可自定义图标
- uni-file-picker 修复 手动上传删除一个文件后不能再上传的bug
- uni-forms 修复 required 参数无法动态绑定
- uni-list 优化 uni-list-chat 具名插槽`header` 非app端套一层元素，方便使用时通过外层元素定位实现样式修改
- uni-list uni-list-chat 新增 支持具名插槽`header`
- uni-list 新增 列表图标新增 customPrefix 属性 ，用法 [详见](https://uniapp.dcloud.net.cn/component/uniui/uni-icons.html#icons-props)
- uni-nav-bar 修复 自定义状态栏高度闪动BUG
- uni-nav-bar 修复 暗黑模式下边线颜色错误的bug
- uni-popup 修复 uni-popup 重复打开时的 bug
- uni-popup uni-popup-dialog 组件新增 inputType 属性
- uni-swipe-action 修复`uni-swipe-action`和`uni-swipe-action-item`不同时使用导致 closeOther 方法报错的 bug
- uni-table 修复 在vue3模式下可能会出现错误的问题
## 1.4.26（2023-01-31）
- uni-badge 修复 运行/打包 控制台警告问题
- uni-calendar 修复 某些情况切换月份错误问题
- uni-data-select 修复 不关联服务空间报错的问题
- uni-data-select 新增  属性 `format` 可用于格式化显示选项内容
- uni-datetime-picker 修复 某些情况切换月份错误问题
- uni-easyinput 新增 keyboardheightchange 事件，可监听键盘高度变化
- uni-list 修复 无反馈效果呈现的bug
## 1.4.25（2023-01-11）
- uni-file-picker 新增 sourceType 属性, 可以自定义图片和视频选择的来源
## 1.4.24（2023-01-11）
- uni-data-select 修复  当where变化时，数据不会自动更新的问题
- uni-datetime-picker 修复 多次加载组件造成内存占用的 bug
- uni-datetime-picker 修复 vue3 下 i18n 国际化初始值不正确的 bug
- uni-easyinput 修复 props 中背景颜色无默认值的bug
- uni-list 修复 uni-list-chat 在vue3下跳转报错的bug
- uni-list 修复 uni-list-chat avatar属性 值为本地路径时错误的问题
- uni-list 修复 uni-list-chat avatar属性 在腾讯云版uniCloud下错误的问题
- uni-list 修复 uni-list-chat note属性 支持：“草稿”字样功能 文本少1位的问题
- uni-list 修复 uni-list-item 的 customStyle 属性 padding值在 H5端 无效的bug
- uni-list 修复 uni-list-item 的 customStyle 属性 padding值在nvue（vue2）下无效的bug
- uni-list uni-list-chat 新增 avatar 支持 fileId
- uni-list uni-list 新增属性 render-reverse 详情参考：[https://uniapp.dcloud.net.cn/component/list.html](https://uniapp.dcloud.net.cn/component/list.html)
- uni-list uni-list-chat note属性 支持：“草稿”字样 加红显示 详情参考uni-im：[https://ext.dcloud.net.cn/plugin?name=uni-im](https://ext.dcloud.net.cn/plugin?name=uni-im)
- uni-list uni-list-item 新增属性 customStyle 支持设置padding、backgroundColor
- uni-popup 修复 nvue 下 v-show 报错
## 1.4.23（2022-10-25）
- uni-datetime-picker 修复，支付宝小程序样式错乱，[详情](https://github.com/dcloudio/uni-app/issues/3861)

- uni-nav-bar 修复 条件编译错误的bug
- uni-nav-bar 修复 nvue 环境 fixed 为 true 的情况下，无法置顶的 bug
## 1.4.22（2022-09-19）
- 优化 部分组件适配 uni-scss 主题色
- uni-badge 修复 当 text 超过 max-num 时，badge 的宽度计算是根据 text 的长度计算，更改为 css 计算实际展示宽度，详见:[https://ask.dcloud.net.cn/question/150473](https://ask.dcloud.net.cn/question/150473)
- uni-calendar 修复 表头年月切换，导致改变当前日期为选择月1号，且未触发change事件
- uni-data-select 修复 微信小程序下拉框出现后选择会点击到蒙板后面的输入框
- uni-data-select 修复 点击的位置不准确
- uni-data-select 新增 支持 disabled 属性
- uni-datetime-picker 修复，反向选择日期范围，日期显示异常，[详情](https://ask.dcloud.net.cn/question/153401?item_id=212892&rf=false)
- uni-datetime-picker 修复 close事件无效的 bug
- uni-datetime-picker 修复 移动端 maskClick 无效的 bug，详见:[https://ask.dcloud.net.cn/question/140824?item_id=209458&rf=false](https://ask.dcloud.net.cn/question/140824?item_id=209458&rf=false)
- uni-fab 修复 小程序端由于 style 使用了对象导致报错，[详情](https://ask.dcloud.net.cn/question/152790?item_id=211778&rf=false)
- uni-fab 修复 nvue 环境下，具有 tabBar 时，fab 组件下部位置无法正常获取 --window-bottom 的bug，详见：[https://ask.dcloud.net.cn/question/110638?notification_id=826310](https://ask.dcloud.net.cn/question/110638?notification_id=826310)
- uni-forms 优化 根据 rules 自动添加 required 的问题
- uni-forms 修复 item 未设置 require 属性，rules 设置 require 后，星号也显示的 bug，详见：[https://ask.dcloud.net.cn/question/151540](https://ask.dcloud.net.cn/question/151540)
- uni-nav-bar 修复 nvue 环境下 fixed 为 true 的情况下，无法置顶的 bug
- uni-notice-bar 新增 属性 fontSize，可修改文字大小。
- uni-pagination 修复，未对主题色设置默认色，导致未引入 uni-scss 变量文件报错。
- uni-pagination 修复，未对移动端当前页文字做主题色适配。
- uni-pagination 修复 es 语言 i18n 错误
## 1.4.21（2022-09-19）
- 修复，安装时未导入 uni-data-select 和 uni-tooltip 的问题。
## 1.4.20（2022-07-25）
- uni-section 新增组件
- uni-forms 修复 model 需要校验的值没有声明对应字段时，导致第一次不触发校验的bug

## 1.4.19（2022-07-07）
- uni-data-picker 优化 pc端图标位置不正确的问题
- uni-data-select 修复 pc端宽度异常的bug
## 1.4.18（2022-07-06）
- uni-forms 【重要】组件逻辑重构，部分用法旧版本不兼容，请注意兼容问题
- uni-forms 【重要】组件使用 Provide/Inject 方式注入依赖，提供了自定义表单组件调用 uni-forms 校验表单的能力
- uni-forms 新增 更多表单示例
- uni-forms 新增 model 属性，等同于原 value/modelValue 属性，旧属性即将废弃
- uni-forms 新增 validateTrigger 属性的 blur 值，仅 uni-easyinput 生效
- uni-forms 新增 onFieldChange 方法，可以对子表单进行校验，可替代binddata方法
- uni-forms 新增 子表单的 setRules 方法，配合自定义校验函数使用
- uni-forms 新增 uni-forms-item 的 setRules 方法，配置动态表单使用可动态更新校验规则
- uni-forms 修复 由 1.4.0 引发的 label 插槽不生效的bug
- uni-forms 修复 子组件找不到 setValue 报错的bug
- uni-forms 修复 uni-data-picker 在 uni-forms-item 中报错的bug
- uni-forms 修复 uni-data-picker 在 uni-forms-item 中宽度不正确的bug
- uni-forms 修复 表单校验顺序无序问题
- uni-forms 优化 子表单组件uni-datetime-picker、uni-data-select、uni-data-picker的显示样式
- uni-forms 优化 动态表单校验方式，废弃拼接name的方式
- uni-breadcrumb 修复 微信小程序 separator 不显示问题
- uni-data-checkbox 优化 在 uni-forms 中的依赖注入方式
- uni-data-picker 修复 uni-data-picker 在 uni-forms-item 中宽度不正确的bug
- uni-data-picker 优化 显示样式
- uni-data-select 优化 显示样式
- uni-datetime-picker 修复 日历顶部年月及底部确认未国际化 bug
- uni-datetime-picker 优化 组件样式，调整了组件图标大小、高度、颜色等，与uni-ui风格保持一致
- uni-easyinput 新增 在 uni-forms 1.4.0 中使用可以在 blur 时校验内容
- uni-easyinput 新增 clear 事件，点击右侧叉号图标触发
- uni-easyinput 新增 change 事件 ，仅在输入框失去焦点或用户按下回车时触发
- uni-easyinput 优化 组件样式，组件获取焦点时高亮显示，图标颜色调整等
- uni-easyinput 优化 clearable 显示策略
- uni-file-picker 修复 在uni-forms下样式不生效的bug
- uni-nav-bar 修复 组件示例中插槽用法无法显示内容的bug
- uni-swipe-action 修复 vue3 下使用组件不能正常运行的Bug
- uni-swipe-action 修复 h5端点击click触发两次的Bug
- uni-table 修复 微信小程序存在无使用组件的问题
## 1.4.17（2022-06-30）
- 支持 ios 安全区
## 1.4.16（2022-06-06）
- uni-breadcrumb 新增 支持 uni.scss 修改颜色
- uni-data-select 修复 localdata 赋值不生效的 bug
- uni-data-select 新增 支持选项禁用（数据选项设置 disabled: true 即禁用）
- uni-data-select 修复 当 value 为 0 时选择不生效的 bug
- uni-easyinput 修复 关闭图标某些情况下无法取消的bug
- uni-fav 新增 stat 属性 ，是否开启uni统计功能
- uni-goods-nav 新增 stat属性，是否开启uni统计功能
- uni-group 新增 stat属性，是否开启uni统计功能
- uni-nav-bar 新增 stat 属性 ，可开启统计 title 上报 ，仅使用了title 属性且项目开启了uni统计生效
- uni-search-bar 新增 readonly 属性，组件只读
- uni-swipe-action 修复 isPC 找不到的Bug
- uni-swipe-action  修复 在 nvue 下 disabled 失效的bug
- uni-tooltip 修复 content 为空时仍然弹出的bug
## 1.4.15（2022-05-07）
- uni-data-picker 修复 字节小程序 本地数据无法选择下一级的Bug
- uni-data-select 新增 记住上次的选项（仅 collection 存在时有效）
- uni-search-bar 修复  vue3 input 事件不生效的bug
- uni-search-bar 修复 多余代码导致的bug
- uni-tooltip 更新 text 属性变更为 content
- uni-tooltip 更新 移除 width 属性
- uni-tooltip 修复 组件根 text 嵌套组件 warning
## 1.4.14（2022-04-18）
- uni-datetime-picker 修复 Vue3 下动态赋值,单选类型未响应的 bug
- uni-easyinput 修复 默认值不生效的bug
## 1.4.13（2022-04-02）
- uni-calendar 修复 条件编译 nvue 不支持的 css 样式
- uni-calendar 修复 startDate、 endDate 属性失效的 bug
- uni-data-picker 修复 nvue 不支持的 v-show 的 bug
- uni-data-picker 修复 条件编译 nvue 不支持的 css 样式
- uni-datetime-picker 修复 Vue3 下动态赋值未响应的 bug
- uni-easyinput 修复 value不能为0的bug
- uni-popup 修复 弹出层内部无法滚动的bug
- uni-popup 修复 小程序中高度错误的bug
- uni-popup 修复 快速调用open出现问题的Bug
- uni-rate 修复 条件判断 `NaN` 错误的 bug
- uni-swipe-action 修复 按钮字体大小不能设置的bug
- uni-swipe-action 修复 h5和app端下报el错误的bug
- uni-swipe-action 修复 HBuilderX 1.4.X 版本中，h5和app端下报错的bug
## 1.4.12（2022-02-19）
- uni-collapse 修复 初始化的时候 ，open 属性失效的bug
- uni-data-checkbox 修复 multiple 为 true 时，v-model 的值为 null 报错的 bug
- uni-icons 优化 size 属性可以传入不带单位的字符串数值
- uni-icons 优化 size 支持其他单位
- uni-nav-bar 新增 left-width/right-width属性 ，可修改左右两侧的宽度
- uni-popup 修复 safeArea 属性不能设置为false的bug
## 1.4.11（2022-01-21）
- uni-collapse 修复 微信小程序resize后组件收起的bug
- uni-countdown 修复 在微信小程序中样式不生效的bug
- uni-countdown 新增 update 方法 ，在动态更新时间后，刷新组件
- uni-load-more 新增 showText属性 ，是否显示文本
- uni-load-more 修复 nvue 平台下不显示文本的bug
- uni-load-more 修复 微信小程序平台样式选择器报警告的问题
- uni-nav-bar 修复 在vue下，标题不垂直居中的bug
- uni-nav-bar 修复 height 属性类型错误
- uni-nav-bar 新增 height 属性,可修改组件高度
- uni-nav-bar 新增 dark 属性可可开启暗黑模式
- uni-nav-bar 优化 标题字数过多显示省略号
- uni-nav-bar 优化 插槽，插入内容可完全覆盖
- uni-popup 修复 isMaskClick 失效的bug
- uni-popup 新增 cancelText \ confirmText 属性 ，可自定义文本
- uni-popup 新增 maskBackgroundColor 属性 ，可以修改蒙版颜色
- uni-popup 优化 maskClick属性 更新为 isMaskClick ，解决微信小程序警告的问题

## 1.4.10（2022-01-17）
- uni-card 修复 在vue页面下略缩图显示不正常的bug
- uni-datetime-picker 修复 clear-icon 属性在小程序平台不生效的 bug
- uni-datetime-picker 修复 日期范围选在小程序平台，必须多点击一次才能取消选中状态的 bug
- uni-fab 更新 组件依赖
-
- uni-icons 修复 nvue 有些图标不显示的bug，兼容老版本图标
- uni-icons 优化 示例可复制图标名称
- uni-nav-bar 修复 color 属性不生效的bug
- uni-popup 修复 设置 safeArea 属性不生效的bug
- uni-popup 优化 组件示例
- uni-popup 修复 vuedoc 文字错误
## 1.4.9（2021-11-23）
- uni-ui 修复 vue3中某些scss变量无法找到的问题
- uni-combox 优化 label、label-width 属性
- uni-data-picker 修复 由上个版本引发的map、v-model等属性不生效的bug
- uni-file-picker 修复 参数为对象的情况下，url在某些情况显示错误的bug
- uni-icons 优化 兼容旧组件 type 值
- uni-list 修复 在vue3中to属性在发行应用的时候报错的bug
- uni-scss 修复 vue3中scss语法兼容问题
- uni-transition 修复 init 方法初始化问题
## 1.4.8（2021-11-19）
- uni-fab 修复 阴影颜色不正确的bug
## 1.4.7（2021-11-19）
- uni-ui 新增 支持国际化
- uni-ui 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- uni-ui 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-ui](https://uniapp.dcloud.io/component/uniui/uni-ui)
- uni-badge 修改 size 属性默认值调整为 small
- uni-badge 修改 type 属性，默认值调整为 error，info 替换 default
- uni-badge 修复 在字节小程序上样式不生效的 bug
- uni-calendar 修复 弹出层被 tabbar 遮盖 bug
- uni-card 重构插槽的用法 ，header 替换为 title
- uni-card 新增 actions 插槽
- uni-card 新增 cover 封面图属性和插槽
- uni-card 新增 padding 内容默认内边距离
- uni-card 新增 margin 卡片默认外边距离
- uni-card 新增 spacing 卡片默认内边距
- uni-card 新增 shadow 卡片阴影属性
- uni-card 取消 mode 属性，可使用组合插槽代替
- uni-card 取消 note 属性 ，使用actions插槽代替
- uni-collapse 优化 show-arrow 属性默认为true
- uni-collapse 新增 show-arrow 属性，控制是否显示右侧箭头
- uni-countdown 新增 font-size 支持自定义字体大小
- uni-data-checkbox 修复 在uni-forms中 modelValue 中不存在当前字段，当前字段必填写也不参与校验的问题
- uni-data-checkbox 修复 单选 list 模式下 ，icon 为 left 时，选中图标不显示的问题
- uni-data-checkbox 修复 在 uni-forms 中重置表单，错误信息无法清除的问题
- uni-dateformat 优化 默认时间不再是当前时间，而是显示'-'字符
- uni-datetime-picker 修复 hide-second 在移动端的 bug
- uni-datetime-picker 修复 单选赋默认值时，赋值日期未高亮的 bug
- uni-datetime-picker 修复 赋默认值时，移动端未正确显示时间的 bug
- uni-datetime-picker 新增 hide-second 属性，支持只使用时分，隐藏秒
- uni-datetime-picker 优化 取消选中时（范围选）直接开始下一次选择, 避免多点一次
- uni-datetime-picker 优化 移动端支持清除按钮，同时支持通过 ref 调用组件的 clear 方法
- uni-datetime-picker 优化 调整字号大小，美化日历界面
- uni-datetime-picker 优化 范围选择器在 pc 端过宽的问题
- uni-datetime-picker 新增 支持作为 uni-forms 子组件相关功能
- uni-datetime-picker 修复 在 uni-forms 中使用时，选择时间报 NAN 错误的 bug
- uni-datetime-picker 修复 type 属性动态赋值无效的 bug
- uni-datetime-picker 修复 ‘确认’按钮被 tabbar 遮盖 bug
- uni-datetime-picker 修复 组件未赋值时范围选左、右日历相同的 bug
- uni-datetime-picker 修复 范围选未正确显示当前值的 bug
- uni-datetime-picker 修复 h5 平台（移动端）报错 'cale' of undefined 的 bug
- uni-easyinput 修复 在 uni-forms 的动态表单中默认值校验不通过的 bug
- uni-easyinput 修复 在 uni-forms 中重置表单，错误信息无法清除的问题
- uni-file-picker 新增 参数中返回 fileID 字段
- uni-file-picker 修复 腾讯云传入fileID 不能回显的bug
- uni-file-picker 修复 选择图片后，不能放大的问题
- uni-file-picker 修复 由于 0.2.11 版本引起的不能回显图片的Bug
- uni-file-picker 新增 clearFiles(index) 方法，可以手动删除指定文件
- uni-file-picker 修复 v-model 值设为 null 报错的Bug
- uni-file-picker 修复 return-type="object" 时，无法删除文件的Bug
- uni-file-picker 修复 auto-upload 属性失效的Bug
- uni-forms 修复 label 插槽不生效的bug
- uni-forms 修复 没有添加校验规则的字段依然报错的Bug
- uni-forms 修复 重置表单错误信息无法清除的问题
- uni-forms 修复 表单验证只生效一次的问题
- uni-icons 新增 更多图标
- uni-icons 优化 自定义图标使用方式
- uni-link 修复 在 nvue 下不显示的 bug
- uni-pagination 修复 current 、value 属性未监听，导致高亮样式失效的 bug
- uni-rate 优化 默认值修改为 0 颗星
- uni-search-bar 修复 value 属性与 modelValue 属性不兼容的Bug
- uni-swipe-action 新增 close-all 方法，关闭所有已打开的组件
- uni-swipe-action 新增 resize() 方法，在非微信小程序、h5、app-vue端出现不能滑动的问题的时候，重置组件
- uni-swipe-action 修复 app 端偶尔出现类似 Page[x][-x,xx;-x,xx,x,x-x] 的问题
- uni-swipe-action 优化 微信小程序、h5、app-vue 滑动逻辑，避免出现动态新增组件后不能滑动的问题
- uni-tag 新增 提供组件设计资源，组件样式调整
- uni-tag 移除 插槽
- uni-tag 移除 type 属性的 royal 选项
- uni-tag type 不是 default 时，size 为 small 字体大小显示不正确
## 1.4.2（2021-08-20）
- 新增 uni-ui 组件支持国际化 i18n
- uni-collapse 优化 show-arrow 属性默认为true
- uni-collapse 新增 show-arrow 属性，控制是否显示右侧箭头
- uni-data-checkbox 修复 单选 list 模式下 ，icon 为 left 时，选中图标不显示的问题
- uni-easyinput 修复 在 uni-forms 的动态表单中默认值校验不通过的 bug
- uni-file-picker 修复 由于 0.2.11 版本引起的不能回显图片的Bug
- uni-file-picker 新增 clearFiles(index) 方法，可以手动删除指定文件
- uni-file-picker 修复 v-model 值设为 null 报错的Bug
- uni-swipe-action 新增 close-all 方法，关闭所有已打开的组件
- uni-swipe-action 新增 resize() 方法，在非微信小程序、h5、app-vue端出现不能滑动的问题的时候，重置组件
- uni-swipe-action 修复 app 端偶尔出现类似 Page[x][-x,xx;-x,xx,x,x-x] 的问题
- uni-swipe-action 优化 微信小程序、h5、app-vue 滑动逻辑，避免出现动态新增组件后不能滑动的问题
## 1.4.0（2021-08-13）
- uni-calendar 修复 弹出层被 tabbar 遮盖 bug
- uni-data-checkbox 修复 在 uni-forms 中重置表单，错误信息无法清除的问题
- uni-dateformat 调整 默认时间不再是当前时间，而是显示'-'字符
- uni-datetime-picker 新增 适配 vue3
- uni-datetime-picker 新增 支持作为 uni-forms 子组件相关功能
- uni-datetime-picker 修复 在 uni-forms 中使用时，选择时间报 NAN 错误的 bug
- uni-datetime-picker 修复 type 属性动态赋值无效的 bug
- uni-datetime-picker 修复 ‘确认’按钮被 tabbar 遮盖 bug
- uni-datetime-picker 修复 组件未赋值时范围选左、右日历相同的 bug
- uni-datetime-picker 修复 范围选未正确显示当前值的 bug
- uni-datetime-picker 修复 h5 平台（移动端）报错 'cale' of undefined 的 bug
- uni-easyinput 修复 在 uni-forms 中重置表单，错误信息无法清除的问题
- uni-file-picker 修复 return-type="object" 时，无法删除文件的Bug
- uni-file-picker 修复 auto-upload 属性失效的Bug
- uni-forms 修复 没有添加校验规则的字段依然报错的Bug
- uni-forms 修复 重置表单错误信息无法清除的问题
- uni-forms 优化 组件文档
- uni-forms 修复 表单验证只生效一次的问题
- uni-tag type 不是 default 时，size 为 small 字体大小显示不正确
## 1.3.9（2021-08-02）
- uni-datetime-picker 新增 return-type 属性支持返回 date 日期对象
- uni-file-picker 修复 fileExtname属性不指定值报错的Bug
- uni-file-picker 修复 在某种场景下图片不回显的Bug
- uni-link 支持自定义插槽
## 1.3.8（2021-07-31）
- uni-ui 组件兼容 vue3
- uni-collapse 修复 由1.2.0版本引起的 change 事件返回 undefined 的Bug
- uni-collapse 优化 组件示例
- uni-collapse 新增 组件折叠动画
- uni-collapse 新增 value\v-model 属性 ，动态修改面板折叠状态
- uni-collapse 新增 title 插槽 ，可定义面板标题
- uni-collapse 新增 border 属性 ，显示隐藏面板内容分隔线
- uni-collapse 新增 title-border 属性 ，显示隐藏面板标题分隔线
- uni-collapse 修复 resize 方法失效的Bug
- uni-collapse 修复 change 事件返回参数不正确的Bug
- uni-collapse 优化 H5、App 平台自动更具内容更新高度，无需调用 reszie() 方法
- uni-data-checkbox 优化 在uni-forms组件，与label不对齐的问题
- uni-data-checkbox 修复 单选默认值为0不能选中的Bug
- uni-easyinput 优化 errorMessage 属性支持 Boolean 类型
- uni-file-picker 修复 return-type为object下，返回值不正确的Bug
- uni-file-picker 修复（重要） H5 平台下如果和uni-forms组件一同使用导致页面卡死的问题
- uni-file-picker 优化 h5平台下上传文件导致页面卡死的问题
- uni-forms 修复 vue2 下条件编译导致destroyed生命周期失效的Bug
- uni-forms 修复 1.2.1 引起的示例在小程序平台报错的Bug
- uni-forms 修复 动态校验表单，默认值为空的情况下校验失效的Bug
- uni-forms 修复 不指定name属性时，运行报错的Bug
- uni-forms 优化 label默认宽度从65调整至70，使required为true且四字时不换行
- uni-forms 优化 组件示例，新增动态校验示例代码
- uni-forms 优化 组件文档，使用方式更清晰
- uni-list 修复 与其他组件嵌套使用时，点击失效的Bug
- uni-swipe-action 修复 跨页面修改组件数据 ，导致不能滑动的问题
## 1.3.7（2021-07-16）
- uni-ui 兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
- uni-datetime-picker 修复 单选日期类型，初始赋值后不在当前日历的 bug
- uni-datetime-picker 新增 clearIcon 属性，显示框的清空按钮可配置显示隐藏（仅 pc 有效）
- uni-datetime-picker 优化 移动端移除显示框的清空按钮，无实际用途
- uni-datetime-picker 修复 组件赋值为空，界面未更新的 bug
- uni-datetime-picker 修复 start 和 end 不能动态赋值的 bug
- uni-datetime-picker 修复 范围选类型，用户选择后再次选择右侧日历（结束日期）显示不正确的 bug
## 1.3.6（2021-07-09）
- uni-data-checkbox 优化 删除无用日志
- uni-data-checkbox 修复 由 0.1.9 引起的非 nvue 端图标不显示的问题
- uni-data-checkbox 修复 nvue 黑框样式问题
- uni-datetime-picker 修复 范围选择不能动态赋值的 bug
- uni-datetime-picker 修复 范围选择的初始时间在一个月内时，造成无法选择的bug
- uni-datetime-picker 优化 弹出层在超出视窗边缘定位不准确的问题
- uni-datetime-picker 修复 范围起始点样式的背景色与今日样式的字体前景色融合，导致日期字体看不清的 bug
- uni-datetime-picker 优化 弹出层在超出视窗边缘被遮盖的问题
- uni-datetime-picker 新增 maskClick 事件
- uni-datetime-picker 修复 特殊情况日历 rpx 布局错误的 bug，rpx -> px
- uni-datetime-picker 修复 范围选择时清空返回值不合理的bug，['', ''] -> []
- uni-datetime-picker 新增 日期时间显示框支持插槽
- uni-file-picker 修复 sourceType 缺少默认值导致 ios 无法选择文件
- uni-file-picker 优化 解耦与uniCloud的强绑定关系 ，如不绑定服务空间，默认autoUpload为false且不可更改
- uni-table 新增 uni-th 支持 date 日期筛选范围
- uni-table 新增 uni-th 支持 range 筛选范围
- uni-table 新增 uni-th 筛选功能
## 1.3.5（2021-07-02）
- uni-card 优化 图文卡片无图片加载时，提供占位图标
- uni-card 新增 header 插槽，自定义卡片头部（ 图文卡片 mode="style" 时，不支持）
- uni-card 修复 thumbnail 不存在仍然占位的 bug
- uni-data-checkbox 修复 selectedTextColor 属性不生效的Bug
- uni-datetime-picker 优化 添加 uni-icons 依赖
- uni-easyinput 修复 confirmType 属性（仅 type="text" 生效）导致多行文本框无法换行的 bug
- uni-file-picker 修复 由 0.0.10 版本引发的 returnType 属性失效的问题
- uni-file-picker 优化 文件上传后进度条消失时机
- uni-file-picker 修复 在uni-forms 中，删除文件 ，获取的值不对的Bug
- uni-forms 修复 pattern 属性在微信小程序平台无效的问题
## 1.3.4（2021-06-25）
- uni-badge 优化 示例项目
- uni-countdown 修复 uni-countdown 重复赋值跳两秒的 bug
- uni-easyinput 修复 passwordIcon 属性拼写错误的 bug
- uni-forms 修复 validate-trigger属性为submit且err-show-type属性为toast时不能弹出的Bug
- uni-forms 修复 只写setRules方法而导致校验不生效的Bug
- uni-forms 修复 由上个办法引发的错误提示文字错位的Bug
- uni-forms 修复 不设置 label 属性 ，无法设置label插槽的问题
- uni-forms 修复 不设置label属性，label-width属性不生效的bug
- uni-forms 修复 setRules 方法与rules属性冲突的问题
- uni-link 新增 download 属性，H5平台下载文件名
- uni-popup 新增 mask-click 遮罩层点击事件
- uni-popup 修复 nvue 平台中间弹出后，点击内容，再点击遮罩无法关闭的Bug
- uni-tag 修复 uni-tag 在字节跳动小程序上 css 类名编译错误的 bug
## 1.3.3（2021-06-18）
- uni-easyinput 新增 passwordIcon 属性，当type=password时是否显示小眼睛图标
- uni-easyinput 修复 confirmType 属性不生效的问题
- uni-easyinput 修复 disabled 状态可清出内容的 bug
- uni-file-picker 修复 删除文件时无法触发 v-model 的Bug
- uni-popup 修复 H5平台中间弹出后，点击内容，再点击遮罩无法关闭的Bug
- uni-popup 修复 错误的 watch 字段
- uni-popup 修复 safeArea 属性不生效的问题
- uni-popup 修复 点击内容，再点击遮罩无法关闭的Bug
## 1.3.2（2021-06-04）
- uni-data-checkbox 新增 map 属性，可以方便映射text/value属性
- uni-data-checkbox 修复 不关联服务空间的情况下组件报错的Bug
- uni-data-picker 修复 上个版本引出的本地数据无法选择带有children的2级节点
- uni-forms 修复 动态删减数据导致报错的问题
- uni-forms 新增 modelValue 属性 ，value 即将废弃
- uni-forms 新增 uni-forms-item 可以设置单独的 rules
- uni-forms 新增 validate 事件增加 keepitem 参数，可以选择那些字段不过滤
- uni-forms 优化 submit 事件重命名为 validate
- uni-data-picker 修复 无法加载云端数据的问题
- uni-data-picker 修复 v-model无效问题
- uni-data-picker 修复 loaddata 为空数据组时加载时间过长问题
- uni-datetime-picker 修复 图标在小程序上不显示的 bug
- uni-datetime-picker 优化 重命名引用组件，避免潜在组件命名冲突
- uni-datetime-picker 优化 代码目录扁平化
- uni-tag 修复 未定义 sass 变量 "$uni-color-royal" 的bug
## 1.3.1（2021-05-14）
- uni-badge 新增 uni-badge 的 absolute 属性，支持定位
- uni-badge 新增 uni-badge 的 offset 属性，支持定位偏移
- uni-badge 新增 uni-badge 的 is-dot 属性，支持仅显示有一个小点
- uni-badge 新增 uni-badge 的 max-num 属性，支持自定义封顶的数字值，超过 99 显示99+
- uni-badge 优化 uni-badge 属性 custom-style， 支持以对象形式自定义样式
- uni-badge 修复 uni-badge 在 App 端，数字小于10时不是圆形的bug
- uni-badge 修复 uni-badge 在父元素不是 flex 布局时，宽度缩小的bug
- uni-badge 新增 uni-badge 属性 custom-style， 支持自定义样式
- uni-datetime-picker 修复 ios 下不识别 '-' 日期格式的 bug
- uni-datetime-picker 优化 pc 下弹出层添加边框和阴影
- uni-datetime-picker 修复 在 admin 中获取弹出层定位错误的bug
- uni-datetime-picker 修复 type 属性向下兼容，默认值从 date 变更为 datetime
- uni-datetime-picker 支持日历形式的日期+时间的范围选择
- uni-steps 修复 uni-steps 横向布局时，多行文字高度不合理的 bug
- uni-countdown 修复 uni-countdown 不能控制倒计时的 bug
- uni-tag 修复 royal 类型无效的bug
- uni-tag 修复 uni-tag 宽度不自适应的bug
- uni-tag 新增 uni-tag 支持属性 custom-style 自定义样式
- uni-link 新增 href 属性支持 tel:|mailto:
- uni-popup 修复 组件内放置 input 、textarea 组件，无法聚焦的问题
- uni-popup 新增 type 属性的 left\right 值，支持左右弹出
- uni-popup 新增 open(String:type) 方法参数 ，可以省略 type 属性 ，直接传入类型打开指定弹窗
- uni-popup 新增 backgroundColor 属性，可定义主窗口背景色,默认不显示背景色
- uni-popup 新增 safeArea 属性，是否适配底部安全区
- uni-popup 修复 App\h5\微信小程序底部安全区占位不对的Bug
- uni-popup 修复 App 端弹出等待的Bug
- uni-popup 优化 提升低配设备性能，优化动画卡顿问题
- uni-popup 优化 更简单的组件自定义方式
- uni-table 修复 示例项目缺少组件的Bug
- uni-forms 修复 自定义检验器失效的问题
- uni-title 修复 示例项目缺少组件的Bug
- uni-transition 修复 示例项目缺少组件的Bug
- uni-swiper-dot 修复 示例项目缺少组件的Bug
- uni-ui 新增 组件示例地址
## 1.3.0（2021-04-23）
- uni-combox 优化 添加依赖 uni-icons, 导入后自动下载依赖
- uni-data-picker 修复 非树形数据有 where 属性查询报错的问题
- uni-fav 优化 添加依赖 uni-icons, 导入后自动下载依赖
- uni-goods-nav 优化 添加依赖 uni-icons, 导入后自动下载依赖
- uni-nav-bar 优化 添加依赖 uni-icons, 导入后自动下载依赖
- uni-notice-bar 优化 添加依赖 uni-icons, 导入后自动下载依赖
- uni-number-box 修复 uni-number-box 浮点数运算不精确的 bug
- uni-number-box 修复 uni-number-box change 事件触发不正确的 bug
- uni-number-box 新增 uni-number-box v-model 双向绑定
- uni-rate 修复 布局变化后 uni-rate  星星计算不准确的 bug
- uni-rate 优化 添加依赖 uni-icons, 导入 uni-rate 自动下载依赖
- uni-search-bar 优化 添加依赖 uni-icons, 导入后自动下载依赖
- uni-steps 优化 添加依赖 uni-icons, 导入后自动下载依赖
- uni-transition 新增 通过方法自定义动画
- uni-transition 新增 custom-class 非 NVUE 平台支持自定义 class 定制样式
- uni-transition 优化 动画触发逻辑，使动画更流畅
- uni-transition 优化 支持单独的动画类型
- uni-transition 优化 文档示例
## 1.2.13（2021-04-16）
- uni-ui 新增 uni-data-picker 支持云端非树形表结构数据
- uni-ui 修复 uni-data-checkbox nvue 下无法选中的问题
- uni-ui 修复 uni-data-picker 根节点 parent_field 字段等于null时选择界面错乱问题
- uni-ui 修复 uni-file-picker 选择的文件非 file-extname 字段指定的扩展名报错的Bug
- uni-ui 修复 uni-swipe-action 报错 nv_navigator is not defined 的bug
- uni-ui 修复 uni-load-more 在首页使用时，h5 平台报 'uni is not defined' 的 bug
- uni-ui 优化 uni-file-picker file-extname 字段支持字符串写法，多个扩展名需要用逗号分隔
- uni-ui 优化 uni-pagination PC 和 移动端适配不同的 ui
- uni-ui 更新 uni-file-picker 组件示例
- uni-ui 修复 uni-nav-bar 当 fixed 属性为 true 时铺不满屏幕的 bug
- uni-ui 新增 uni-search-bar 的 focus 事件
- uni-ui 修复 uni-rate 属性 margin 值为 string 组件失效的 bug
- uni-data-picker 修复 本地数据概率无法回显时问题
- uni-table 新增 sortable 属性，是否开启单列排序
- uni-table 优化 表格多选逻辑
## 1.2.12（2021-03-23）
- uni-ui 新增 uni-datetime-picker 的 hide-second 属性、border 属性;
- uni-ui 修复 uni-datetime-picker 选择跟显示的日期不一样的 bug，
- uni-ui 修复 uni-datetime-picker change事件触发2次的 bug
- uni-ui 修复 uni-datetime-picker 分、秒 end 范围错误的 bug
- uni-ui 新增 uni-tr selectable 属性，用于 type=selection 时，设置某行是否可由全选按钮控制
- uni-ui 新增 uni-data-checkbox 新增 disabled属性，支持nvue
- uni-ui 优化 uni-data-checkbox  无选项时提示“暂无数据”
- uni-ui 优化 uni-data-checkbox  默认颜色显示
- uni-ui 新增 uni-link href 属性支持 tel:|mailto:
- uni-ui 新增 uni-table 示例demo
- uni-ui 修复 uni-data-picker 微信小程序某些情况下无法选择的问题，事件无法触发的问题
- uni-ui 修复 uni-nav-bar easycom 下，找不到 uni-status-bar 的bug
- uni-ui 修复 uni-easyinput 示例在 qq 小程序上的bug
- uni-ui 修复 uni-forms 动态显示uni-forms-item的情况下，submit 方法获取值错误的Bug
- uni-ui 调整 cli 项目 建议使用 easycom 方式引用组件，如使用按需引用，需手动维护组件内部引用

## 1.2.11（2021-02-24）
- 调整为uni_modules目录规范
- uni-data-picker 新增  数据驱动的picker选择器
- uni-file-picker 新增  文件选择上传
- uni-row 新增 栅格系统
- uni-data-checkbox 优化 支持 nvue
- uni-forms 修复 偶发性获取表单值错误的Bug
- uni-forms 修复 校验 uni-data-picker value 为 0 时，返回值错误的Bug
- uni-forms 修复 uni-forms-item 组件隐藏时依然触发校验的bug
- uni-forms 优化 实时校验
- uni-forms 优化 兼容nvue页面
- uni-easyinput 优化 兼容nvue页面
- uni-group 优化 兼容nvue页面
- uni-popup 优化 组件适配 PC
- uni-fab 优化 适配 PC
- uni-swiper-dot 优化 适配 PC
- uni-rate 优化 适配 PC
- uni-notice-bar 优化 适配 PC
- uni-indexed-list 优化 适配 PC
- uni-combox 优化 适配 PC
- uni-transition 优化 适配 PC
- uni-nav-bar 优化 适配 PC
- uni-swipe-action 优化 适配 PC
