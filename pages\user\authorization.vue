<template>
  <view class="container-root">
    <view class="content">
      <view class="title">协议与规则</view>
      <view class="rows">
        <view class="flex-row border-b">
          <view class="cell-title">
            用户协议
            <view class="time">授权同意时间：{{ authorizeInfo.userProtocolTime || '-' }}</view>
          </view>
        </view>
        <view class="flex-row border-b">
          <view class="cell-title">
            隐私政策
            <view class="time">授权同意时间：{{ authorizeInfo.privacyProtocolTime || '-' }}</view>
          </view>
        </view>
        <view class="flex-row border-b">
          <view class="cell-title">
            赠酒免责条款
            <view class="time"
              >授权同意时间：{{ authorizeInfo.disclaimerProtocolTime || '-' }}</view
            >
          </view>
        </view>
      </view>

      <view class="title">个人信息</view>
      <view class="rows">
        <view class="flex-row border-b" v-if="authorizeInfo.iconCtn">
          <view class="cell-title">头像</view>
          <view class="cell-value">
            <switch :checked="authorizeInfo.iconCtn" @change="handleCloseAvatar" />
          </view>
        </view>
        <view class="flex-row border-b" v-if="authorizeInfo.nickNameCtn">
          <view class="cell-title">昵称</view>
          <view class="cell-value">
            <switch :checked="authorizeInfo.nickNameCtn" @change="handleCloseNickname" />
          </view>
        </view>
        <view class="flex-row border-b">
          <view class="cell-title">手机号</view>
          <view class="cell-value">
            <switch :checked="authorizeInfo.mobileCtn" disabled style="opacity: 0.5" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { GetUserAuthorize, SetIconCtn, SetNickNameCtn } from '@/service/common'
import { saveUserInfo } from '@/controller/login'
import { mapState, mapMutations } from 'vuex'

export default {
  data() {
    return {
      authorizeInfo: {},
    }
  },
  computed: {
    ...mapState(['userInfo']),
  },

  onLoad() {
    this.getAuthorizeInfo()
  },

  methods: {
    ...mapMutations(['SET_USERINFO']),
    getAuthorizeInfo() {
      GetUserAuthorize().then(res => {
        this.authorizeInfo = res.data
      })
    },

    handleCloseAvatar(e) {
      const flag = e.detail.value
      SetIconCtn({ flag }).then(() => {
        const userInfo = this.userInfo
        userInfo.avatarUl = ''
        this.SET_USERINFO(userInfo)
        wx.setStorageSync('userInfo', userInfo)
        this.authorizeInfo.iconCtn = flag
      })
    },

    handleCloseNickname(e) {
      const flag = e.detail.value
      SetNickNameCtn({ flag }).then(() => {
        const userInfo = this.userInfo
        userInfo.nickName = ''
        this.SET_USERINFO(userInfo)
        wx.setStorageSync('userInfo', userInfo)
        this.authorizeInfo.nickNameCtn = flag
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 0 24rpx;
  .title {
    color: #666;
    padding: 32rpx 0 16rpx;
  }
}

.rows {
  background-color: #fff;
  padding: 0 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.flex-row {
  display: flex;
  padding: 24rpx 0;
  position: relative;
  align-items: center;
}
.cell-title {
  flex: 1;
  color: #000;
  line-height: 40rpx;
  font-size: 28rpx;
  .time {
    margin-top: 8rpx;
    color: #8c8c8c;
    font-size: 24rpx;
    line-height: 32rpx;
  }
}
.iconfont {
  color: #8c8c8c;
}
.icon-arrow-right {
  font-size: 20rpx;
}
</style>
