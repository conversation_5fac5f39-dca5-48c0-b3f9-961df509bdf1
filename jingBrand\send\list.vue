<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '我的转赠' }"
      @init="statusBarHeight = $event"
    />

    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <ul class="tabs">
          <li
            v-for="item in types"
            :key="item.value"
            class="tab"
            @click="handleTabClick(item.value)"
          >
            {{ item.label }}
          </li>
          <li class="line" :style="{ left: tabLeft }" />
        </ul>

        <div v-for="item in list" :key="item.id" class="card">
          <div class="header">
            <div class="flex flex-align-center">
              <img
                src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/IWAk8HBXQuCVqdHoc5XID.png"
              />
              <span>{{ item.depositNo }}</span>
            </div>
            <span :class="statusClass[item.status]">
              {{ getStatusText(item) }}
            </span>
          </div>

          <div class="inner">
            <div class="meta">
              <img :src="item.depositImgList[0] | formatUrl" class="cover" mode="aspectFill" />

              <div>
                <p class="title">{{ item.depositName }}</p>

                <!--                <p class="desc">-->
                <!--                  <span>{{ item.capacity }}ml</span>-->
                <!--                </p>-->
              </div>
            </div>

            <img
              v-if="item.capacity === 0"
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/jlusgYmY8VHCCud5dA_qx.png?x-image-process=image/resize,m_lfit,w_170"
              class="status"
            />
          </div>

          <div class="flex flex-justify-between flex-align-center">
            <div class="flex flex-column" style="row-gap: 8rpx; max-width: 450rpx">
              <template v-if="item.status === 'ACQUIRE'">
                <template v-if="item.type === 1">
                  <p><span class="text-gray">接收时间：</span>{{ item.acceptTime }}</p>
                  <p><span class="text-gray">接收人：</span>{{ item.toCustomName }}</p>
                </template>
                <template v-else>
                  <p><span class="text-gray">赠送人：</span>{{ item.customName }}</p>
                </template>
              </template>

              <template v-else-if="item.status === 'IN_TRANSFER'">
                <template v-if="item.type === 1">
                  <p>
                    <span class="text-gray">赠送时间：</span>
                    {{ item.createTime }}
                  </p>
                  <p>
                    <span class="text-gray">接收人：</span>
                    {{ item.toCustomName }}
                  </p>
                </template>
              </template>

              <template v-else>
                <p>
                  <span class="text-gray">赠送人：</span>
                  {{ item.toCustomName }}
                </p>
              </template>
            </div>

            <button
              v-if="item.status === 'IN_TRANSFER' && item.type === 1"
              class="btn"
              style="margin-inline: 0; white-space: nowrap"
              @click="cancel(item)"
            >
              撤销赠送
            </button>
          </div>
        </div>

        <div v-if="!list || !list.length" class="empty">
          <img
            class="img"
            :src="
              `${OSS_PREFIX}hishop/upload/oKMEB_HehmmnVxtN39AyG.png?x-image-process=image/resize,m_lfit,w_170`
            "
            alt=""
          />
          <p class="title">转赠记录为空</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import { getSendList, cancelSend } from '@/service/jingBrand/send'
import { OSS_PREFIX } from '@/config/system'

export default {
  name: 'list',
  components: { CustomNav },
  data() {
    return {
      OSS_PREFIX,
      statusBarHeight: 168,
      types: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '我赠送的',
          value: 1,
        },
        {
          label: '我接收的',
          value: 2,
        },
      ],
      query: {
        type: '',
      },
      list: [],
      statusText: {
        IN_TRANSFER: '赠送中',
        ACQUIRE: '已领取',
        CLOSED: '已取消',
      },
      statusClass: {
        IN_TRANSFER: 'text-primary',
        ACQUIRE: 'text-brown',
        CLOSED: 'text-gray',
      },
    }
  },
  computed: {
    tabLeft() {
      const index = this.types.findIndex(t => t.value === this.query.type)
      const itemWidth = 100 / this.types.length
      return `${itemWidth * index + itemWidth / 2}%`
    },
  },
  onLoad() {
    this.init()
  },
  methods: {
    handleTabClick(value) {
      this.query.type = value
      this.init()
    },

    getStatusText({ status, type }) {
      if (status === 'ACQUIRE') {
        if (type === 1) {
          return '已赠送'
        } else if (type === 2) {
          return '已领取'
        }
      }

      if (status === 'IN_TRANSFER') {
        return '赠送中'
      }

      if (status === 'CLOSED') {
        return '已取消'
      }
    },

    async init() {
      try {
        const res = await getSendList(this.query)
        res.data.forEach(item => {
          item.depositImgList = JSON.parse(item.depositImg || '[]')
        })
        this.list = res.data
      } catch (e) {
        // this.list = []
        console.log(e)
      }
    },

    cancel(item) {
      uni.showModal({
        title: '提示',
        content: '是否确认撤销赠送？',
        success: res => {
          if (res.confirm) {
            cancelSend({ transferId: item.id }).then(() => {
              this.init().then(() => {
                uni.showToast({
                  title: '撤销成功',
                })
              })
            })
          }
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  padding-bottom: 24rpx;
  background-color: #f5f4f3;
}
//
//.content {
//  margin: 24rpx;
//  padding: 32rpx 24rpx;
//  background-color: #fff;
//  border-radius: 16rpx;
//}
.card {
  margin: 24rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  background-color: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }
}

.text-brown {
  color: #bc9173;
}

.text-gray {
  color: #b0b0b0;
}

.text-primary {
  color: #0b7d83;
}

.tabs {
  position: relative;
  display: flex;
  justify-content: space-around;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #faf9f7;

  .tab {
    flex: 1;
    line-height: 88rpx;
    text-align: center;

    //&--active {
    //  color: #0b7d83;
    //  border-bottom: 8rpx solid #0b7d83;
    //}
  }

  .line {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 32rpx;
    height: 8rpx;
    background-color: #0b7d83;
    border-radius: 4rpx;
    transform: translateX(-50%);
    transition: left 0.2s ease;
  }
}

.inner {
  position: relative;
  margin: 24rpx auto;
  padding: 24rpx;
  background-color: #f9f9f9;
  border-radius: 4rpx;
}

.meta {
  display: flex;
}

.cover {
  width: 160rpx;
  min-width: 160rpx;
  height: 160rpx;
  margin-right: 16rpx;
  background-color: #f5f4f3;
}

.title {
  font-size: 28rpx;
}

.desc {
  margin-top: 8rpx;
  font-size: 24rpx;
}

.status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120rpx;
  height: 120rpx;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .img {
    width: 280rpx;
    height: 280rpx;
  }

  .title {
    margin-top: 0;
    color: #999;
  }
}
</style>
