const obsUrl = uni.getStorageSync('obsUrl') || ''
let userInfo = uni.getStorageSync('userInfo') || {}
// 判断是否登录有效
const token = uni.getStorageSync('token')
let isLogin = !!token

export default {
  obsUrl,
  imageProcess: '',
  isLogin,
  tabBarSelected: '/pages/index/basicSystem/home', // 当前选中的tab
  setting: {
    pointsName: '积分',
  },
  isAuthorized: true, // 是否授权当前端口
  theme: {
    primary: {},
    secondary: {},
  }, // 主题色
  userInfo, // 用户信息
  isLoginPage: false, // 是否在登录页面
  againBuyList: [], // 再来一单的商品id集合,用于购物车默认选中
  wechatSubscribeMessage: [], // 微信订阅消息
  cacheAddress: null, // 缓存一个地址, 用来订单选择地址时使用
  cachePassword: null, // 缓存用户交易密码,重置交易密码时使用
  cartData: {}, // 普通购物车（用于计算购物车里面的总数量）
  reductions: {}, // 满减送活动购物车（专门存储参与活动的购物车，用于数据交互）
  cartDataInit: false,
  sceneId: 0, // 直播场次Id
  liveCoupon: {},
  bargainSetting: {},
  bargainId: 0,
  distributionReName: {},
  referrer: {
    referrerId: 0,
    url: '',
  }, // 分销员分销数据
  inviteReferrerId: 0, // 邀请分销，上级分销员id
  wxConfig: {}, // 微信jssdk参数
  isShareGuide: false, // 微信H5端引导提示
  rookiePages: 0,
  isShowAd: false, // 是否显示广告
  adHaveRead: false, // 本次打开是否已读广告
  attachment: [], // 供应商发布商品预留信息
  cachePickupCenter: {},
  // 特殊的goods id集合
  specialGoodsIds: [],
}
