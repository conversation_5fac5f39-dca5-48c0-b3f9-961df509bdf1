<script>
import { mapActions, mapState, mapMutations } from 'vuex'

export default {
  data() {
    return {
      loaded: false,
    }
  },
  computed: {
    ...mapState(['isLogin', 'setting']),
  },
  onLaunch() {
    // 获取配置信息
    this.getSettingAction()

    // 获取特殊商品id
    if (this.isLogin) {
      this.setSpecialGoodsIds()
    }
  },
  onShow(options) {
    // 移动端存在锁屏或切换到后台定时器不走问题，故切换到前台判断token过期时间 刷新token
    if (!this.loaded) return
  },
  onHide() {
    this.loaded = true
  },
  methods: {
    ...mapActions(['getSettingAction', 'getPointsMallColorId', 'setSpecialGoodsIds']),
  },
}
</script>

<style lang="scss">
@import './assets/style/common.scss';
@import './assets/style/iconfont.css';
@import './assets/style/iconfont1.css';
@import './assets/style/flex.scss';
@import './components/uParse/parse.css';
@font-face {
  font-family: 'LatoBold';
  src: url('https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/images/basicSystem/1746363160731844608.ttf');
  font-display: swap;
}
@font-face {
  font-family: 'LatoRegular';
  src: url('https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/images/basicSystem/1748556695233101824.ttf');
  font-display: swap;
}
@font-face {
  font-family: 'siyuanBold';
  src: url('https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/images/basicSystem/1746406490559741952.woff');
  font-display: swap;
}
@font-face {
  font-family: '思源宋体 Medium';
  font-weight: 500;
  src:
    url('//at.alicdn.com/wf/webfont/oBEStfSHYFGV/Ks0pc3LLy7vF.woff2') format('woff2'),
    url('//at.alicdn.com/wf/webfont/oBEStfSHYFGV/VeDhN8f4GReK.woff') format('woff');
  font-display: swap;
}
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: '思源宋体 Heavy';
  font-weight: 900;
  src:
    url('//at.alicdn.com/wf/webfont/oBEStfSHYFGV/aSgcIwJfC6gk.woff2') format('woff2'),
    url('//at.alicdn.com/wf/webfont/oBEStfSHYFGV/Sy63PX3vBT7M.woff') format('woff');
  font-display: swap;
}
page {
  font-family: 'LatoBold';
  font-size: 28rpx;
  color: #212121;
}
</style>
