/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$text-highLight-color:#000000;//高亮
$text-light-color:#262625;//朴素
$text-inverse-color:#fff;//反色
$text-tip-color:#908E8D;//
$text-grey-color:#999999;//
$text-placeholder-color: #BFBEBD;
$text-disable-color:#c0c0c0;

/* 背景颜色 */
$bg-color:#ffffff;
$bg-upload-box:#F5F4F3;
$bg-color-grey:#f8f8f8;
$bg-color-hover:#f1f1f1;//点击状态颜色
$bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$border-color:#DDDDDD;
$border-popup-color:#F0F0F0;

/* 尺寸变量 */
/* 阴影颜色变量 */
$box-shadow-color:#BC9173;

/* 文字尺寸 */
$uni-font-size-sm:24upx;
$uni-font-size-base:28upx;
$uni-font-size-lg:32upx;

/* 图片尺寸 */
$uni-img-size-sm:40upx;
$uni-img-size-base:52upx;
$uni-img-size-lg:80upx;

/* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20upx;
$uni-spacing-row-lg: 30upx;

/* 垂直间距 */
$uni-spacing-col-sm: 8upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 24upx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36upx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30upx;

/**
 * 自定义
 */
$colorborder:#f0f0f0;
// $colorborder:#000;
$colorbackground: #F4F5F6;
$colortext: #212121;
$colorgray: #5C5C5C;
$colorgraylight: #BCBCBC;
$colorgraydeep: #8F8F8F;
$colorred: #F52440;
$coloryellow: #FFA41F;
$coloryellowlight:#FFFAEB;

$size:28rpx;

@import './assets/style/mixin.scss';