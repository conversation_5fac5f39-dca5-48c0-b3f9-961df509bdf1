<template>
  <view class="actionSheet-container" :class="{ 'actionSheet-show': show }">
    <view class="mask" @click="handleCancel"></view>
    <view class="actionSheet-body">
      <view class="title">{{ title }}</view>
      <view class="body">
        <view class="item" v-for="(item, index) in list" :key="index" @click="handleSelect(item)">
          <text>{{ fieldText ? item[fieldText] : item }}</text>
          <view class="checkbox-group">
            <text v-if="activeId == (fieldText ? item.id : item)" class="iconfont icon-on"></text>
            <text v-else class="iconfont icon-radio" />
          </view>
        </view>
      </view>
      <text class="iconfont icon-cross close" @click="handleCancel"></text>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    display: Boolean,
    list: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    fieldText: {
      type: String,
      default: ''
    },
    checked: [String, Number]
  },
  data() {
    return {
      show: false,
      activeId: this.checked
    }
  },
  computed: {
    ...mapState(['setting', 'theme']),
    isActive() {
      let flag = false

      return flag
    }
  },
  watch: {
    display(value) {
      this.show = value
    },
    show(value) {
      this.$emit('update:display', value)
    }
  },
  methods: {
    handleCancel() {
      this.show = !this.show
    },
    handleSelect(item) {
      this.activeId = this.fieldText ? item.id : item
      this.$emit('select', item)
      this.show = false
    }
  }
}
</script>

<style lang="scss" scoped>
.actionSheet-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999999;
  transform: translateY(100%);
  .mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.actionSheet-body {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #fff;
  height: 824rpx;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: all 0.3s;
  .title {
    padding: 26rpx 24rpx;
    line-height: 46rpx;
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    position: relative;
    &:after {
      @include border-line(auto, 24rpx, 0, 24rpx);
    }
  }

  .body {
    flex: 1;
    overflow-y: scroll;
    padding: 24rpx;
    .item {
      display: flex;
      justify-content: space-between;
      line-height: 40rpx;
      padding: 16rpx 0;
    }
  }

  .close {
    position: absolute;
    top: 29rpx;
    right: 24rpx;
    font-size: 40rpx;
    color: $colorgraylight;
  }
}

.actionSheet-show {
  transform: translateY(0);
  .actionSheet-body {
    transform: translateY(0);
  }
}

.iconfont {
  color: #999;
  font-size: 40rpx;
  &.icon-on {
    color: #f52440;
  }
}
</style>
