/* Flex 容器 */
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

/* Flex 对齐方式 */
.flex-justify-start {
  justify-content: flex-start;
}

.flex-justify-end {
  justify-content: flex-end;
}

.flex-justify-center {
  justify-content: center;
}

.flex-justify-between {
  justify-content: space-between;
}

.flex-justify-around {
  justify-content: space-around;
}

.flex-align-start {
  align-items: flex-start;
}

.flex-align-end {
  align-items: flex-end;
}

.flex-align-center {
  align-items: center;
}

.flex-align-baseline {
  align-items: baseline;
}

.flex-align-stretch {
  align-items: stretch;
}

.flex-align-content-start {
  align-content: flex-start;
}

.flex-align-content-end {
  align-content: flex-end;
}

.flex-align-content-center {
  align-content: center;
}

.flex-align-content-between {
  align-content: space-between;
}

.flex-align-content-around {
  align-content: space-around;
}

.flex-align-content-stretch {
  align-content: stretch;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-start {
  justify-content: flex-start;
  align-items: flex-start;
}

.flex-end {
  justify-content: flex-end;
  align-items: flex-end;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

.flex-evenly {
  justify-content: space-evenly;
  align-items: center;
}

/* Flex 项目属性 */
.flex-grow-0 {
  flex-grow: 0;
}

.flex-grow-1 {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.flex-shrink-1 {
  flex-shrink: 1;
}

.flex-basis-auto {
  flex-basis: auto;
}

.flex-basis-0 {
  flex-basis: 0;
}

.flex-order-first {
  order: -1;
}

.flex-order-none {
  order: 0;
}

.flex-order-last {
  order: 1;
}

/* 常用组合类 */
.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-row-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
