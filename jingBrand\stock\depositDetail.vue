<template>
  <div>
    <custom-nav
      :data="{ barBgColor: '#FAF9F7', title: '查看详情' }"
      @init="statusBarHeight = $event"
    />
    <div class="container" :style="{ paddingTop: statusBarHeight + 'rpx' }">
      <div class="content">
        <p class="item" style="margin-top: 0">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/7oBHWN6QoTtGAbH8OwHqw.png"
              class="icon"
            />
            坛酒名称:
          </span>
          <span class=" value">{{ detail.depositName || '--' }}</span>
        </p>

        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/XR7VeYgqDjZ86bTLuNhBS.png"
              class="icon"
            />
            贮藏编号：
          </span>
          <span class="value">{{ detail.depositNo || '--' }}</span>
        </p>

        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/h19xWygs3SvWK25d2AA4U.png"
              class="icon"
            />
            藏酒所有人：
          </span>
          <span class="value">{{ detail.customName || '--' }}</span>
        </p>

        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/np_SxEakzaqrMzLleYb7e.png"
              class="icon"
            />
            贮藏日期：
          </span>
          <span class="value"> {{ detail.sealingTime || '--' }}</span>
        </p>

        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/2e3oGG4T-Nph9P7fxoJwe.png"
              class="icon"
            />
            贮藏到期日期：
          </span>
          <span class="value">{{ detail.storeTime || '--' }}</span>
        </p>

        <!--        <p class="item">-->
        <!--          <span class="label">-->
        <!--            <img-->
        <!--              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/IARQw7BGXsXAVLgyt3ZLI.png"-->
        <!--              class="icon"-->
        <!--            />-->
        <!--            藏酒状态：-->
        <!--          </span>-->
        <!--          <span class="value">-->
        <!--            {{ detail.capacity === detail.totalCapacity ? '未开封' : '已开封' }}-->
        <!--          </span>-->
        <!--        </p>-->
        <p class="item">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/obJ2YKOaGN9V8rv1uNCLM.png"
              class="icon"
            />
            剩余容量：
          </span>
          <span class="value">
            <span class="text-primary">{{ detail.capacity | ml2L }}L</span>
            /
            <span>{{ detail.totalCapacity | ml2L }}L</span>
          </span>
        </p>
        <p class="item flex-wrap" style="gap: 24rpx">
          <span class="label">
            <img
              src="https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/hishop/upload/JyTg4FQkBMTeyiFYA5I5a.png"
              class="icon"
            />
            藏酒证书：
          </span>

          <img
            v-if="detail.certificate"
            :src="detail.certificate | formatUrl"
            mode="aspectFill"
            class="value"
            style="width: 100%"
            @click="goCert"
          />
          <!--            <Cert v-if="certificateJSON" :value="certificateJSON" :canvas-width="654" />-->
        </p>

        <button
          v-if="detail.totalCapacity && detail.capacity !== detail.totalCapacity"
          class="btn"
          style="margin-inline: 150rpx"
          @click="goRecord"
        >
          查看取酒记录
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import CustomNav from '@/components/customNav/customNav.vue'
import Cert from './components/Cert.vue'
import ml2L from '@/hooks/ml2L'

export default {
  name: 'detail',
  components: { CustomNav, Cert },
  data() {
    return {
      statusBarHeight: 168,
      detail: {},
    }
  },
  computed: {
    certificateVos() {
      return this.detail.certificateVos || []
    },
    certificateJSON() {
      if (!this.certificateVos.length) return null

      const json = JSON.parse(this.certificateVos[0].templateJson || 'null')

      if (!json) return null

      json.fields.forEach(field => {
        field.text = this.detail[field.id]
      })
      return json
    },
  },
  async onLoad() {
    this.detail = uni.getStorageSync('depositDetail')
  },
  methods: {
    ml2L,
    goCert() {
      uni.navigateTo({
        url: `/jingBrand/stock/certificate?id=${this.detail.id}`,
      })
    },
    goRecord() {
      uni.navigateTo({
        url: `/jingBrand/take/record?depositId=${this.detail.id}&targetCustomId=${this.detail.customId}`,
      })
    },
  },
}
</script>

<style scoped lang="scss">
.text-primary {
  color: #0b7d83;
}

.container {
  min-height: calc(100vh - (env(safe-area-inset-bottom) + 168rpx));
  background-color: #f5f4f3;
}
.content {
  margin: 24rpx;
  padding: 32rpx 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.cover {
  border-radius: 4rpx;
}

.item {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  margin: 40rpx auto;

  .label {
    display: flex;
    align-items: center;
    white-space: nowrap;
    font-size: 28rpx;
    color: #262626;
  }

  .icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }

  .value {
    font-size: 28rpx;
    color: #666;
  }

  .name {
    font-weight: 600;
    font-size: 32rpx;
    color: #000;
  }

  .tag {
    margin-left: 16rpx;
    padding: 2rpx 8rpx;
    font-size: 20rpx;
    color: #0b7d83;
    line-height: 28rpx;
    background: #f2f8f8;
    border-radius: 4rpx;
  }
}
</style>
