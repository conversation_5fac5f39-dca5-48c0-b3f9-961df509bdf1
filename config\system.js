/**
 * 系统配置
 *
 * @returns {String} baseUrl 所有系统接口请求地址
 * @returns {String} appId 当前小程序appId
 * @returns {Object} baseUrl 系统配置印射关系，包含的子系统命名
 * */

// 测试环境
 //const BASE_URL_BASIC_SYSTEM = 'https://basic-system-fengtan.cce.35hiw.com/'
// const BASE_URL_JING_BRAND = 'https://jingbrand-wine-fengtan.cce.35hiw.com/'
// 正式环境
const BASE_URL_BASIC_SYSTEM = 'https://jpfc.jingjiu.com/'
const BASE_URL_JING_BRAND = 'https://jpfc.jingjiu.com/'
const BASE_URL_POINTS_MALL = 'https://points-mall-dev.cce.35hiw.com/'
const BASE_URL_SCAN_MARKETING = 'https://scan-marketing-dev.cce.35hiw.com/'
const BASE_URL_FANS_CLUB = 'https://fans-club-dev.cce.35hiw.com/'
const BASE_URL_FENGTAN_WINE = 'https://fengtan-wine-dev.cce.35hiw.com/'

// 测试环境
// export const appId = 'wxd151d5aabe36d4ed' // 当前小程序appId
// export const appId = 'wxd151d5aabe36d4ed' // 供应商oa
// 正式环境
export const appId = 'wx53bc232e2ecc4bf4' // 当前小程序appId

// 系统配置隐射关系，所有子系统命名
export const APP_TYPES = {
  wine: 'basic_system',
  pointsMall: 'points_mall',
  scanMarketing: 'scan_marketing',
  fansClub: 'fans_club',
  fengtanWine: 'fengtan_wine',
  jingBrand: 'jing_brand',
}

export const baseUrl = {
  BASE_URL_BASIC_SYSTEM,
  BASE_URL_POINTS_MALL,
  BASE_URL_SCAN_MARKETING,
  BASE_URL_FANS_CLUB,
  BASE_URL_FENGTAN_WINE,
  BASE_URL_JING_BRAND,
}

export const OSS_PREFIX = 'https://hishop-wine.obs.cn-south-1.myhuaweicloud.com/'
