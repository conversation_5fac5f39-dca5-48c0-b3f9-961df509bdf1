{"dependencies": {"crypto-js": "^4.1.1", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^5.0.0", "html2canvas": "^1.4.1", "js-base64": "^3.7.5", "konva": "^9.3.3", "nanoid": "^4.0.2", "qs": "^6.9.1", "vconsole": "^3.6.1", "vue-konva": "^2.1.7", "vuex": "^3.1.2"}, "devDependencies": {"@leekoho/prettier-config": "^1.0.1", "@vue/eslint-config-prettier": "^9.0.0", "babel-eslint": "^10.1.0", "eslint": "^8.56.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-vue": "^9.19.2", "prettier": "^1.18.0"}, "prettier": "@leekoho/prettier-config"}