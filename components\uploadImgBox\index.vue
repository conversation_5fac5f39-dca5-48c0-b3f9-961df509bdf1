<template>
	<view>
		<view class="images">
			<button  @click="chooseFile"></button>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import {
		baseUrl
	} from '@/config/system'
	export default {
		components: {},
		props: {
			maxLen: {
				type: Number,
				default: 1
			},
			defaultImg: {
				type: Array,
				default: () => []
			},
			//1图片 2视频 3图片视频
			type: {
				type: Number,
				default: 3
			}
		},
		data() {
			return {
				imgList: []
			}
		},
		computed: {
			...mapState(['setting', 'theme'])
		},
		watch: {
			defaultImg(value) {
				// this.show = value
				// this.$emit('update:display', value)
				console.log("监听到默认数组变化：", value);
			},
			imgList(val){
				// console.log("new,old: ",new,old)
				 this.$emit('input', val)
				console.log("图片列表：~~~~~~~~~~~~~~~~~~~",val);
				
				// this.$emit('update:display', value)
			}
		},
		methods: {
	 
			chooseFile() {
				uni.chooseMedia({
					count: this.maxLen,
					mediaType: ['image', 'video'],
					sourceType: ['album', 'camera'],
					camera: 'back',
					success: (res) => {
						console.log("获取到的文件：", res)
						res.tempFiles.forEach((item, index) => {
							this.uploadFile(item.tempFilePath,item.fileType)
						})
					}
				})
			},
			uploadFile(tempFilePath,fileType) {
				uni.showLoading()
				let domain = baseUrl['BASE_URL_BASIC_SYSTEM']
				const token = uni.getStorageSync('token') || ''
				let that=this;
				uni.uploadFile({
					url: domain + 'wine/nfs/upload',
					filePath: tempFilePath,
					name: 'file',
					header: {
						Authorization: token
					},
					success: (fileRes) => {
						uni.hideLoading()
						console.log("fileRes: ", fileRes)
						const fileUrl = JSON.parse(fileRes.data).data;
						that.imgList.push(fileUrl);
						console.log("imgList: ",that.imgList)
					},
					fail: () => {
						uni.showToast({
							title: '上传失败',
							icon: 'success'
						})
					}
				})
			},
			handleClose() {
				this.show = false
			},
			handleOk() {
				this.$checkLogin(() => {
					uni.navigateTo({
						// url: `/pointsMall/order/submit?relateId=${this.relateId}&orderType=${this.type}&quantity=${this.quantity}`
						url: `/wineJar/wineGoods/submit`
					})
					this.show = false
				})
			}
		},
		created() {}
	}
</script>

<style lang="scss" scoped>
	.images {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		.image-item,
		button {
			position: relative;
			width: 186rpx;
			padding: 186rpx 0 0;
			margin-bottom: 32rpx;
			margin-right: 32rpx;
			overflow: hidden;
			border-radius: 8rpx;
			margin-left: 0;

			&:nth-child(3n) {
				margin-right: 0;
			}

			image,
			video {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}

			.iconfont {
				position: absolute;
				top: 0;
				right: 0;
				display: block;
				width: 40rpx;
				height: 40rpx;
				background: rgba(0, 0, 0, 0.3);
				border-radius: 0 8rpx 0 8rpx;
				color: #ffffff;
				font-size: 28rpx;
				font-weight: 600;
				line-height: 40rpx;
				text-align: center;
			}
		}

		button {
			border-radius: 4rpx 4rpx 4rpx 4rpx;
			border: 1rpx solid #d9d9d9;
			background-color: #fff;

			&::after {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
				width: 40rpx;
				height: 4rpx;
				background: #d9d9d9;
				content: '';
			}

			&::before {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
				width: 4rpx;
				height: 40rpx;
				background: #d9d9d9;
				content: '';
			}
		}
	}
</style>