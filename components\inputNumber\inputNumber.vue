<template>
	<input class="input" :focus="focus" @blur="handleBlur" :type="digit === 0 ? 'number' : 'digit'" :value="number" :class="{ disabled, border }" :disabled="disabled" @input="handleInputChange" :placeholder="placeholder">
</template>

<script>
	export default {
		props: {
			placeholder: String,
			value: [String, Number],
			min: {
				type: Number,
				default: 0
			},
			max: {
				type: Number,
				default: 9999999
			},
			disabled: {
				type: Boolean,
				default: false
			},
			digit: {
				type: Number,
				default: 2
			},
			border: {
				type: Boolean,
				default: false
			},
			focus: {
				type: Boolean,
				default: false
			}
			
		},
		watch: {
			value(val) {
				this.number = val
			},
			number(val){
				this.$emit('input', val)
			}
		},
		data() {
			return {
				number: this.value
			}
		},
		methods:{
			handleInputChange(event) {
				let value = event.detail.value
				if (value === '0.' || value.split('.')[1] === '0' || (value.split('.').length === 2 && value.split('.')[1] === '')) {
					this.number = value
					return value
				}
				let reg
				if (this.digit === 2) {
					reg = value.match(/^(-)?\d*(\.?\d{0,2})/g)
				} else if (this.digit === 0) {
					reg = value.match(/^(-)?\d*/g)
				} else {
					reg = value.match(/^(-)?\d*(\.?\d{0,3})/g)
				}
				if (reg) {
					value = parseFloat(reg[0])
				}
				if(this.max && parseFloat(value) > parseFloat(this.max)) {
					value = this.max
				}
				
				// if (this.min && parseFloat(value) < parseFloat(this.min)) {
				// 	value = this.min
				// }
				value = !value && value !== 0 ? '' : value
				// #ifdef MP
				this.number = value
				// #endif
				
				// #ifndef MP
				this.number = 0
				this.$nextTick(() => {
					this.number = value
				})
				// #endif
				
				return value
			},
			handleBlur(event) {
				const value = event.detail.value
				if (this.min && parseFloat(value) < parseFloat(this.min)) {
					this.number = this.min
				}
				this.$emit('blur')
				return this.number
			}
		}
	}
</script>

<style lang="scss" scoped>
	.input {
		&.disabled {
			color: #999;
		}
		&.border::after {
			border-radius: 16rpx;
			z-index: -1;
		}
	}
	
</style>
