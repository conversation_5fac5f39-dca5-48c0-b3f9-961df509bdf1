// Description: 扫码活动
import request from '@/config/request'

// 获取活动详情
export const GetActivityDetail = (data) => request({ url: 'scanMarketing/mini/activity/detail', data })
// 获取活动启动页
export const GetActivityStartPage = (data) => request({ url: 'scanMarketing/mini/activity/startPage', data, showLoading: false })
// 活动开奖
export const ActivityDraw = (data) => request({ url: 'scanMarketing/mini/activity/draw', data, method: 'post', showLoading: false })
// 收集用户位置信息
export const CollectLocation = (data) => request({ url: 'scanMarketing/mini/activity/collectLocation', data, method: 'post', showLoading: false })
