<template>
  <view
    :style="{
      paddingTop: page.header && page.header.enableBar === false ? 0 : statusBarHeight + 'rpx',
    }"
  >
    <custom-nav v-if="page.header" :data="page.header" @init="statusBarHeight = $event" />
    <slot :data="page"></slot>
    <diy v-if="diyUrl" :url="diyUrl" method="get" :params="diyParams" @load="loadDiy" />
  </view>
</template>

<script>
import Diy from '@/components/diy/index'
import CustomNav from '@/components/customNav/customNav'
import tabBarCheck from '@/hooks/tabBarCheck'
import { appId } from '@/config/system'

export default {
  components: {
    Diy,
    CustomNav,
  },
  props: {
    type: {
      type: String,
      default: '',
    },
    path: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      page: {},
      diyUrl: '',
      diyParams: {
        appId,
        moduleCode: '',
        decorateType: 'homePage',
      },
      statusBarHeight: 128,
    }
  },
  methods: {
    loadData() {
      this.diyParams.moduleCode = this.type
      this.diyUrl = 'wine/micropage/getHomePage'

      if (this.page.header) {
        uni.setNavigationBarColor({
          frontColor: this.page.header.barType ? '#ffffff' : '#000000',
          backgroundColor: '#000',
        })
      }

      uni.stopPullDownRefresh()
      this.$emit('load')
      console.log('loadData diyParams: ', this.diyParams)
    },
    refresh() {
      this.diyUrl = ''
      this.$nextTick(() => {
        this.loadData()
      })
    },
    loadDiy(page) {
      console.log('loadDiy-------，', page)
      this.page = page
    },
  },
  mounted() {
    if (tabBarCheck(this.path)) {
      this.loadData()
    }
  },
}
</script>

<style lang="scss" scoped></style>
