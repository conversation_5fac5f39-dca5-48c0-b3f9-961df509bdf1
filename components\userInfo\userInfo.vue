<template>
  <view>
    <uni-popup ref="popup" type="bottom">
      <view class="popup">
        <view class="app-row">
          <image :src="setting.webLogo | formatUrl" />
          <text>{{ setting.webName }}</text>
        </view>
        <view class="title">获取你的昵称、头像</view>
        <view class="cell avatar">
          <view class="cell-title">头像</view>
          <button
            class="avatar-btn cell-content"
            open-type="chooseAvatar"
            @chooseavatar="getAvatar"
          >
            <image
              class="avatar"
              :src="
                userData.avatarUrl
                  ? userData.avatarUrl
                  : 'static/applet/default-avatar.png' | formatUrl(96)
              "
              mode="aspectFill"
            ></image>
          </button>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        <view class="cell">
          <view class="cell-title">昵称</view>
          <input
            class="cell-content"
            @change="getNickname"
            v-model="userData.nickName"
            type="nickname"
            placeholder="点击填写"
          />
        </view>
        <view class="btns">
          <view class="btn" @click="handleCancel">取消</view>
          <view
            class="btn primary"
            :style="{
              backgroundColor: theme.primary.color6,
              color: theme.primaryTextColor,
            }"
            @click="handleConfirm"
            >确定</view
          >
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapState } from "vuex";
import UniPopup from "@/components/uni-popup/uni-popup";
import { SaveUserData } from "@/service/common";
import { baseUrl } from "@/config/system";
export default {
  components: {
    UniPopup,
  },
  props: {},
  data() {
    return {
      userData: {
        avatarUrl: "",
        nickName: "",
      },
    };
  },
  computed: {
    ...mapState(["userInfo", "theme", "setting"]),
  },
  mounted() {},
  watch: {},
  methods: {
    getAvatar(e) {
      this.upload(e.detail.avatarUrl);
    },
    getNickname({ detail }) {
      this.userData.nickName = detail.value;
    },
    upload(filePath) {
      let domain = baseUrl["BASE_URL_BASIC_SYSTEM"];
      uni.uploadFile({
        url: domain + "wine/nfs/upload",
        filePath,
        name: "file",
        header: {
          Authorization: uni.getStorageSync("token"),
        },
        success: (res) => {
          this.userData.avatarUrl = JSON.parse(res.data).data;
        },
      });
    },
    handleOpen() {
      this.$refs.popup.open();
      this.userData.avatarUrl = this.userInfo.avatarUrl;
      this.userData.nickName = this.userInfo.nickName;
    },
    handleCancel() {
      this.$refs.popup.close();
    },
    handleConfirm() {
      SaveUserData(this.userData).then((res) => {
        this.userInfo.avatarUrl = this.userData.avatarUrl;
        this.userInfo.nickName = this.userData.nickName;
        let userData = uni.getStorageSync("userInfo");
        userData.avatarUrl = this.userData.avatarUrl;
        userData.nickName = this.userData.nickName;
        uni.setStorageSync("userInfo", userData);
        this.$refs.popup.close();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.popup {
  width: 100%;
  height: calc(env(safe-area-inset-bottom) + 646rpx);
  background-color: #fff;
  padding: 32rpx 32rpx 0 32rpx;
  border-radius: 12rpx 12rpx 0 0;
  box-sizing: border-box;
  position: absolute;
  bottom: 0;
  left: 0;
  overflow: hidden;
  .app-row {
    font-size: 32rpx;
    font-weight: 500;
    color: #000000;
    line-height: 48rpx;
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;
    image {
      width: 64rpx;
      height: 64rpx;
      margin-right: 16rpx;
    }
  }
  .title {
    font-size: 28rpx;
    color: #000;
    font-weight: 500;
    line-height: 40rpx;
    margin-bottom: 32rpx;
  }
  .cell {
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #f0f0f0;
    padding: 20rpx 0;
    color: #000;
    .iconfont {
      flex-shrink: 0;
      font-size: 28rpx;
    }
  }
  .avatar-btn {
    background: none;
    margin: 0;
    padding: 0;
    &::after {
      border: none;
    }
    image {
      width: 64rpx;
      height: 64rpx;
      border-radius: 40px;
    }
  }
  .cell-title {
    flex-shrink: 0;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #000;
    margin-right: 32rpx;
  }
  .cell-content {
    flex-shrink: 1;
    width: 100%;
    height: 64rpx;
    line-height: 64rpx;
    text-align: left;
  }
}
.btns {
  display: flex;
  margin-top: 118rpx;
  padding: 16rpx 0 24rpx;
  .btn {
    flex: 1;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    border-radius: 44rpx;
    font-size: 28rpx;
    color: #000;
    border: 1rpx solid #d9d9d9;
    &.primary {
      background-color: #ff4d4f;
      color: #fff;
      margin-left: 16rpx;
      border: none;
    }
  }
}
</style>
