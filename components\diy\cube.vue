<template>
  <view :style="{ backgroundColor: dataset.bgColor, padding: `${dataset.verticalMargin * 2}rpx ${dataset.pageMargin * 2}rpx` }">
    <view class="image-wrap" :style="{ margin: `-${dataset.imgMargin}rpx`, height: dataset.showType < 4 ? dataset.images[0] && (dataset.images[0].height * (750 - dataset.pageMargin * 4)) / 320 + 'rpx' : 750 - dataset.pageMargin * 4 + 'rpx' }">
      <view v-for="(item, index) in dataset.images" :key="index">
        <view class="item" @click="skip(item.link)" v-if="item" :style="{ margin: dataset.imgMargin + 'rpx', width: (item.width * (750 - dataset.pageMargin * 4)) / 320 - dataset.imgMargin * 2 + (item.width / 320) * dataset.imgMargin * 2 + 'rpx', height: (item.height * (750 - dataset.pageMargin * 4)) / 320 - dataset.imgMargin * 2 + 'rpx', top: (item.top * (750 - dataset.pageMargin * 4)) / 320 + 'rpx', left: (item.left * (750 - dataset.pageMargin * 4)) / 320 + (item.left / 320) * dataset.imgMargin * 2 + 'rpx' }">
          <image v-if="item.imgSrc" :src="item.imgSrc | formatUrl(parseInt(Math.max(item.width, item.height) * 2.5))" mode="aspectFill" />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    dataset: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    skip(link) {
      if (!link) {
        return
      }
      this.$navigateTo({
        ...link
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.image-wrap {
  position: relative;
  .item {
    position: absolute;
    image {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
