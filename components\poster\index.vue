<template>
	<view>
		<canvas :canvas-id="canvasId" :style="{width: (poster.width||0) + 'px', height: (poster.height||0) + 'px'}"></canvas>
	</view>
</template>

<script>
	import { getSharePoster } from './poster'
	
	export default {
		props: {
			height: {
				type: Number,
				default: 1921
			},
			product: {
				type: Object,
				default: null
			},
			// 是否开启缓存，false每次重绘
			cached: {
				type: Boolean,
				default: true
			},
			canvasId: {
				type: String,
				default: 'posterCanvasId'
			}
		},
		data() {
			return {
				poster: {
					path: ''
				}
			}
		},
		methods: {
			async draw(drawArray) {
				if (this.poster.path && this.cached) {
					uni.previewImage({
						urls: [this.poster.path]
					})
					return uni.showToast({
						icon: 'none',
						title: '长按图片保存至相册'
					})
				}
				
				let drawDelayTime = 1000
				//#ifdef APP-PLUS
				drawDelayTime = 1500
				//#endif
				const canvas = await getSharePoster({
					_this: this,
					posterCanvasId: this.canvasId,
					drawDelayTime,
					background: {
						width: 1080,
						height: this.height,
						backgroundColor: '#fff'
					},
					drawArray: ({
						bgObj,
						type,
						bgScale
					}) => {
						return drawArray
					},
					
					setCanvasWH: ({
						bgObj,
						type,
						bgScale
					}) => {
						this.poster = bgObj
					},
				})
				this.poster.path = canvas.poster.tempFilePath
				uni.previewImage({
					urls: [this.poster.path]
				})
				uni.showToast({
					icon: 'none',
					title: '长按图片保存至相册'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	canvas{
		position: fixed;
		left: -99999rpx;
		top: 0;
		z-index: -99999;
	}
</style>
