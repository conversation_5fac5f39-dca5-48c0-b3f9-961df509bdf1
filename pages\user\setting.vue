<template>
  <view class="container-root">
    <view class="content">
      <view class="rows">
        <view class="avatar" @click="chooseFile">
          <image
            class="image"
            :src="
              (userInfo.avatarUrl ||
                'hishop/upload/vZZgAiUEXOPKa1imfu2je.png?x-image-process=image/resize,m_lfit,w_160')
                | formatUrl
            "
            mode="aspectFill"
          />
          <view class="action">
            <text class="iconfont icon-edit" />
          </view>
        </view>

        <view class="flex-row border-b" @click="$refs.dialog.open()">
          <view class="cell-title">昵称</view>
          <text>
            {{ userInfo.nickName || '--' }}
            <text class="iconfont icon-edit" />
          </text>
        </view>
        <view class="flex-row border-b">
          <view class="cell-title">手机号</view>
          <text>{{ userInfo.mobile }}</text>
        </view>
        <view class="flex-row border-b" @click="handleOpen('/pages/user/agreement?type=1')">
          <view class="cell-title">用户协议</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        <view class="flex-row border-b" @click="handleOpen('/pages/user/agreement?type=2')">
          <view class="cell-title">隐私协议</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        <view class="flex-row border-b" @click="handleOpen('/pages/user/agreement?type=3')">
          <view class="cell-title">赠酒免责条款</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        <view class="flex-row border-b" @click="handleOpen('/pages/user/authorization')">
          <view class="cell-title">授权管理</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        <view class="flex-row border-b" @click="handleOpen('/pages/user/info')">
          <view class="cell-title">个人信息收集清单</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
        <view class="flex-row border-b" @click="handleDeleteAccount">
          <view class="cell-title">注销账号</view>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="fake-btn" @click="handleLogOut">退出当前账号</view>
    </view>

    <uni-popup ref="dialog" type="dialog" is-mask-click>
      <div class="dialog">
        <p class="title">修改昵称</p>

        <input v-model="nickName" type="text" class="input" placeholder="请输入昵称" />

        <div class="actions">
          <button class="btn" @click="cancel">取消</button>
          <button class="btn btn-primary" @click="confirm">确定</button>
        </div>
      </div>
    </uni-popup>
  </view>
</template>

<script>
import store from '@/store'
import { SET_ISLOGIN } from '@/store/mutation-types'
import { mapActions, mapState } from 'vuex'
import { SaveUserData, CancelAccount } from '@/service/common'
import { baseUrl } from '@/config/system'

export default {
  components: {},
  data() {
    return {
      avatarUrl: '',
      nickName: '',
    }
  },
  computed: {
    ...mapState(['userInfo']),
  },

  methods: {
    ...mapActions(['setUserInfo']),
    handleOpen(url) {
      uni.navigateTo({
        url,
      })
    },
    handleLogOut() {
      uni.showModal({
        title: '提示',
        content: '确定退出当前账号吗？',
        success: res => {
          if (res.confirm) {
            store.commit(SET_ISLOGIN, false)
            uni.removeStorageSync('userInfo')
            this.setUserInfo({})
            uni.removeStorageSync('token')
            uni.navigateBack({
              delta: 1,
            })
          }
        },
      })
    },
    handleDeleteAccount() {
      uni.showModal({
        title: '提示',
        content:
          '注销后，您将无法使用当前账号相关数据(藏酒、取酒、转增)也将无法查看，是否确认注销?',
        success: res => {
          if (res.confirm) {
            CancelAccount().then(res => {
              if (res.code !== '200') {
                return uni.showModal({
                  title: '提示',
                  content: res.msg,
                  showCancel: false,
                })
              }
              uni.showToast({
                title: '注销成功',
                icon: 'success',
              })
              uni.removeStorageSync('userInfo')
              this.setUserInfo({})
              uni.removeStorageSync('token')
              uni.navigateBack({
                delta: 1,
              })
            })
          }
        },
      })
    },

    close() {
      this.$refs.dialog.close()
    },

    cancel() {
      this.nickName = ''
      this.close()
    },

    confirm() {
      if (!this.nickName) {
        return uni.showToast({
          title: '请输入昵称',
          icon: 'none',
        })
      }
      SaveUserData({
        avatarUrl: this.userInfo.avatarUrl,
        nickName: this.nickName || this.userInfo.nickName,
      }).then(() => {
        uni.showToast({
          title: '更新成功',
        })
        this.setUserInfo({
          ...this.userInfo,
          nickName: this.nickName,
        })
        uni.setStorageSync('userInfo', this.userInfo)
        this.close()
      })
    },

    chooseFile() {
      uni.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        camera: 'back',
        success: async res => {
          // res.tempFiles.forEach(item => {
          //   this.uploadFile(item.tempFilePath, item.fileType)
          // })
          if (!res.tempFiles.length) return
          const item = res.tempFiles[0]
          const url = await this.uploadFile(item.tempFilePath)
          console.log('文件上传成功', url)
          await SaveUserData({
            avatarUrl: url,
            nickName: this.userInfo.nickName,
          })
          uni.showToast({
            title: '更新成功',
          })
          this.setUserInfo({
            ...this.userInfo,
            avatarUrl: url,
          })
          uni.setStorageSync('userInfo', this.userInfo)
        },
      })
    },

    uploadFile(tempFilePath) {
      return new Promise((resolve, reject) => {
        uni.showLoading()
        let domain = baseUrl['BASE_URL_BASIC_SYSTEM']
        const token = uni.getStorageSync('token') || ''
        uni.uploadFile({
          url: domain + 'wine/nfs/upload',
          filePath: tempFilePath,
          name: 'file',
          header: {
            Authorization: token,
          },
          success: fileRes => {
            uni.hideLoading()
            console.log('fileRes: ', fileRes)
            resolve(JSON.parse(fileRes.data).data)
          },
          fail: err => {
            uni.showToast({
              title: '上传失败',
              icon: 'success',
            })
            reject(err)
          },
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 24rpx;

  .title {
    text-align: center;
    font-size: 36rpx;
    font-weight: 600;
  }

  .input {
    width: 480rpx;
    margin: 24rpx auto 40rpx;
    padding: 16rpx 24rpx;
    background-color: #f5f5f5;
    border-radius: 4rpx;
  }

  .actions {
    display: flex;
    gap: 16rpx;
  }

  .btn {
    flex: 1;
    margin: 0;
  }

  .btn-primary {
    background-color: #0b7d83;
  }
}

.avatar {
  position: relative;
  display: block;
  width: 160rpx;
  height: 160rpx;
  margin: 40rpx auto 24rpx;
  border-radius: 50%;
  overflow: hidden;
  .image {
    width: 100%;
    height: 100%;
  }

  .action {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 40rpx;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.64);

    .iconfont {
      color: #fff;
    }
  }
}

.content {
  padding: 24rpx;
}

.rows {
  background-color: #fff;
  padding: 0 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.flex-row {
  display: flex;
  padding: 32rpx 0;
  position: relative;
  align-items: center;
}
.cell-title {
  flex: 1;
  color: #000;
  line-height: 40rpx;
  font-size: 28rpx;
}
.iconfont {
  color: #8c8c8c;
}
.icon-arrow-right {
  font-size: 20rpx;
}
.icon-edit {
  font-size: 28rpx;
}
.fake-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #fff;
  text-align: center;
  font-size: 28rpx;
  border-radius: 16rpx;
  margin-top: 48rpx;
  color: #000000;
}
</style>
