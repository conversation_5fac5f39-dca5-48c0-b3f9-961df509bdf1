/**
 * @description: 格式化图片地址-OBS
 * @param {String} path 图片地址
 * @param {Number} width 图片宽度
 * @return {String} 图片地址
 **/
import store from '@/store'

const formatUrl = (path, width) => {
  const obsUrl = store.state.obsUrl
  if (!obsUrl) return
  if (!path || /^https/.test(path)) {
    return path
  }
  let url = `${obsUrl}${path}`
  if (width) {
    url += `?x-image-process=image/resize,m_lfit,w_${width}`
  }
  return url
}

export default formatUrl
