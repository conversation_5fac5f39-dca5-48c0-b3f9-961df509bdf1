// Description: 商品等相关
import request from '@/config/request'
// 提交订单
export const SubmitWineOrder = (data) => request({ url: 'fengtanWine/miniOrder/createOrder', data, method: 'post' })

// 确认订单
export const ConfirmOrder = (data) => request({ url: 'fengtanWine/miniOrder/confirmOrder', data, method: 'post' })

// 提交凭证
export const SubmitVoucher = (data) => request({ url: 'fengtanWine/miniOrder/submitOrderVoucher', data, method: 'post' })

// 订单列表
export const OrderList = (data) => request({ url: 'fengtanWine/miniOrder/pageList', data, method: 'post' })

// 订单详情
export const OrderDetail = (data) => request({ url: 'fengtanWine/miniOrder/detail', data })

// 订单详情
export const OrderPay = (data) => request({ url: 'fengtanWine/miniOrder/pay', data, method: 'post' })

// 修改收货地址
export const ModifyAddress = (data) => request({ url: 'fengtanWine/miniOrder/updateDeliveryAddress', data, method: 'post' })

// 修改封坛信息
export const ModifyFengTan = (data) => request({ url: 'fengtanWine/miniOrder/updateFengtanInfo', data, method: 'post' })

// 关闭订单
export const CloseOrder = (data) => request({ url: 'fengtanWine/miniOrder/closeOrder', data, method: 'post' })

// 删除订单
export const DelOrder = (data) => request({ url: 'fengtanWine/miniOrder/deleteOrder', data, method: 'post' })

// 确认收货
export const SureTake = (data) => request({ url: 'fengtanWine/miniOrder/confirmReceipt', data, method: 'post' })

// -------------------------------取酒订单---------------------------------

// 确认取酒订单
export const SureTakeOrder = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/confirmOrder', data, method: 'post' })


// 提交取酒订单
export const SubmitTakeWineOrder = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/createOrder', data, method: 'post' })

// 取酒订单发起支付
export const PayTakeWineOrder = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/payTakeWineOrder', data, method: 'post' })

// 取酒订单列表
export const TakeOrderList = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/pageList', data, method: 'post' })

// 取酒订单详情
export const TakeOrderDetail = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/detail', data })

// 关闭取酒订单
export const CloseTakeOrder = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/closeOrder', data, method: 'post' })
// 删除取酒订单
export const DeleteTakeOrder = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/deleteOrder', data, method: 'post' })
// 确认收货取酒订单
export const ConfirmReceiptTakeOrder = (data) => request({ url: 'fengtanWine/miniTakeWineOrder/confirmReceipt', data, method: 'post' })
// 修改封坛信息
export const UpdataSealing = (data) => request({ url: 'fengtanWine/miniOrder/updateFengtanInfo', data, method: 'post' })

// 修改封坛收货地址
export const UpdataSealingAddress = (data) => request({ url: 'fengtanWine/miniOrder/updateDeliveryAddress', data, method: 'post' })
