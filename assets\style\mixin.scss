@mixin overflow-ellipsis(){
	overflow:hidden;
	white-space:nowrap;
	text-overflow:ellipsis;
}

// 多行省略
@mixin multiline-overflow($line: 2) {
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
}

@mixin border-line($top, $right, $bottom, $left, $color: $colorborder) {
	content:'';
	position:absolute;
	top:$top;
	right:$right;
	bottom:$bottom;
	left:$left;
	background:$color;
	@if $top==auto or $bottom==auto {
		height: 1px;
		transform: scaleY(.5);
	}

	@else if $left==auto or $right==auto {
		width: 1px;
		transform: scaleX(.5);
	}
}

/**
 * cell
 */

@mixin cell-list-item($size, $bgcolor: #fff){
	display:flex;
	padding-left:24rpx;
	padding-right:24rpx;
	justify-content:space-between;
	background-color:$bgcolor;
	line-height: 40rpx;
	
	&:after{
		@include border-line(auto, 0, 0, 24rpx);
	}
	
	@if $size == big {
		padding-top:32rpx;
		padding-bottom:32rpx;
	} @else {
		padding-top:24rpx;
		padding-bottom:24rpx;
	}
	
	.aside{
		display:flex;
		align-items:center;
	}
	
	.main{
		flex:1;
		padding:0 24rpx;
		overflow:hidden;
	}
	
	.other{
		display:flex;
		align-items:center;
		justify-content:flex-end;
		.iconfont{
			@if $size == big {
				font-size:48rpx;
			} @else {
				font-size:24rpx;
			}
			color:$colortext;
			margin-left: 8rpx;
		}
	}
}

/**
 * 三角箭頭
 */

@mixin triangle($direction: left, $color : #ddd, $x: 16rpx, $y: 8rpx) {
	width:0;
	height:0;
	display:inline-block;
	box-sizing: border-box;
	overflow:hidden;
	@if ($direction == right) {
		border-top:$y  solid transparent;
		border-bottom: $y solid transparent;
		border-left:$x solid $color;
	} @else if($direction == top) {
		border-bottom: $y solid $color;
		border-right:$x solid transparent;
		border-left:$x solid transparent;
	} @else if ($direction == bottom) {
		border-top: $y solid $color;
		border-right:$x solid transparent;
		border-left:$x solid transparent;
	} @else {
		border-top: $y solid transparent;
		border-bottom: $y solid transparent;
		border-right:$x solid $color;
	}
}